import { useMutation } from '@tanstack/react-query';
import { useShowError, useShowSuccess } from '@/hooks';
import { AxiosResponseError, Http } from '@/config/http';
import { IInvoicePayload } from '@/interfaces';

export const useCreateInvoice = () => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();

  const {
    mutate: create,
    isPending: isCreating,
    ...rest
  } = useMutation({
    mutationKey: ['create_invoices'],
    mutationFn: (payload: IInvoicePayload) => Http.post('invoices', payload),
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Factura creada.' });
    },
  });

  return { create, isCreating, ...rest };
};
