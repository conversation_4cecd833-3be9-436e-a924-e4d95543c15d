import { useField } from 'formik';
import { <PERSON><PERSON>, Key, ListBox, Popover, Select, SelectValue } from 'react-aria-components';
import { LuChevronsUpDown } from 'react-icons/lu';

import { ItemEPSDT } from '@/enums';
import { StatusItem } from '@/modules/providers/create/components/provider-select-type/status-item';
import { getItem24H } from '@/tools/getItem24h';

const PLAN = [
  ItemEPSDT.Available,
  ItemEPSDT.NewServiceRequested,
  ItemEPSDT.NotUsed,
  ItemEPSDT.UnderTreatment,
] as const;

interface IPLanOption {
  plan: ItemEPSDT;
}

interface IItem24H {
  className?: HTMLElement['className'];
  disabled?: boolean;
  name: string;
  value?: ItemEPSDT;
  onChange?: (value: ItemEPSDT) => void;
}

const Options = ({ plan }: IPLanOption) => (
  <StatusItem key={plan} id={plan}>
    <div className="flex gap-2 items-baseline">
      <span>{plan}</span>
    </div>
  </StatusItem>
);

export function SelectItem24H({ className, disabled, name, value, onChange }: IItem24H) {
  const [field, meta, helpers] = useField<ItemEPSDT>(name);

  const handleSelectionChange = (key: Key) => {
    const selectedValue = key as ItemEPSDT;
    helpers.setValue(selectedValue);
    if (onChange) onChange(selectedValue);
  };

  return (
    <div className={className}>
      <Select
        aria-label="EPSDT Family Plan"
        className="flex flex-col gap-1 w-full"
        selectedKey={field.value}
        onSelectionChange={handleSelectionChange}
        defaultSelectedKey={undefined}
        placeholder="select"
        isDisabled={disabled}
      >
        <Button className="field-shadow h-4">
          <SelectValue className="flex-1 truncate placeholder-shown:italic">
            {role => getItem24H(role as unknown as ItemEPSDT)}
          </SelectValue>
          <LuChevronsUpDown />
        </Button>

        <Popover className="max-h-60 w-[--trigger-width] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out">
          <ListBox className="outline-none p-1">
            {PLAN.map(plan => (
              <Options key={plan} plan={plan} />
            ))}
          </ListBox>
        </Popover>
      </Select>

      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
