import { FormField } from '@/components';

export function Items09({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 py-4 border-t-transparent border-r-transparent hover-card">
      <div className="flex gap-4 my-2 ml-4 ">
        <strong>9.</strong>
        <FormField
          label="OTHER INSURED'S NAME (Last Name, First Name, Middle Initial)"
          name="otherInsuredName"
          disabled={isLoading}
        />
      </div>
      <div className="flex gap-4 my-2 ml-4">
        <strong>a.</strong>
        <FormField
          label="OTHER INSURED'S POLICY OR GROUP NUMBER"
          name="insured.policyGroupNumber"
          disabled={isLoading}
        />
      </div>
      <div className="flex gap-4 my-2 ml-4">
        <strong>b.</strong>
        <FormField
          label="RESERVED FOR NUCC USE"
          name=" "
          className="border-none bg-transparent"
          disabled
        />
      </div>
      <div className="flex gap-4 my-2 ml-4">
        <strong>c.</strong>
        <FormField
          label="RESERVED FOR NUCC USE"
          name=" "
          className="border-none bg-transparent"
          disabled
        />
      </div>
      <div className="flex gap-4 my-2 ml-4">
        <strong>d.</strong>
        <FormField
          label="INSURANCE PLAN NAME OR PROGRAM NAME"
          name="otherInsuredPlanNameOrProgramName"
          disabled={isLoading}
        />
      </div>
    </section>
  );
}
