'use client';

import { Button } from '@digheontech/digh.ui';
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-not-found-bg bg-no-repeat bg-center bg-cover flex flex-col items-center justify-center p-4 bg-slate-100">
      <div className="text-center max-w-xl mx-auto">
        <div className="relative mb-8">
          <div className="text-9xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#15803D] via-[#8ABD29] to-[#FFF915]">
            404
          </div>
        </div>
        <h1 className="text-3xl font-bold mb-4 text-center text-green-700">
          Error paginan no encontrada
        </h1>
        <p className="text-gray-600 mb-8">Lo sentimos no pudimos encontrar esta pagina</p>
        <Link href="/" className="inline-flex items-center">
          <Button primary size="large">
            Volver al inicio
          </Button>
        </Link>
      </div>
    </div>
  );
}
