import { Reflector } from '@nestjs/core';
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';

import { User } from '../domain/user.model';

import { REQUESTED_ROLES } from './roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.get(REQUESTED_ROLES, context.getHandler());

    const request = context.switchToHttp().getRequest();
    const user = request.user as User;

    return RolesGuard.matchAnyRole(roles, user);
  }

  static matchAnyRole(requestedRoles: string[], user: User): boolean {
    return requestedRoles.some(role => user.role === role);
  }
}
