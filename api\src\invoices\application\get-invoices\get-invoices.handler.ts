import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';

import { InvoiceRepository } from '../../infrastructure/repositories/invoice.repository';
import { GetInvoicesQuery } from './get-invoices.query';

@QueryHandler(GetInvoicesQuery)
export class GetInvoicesHandler implements IQueryHandler<GetInvoicesQuery> {
  constructor(private readonly invoiceRepository: InvoiceRepository) {}

  async execute(query: GetInvoicesQuery) {
    return this.invoiceRepository.search(query);
  }
}
