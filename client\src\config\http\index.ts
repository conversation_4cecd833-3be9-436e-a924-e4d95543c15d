'use client';

import axios, { AxiosError, AxiosInstance, HttpStatusCode, isAxiosError } from 'axios';
import { api, mockoon } from '@/config';

export interface ApiError {
  name: Record<string, string>;
  code: string | number;
  message: string;
  request: {
    url: string;
  };
}

export type AxiosResponseError = AxiosError<ApiError>;

export const Http: AxiosInstance = axios.create({ baseURL: api });

export const PublicHttp: AxiosInstance = axios.create({ baseURL: api });

export const HttpMockoon: AxiosInstance = axios.create({ baseURL: mockoon });

export const isDomainBackendError = <T = any, D = any>(error: unknown): error is AxiosError<T, D> =>
  isAxiosError(error) && error.response?.status === HttpStatusCode.UnprocessableEntity;

Http.interceptors.response.use(
  response => response,
  (error: AxiosResponseError) => {
    if (isAxiosError(error)) {
      const requestUrl = error.config?.url ?? 'URL desconocida';
      console.error(`Error en petición a ${requestUrl}:`, error);
    }
    return Promise.reject(error);
  },
);

PublicHttp.interceptors.response.use(
  response => response,
  (error: AxiosResponseError) => {
    if (isAxiosError(error)) {
      const requestUrl = error.config?.url ?? 'URL desconocida';
      console.error(`Error en petición pública a ${requestUrl}:`, error);
    }
    return Promise.reject(error);
  },
);

HttpMockoon.interceptors.response.use(
  response => response,
  (error: AxiosResponseError) => {
    if (isAxiosError(error)) {
      const requestUrl = error.config?.url ?? 'URL desconocida';
      console.error(`Error en petición pública a ${requestUrl}:`, error);
    }
    return Promise.reject(error);
  },
);
