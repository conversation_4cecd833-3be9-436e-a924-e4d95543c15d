import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types, UpdateQuery } from 'mongoose';
import {
  InvoiceAdjustment,
  InvoiceAdjustmentDocument,
} from '../schema/invoice-adjustments.schema';

@Injectable()
export class InvoiceAdjustmentRepository {
  constructor(
    @InjectModel('InvoiceAdjustment')
    private invoiceModel: Model<InvoiceAdjustmentDocument>,
  ) {}

  async create(data: Partial<InvoiceAdjustment>) {
    return this.invoiceModel.create(data);
  }

  async findByInvoiceId(invoiceId: string) {
    const invoiceAdjustments = await this.invoiceModel
      .findOne({ originalInvoiceId: new Types.ObjectId(invoiceId) })
      .exec();
    return invoiceAdjustments;
  }

  async update(
    originalInvoiceId: string,
    data: UpdateQuery<InvoiceAdjustmentDocument>,
  ) {
    return this.invoiceModel
      .findOneAndUpdate(
        { originalInvoiceId: new Types.ObjectId(originalInvoiceId) },
        {
          $set: data,
          $setOnInsert: {
            originalInvoiceId: new Types.ObjectId(originalInvoiceId),
            createdAt: new Date(),
          },
        },
        { upsert: true, new: true },
      )
      .exec();
  }

  async removeByOriginalInvoiceId(originalInvoiceId: string) {
    return this.invoiceModel
      .deleteOne({ originalInvoiceId: new Types.ObjectId(originalInvoiceId) })
      .exec();
  }
}
