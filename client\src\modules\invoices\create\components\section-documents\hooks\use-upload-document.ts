import { useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { AxiosResponseError, Http } from '@/config/http';
import { useShowError, useShowSuccess } from '@/hooks';
import { useDocumentList } from './use-document-list';

interface UploadDocumentFormValues {
  file: File | null;
}

export function useUploadDocument(invoiceId: string) {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateFiles } = useDocumentList(invoiceId || '');

  const { mutateAsync } = useMutation({
    mutationKey: ['UPLOAD_DOCUMENT'],
    mutationFn: (formValues: UploadDocumentFormValues) => {
      const formData = new FormData();
      if (formValues.file) {
        formData.append('file', formValues.file);
      }
      return Http.post(`invoices/${invoiceId}/files`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
    },
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Documento subido.' });
      invalidateFiles();
    },
  });

  const onSubmit = useCallback(
    (values: UploadDocumentFormValues) => {
      mutateAsync(values);
    },
    [mutateAsync],
  );

  return { onSubmit };
}
