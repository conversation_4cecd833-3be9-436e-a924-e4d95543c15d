import { useState } from 'react';
import { ListBox, ListBoxItem } from 'react-aria-components';
import type { Selection } from 'react-stately';
import { LuX } from 'react-icons/lu';
import { useField, useFormikContext } from 'formik';
import { DenialReason } from '@/enums';
import { getDenialReasonLabel } from '@/tools/getDenialReasonLabel';

const DENIAL_REASONS = Object.values(DenialReason);

function useDenialReasonField(
  name: string,
  externalValue?: DenialReason[],
  externalOnChange?: (value: DenialReason[]) => void,
) {
  const formikContext = useFormikContext();
  const [localValue, setLocalValue] = useState<DenialReason[]>(externalValue || []);

  let formikField: any,
    formikMeta: any,
    formikHelpers: { setValue: (value: DenialReason[]) => void };
  try {
    [formikField, formikMeta, formikHelpers] = useField(name);
  } catch {
    formikField = null;
  }

  const isFormikMode = !!formikContext && !!formikField;

  const value = isFormikMode ? formikField?.value || [] : (externalValue ?? localValue);
  const error =
    isFormikMode && formikMeta?.touched && formikMeta?.error ? formikMeta.error : undefined;

  const setValue = (newValue: DenialReason[]) => {
    if (isFormikMode) {
      formikHelpers?.setValue(newValue);
    } else {
      setLocalValue(newValue);
      externalOnChange?.(newValue);
    }
  };

  return { value, error, setValue };
}

interface DenialReasonSelectProps {
  className?: string;
  disabled?: boolean;
  name?: string;
  value?: DenialReason[];
  onChange?: (value: DenialReason[]) => void;
  error?: string;
  placeholder?: string;
  multiple?: boolean;
  label?: string;
}

export function DenialReasonSelect({
  className,
  disabled,
  name = 'denialReason',
  value: externalValue,
  onChange: externalOnChange,
  error: externalError,
  placeholder = 'Seleccione motivo(s) de denegación',
  multiple = true,
  label = 'Motivos de Denegación',
}: DenialReasonSelectProps) {
  const {
    value,
    error: formikError,
    setValue,
  } = useDenialReasonField(name, externalValue, externalOnChange);

  const handleSelectionChange = (selection: Selection) => {
    if (selection === 'all') {
      setValue(DENIAL_REASONS);
    } else if (selection instanceof Set) {
      const selectedValues = Array.from(selection) as DenialReason[];
      setValue(selectedValues);
    } else {
      setValue([]);
    }
  };

  const removeItem = (itemToRemove: DenialReason) => {
    const newValue = value.filter((item: DenialReason) => item !== itemToRemove);
    setValue(newValue);
  };

  const displayError = externalError || formikError;

  const selectedKeys = new Set(value);

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>

      {value.length > 0 && (
        <div className="mb-3 p-2 bg-gray-50 rounded-md border">
          <div className="text-sm text-gray-600 mb-2">Seleccionados:</div>
          <div className="flex flex-wrap gap-1">
            {value.map((reason: DenialReason) => (
              <span
                key={reason}
                className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm"
              >
                {getDenialReasonLabel(reason)}
                {!disabled && (
                  <button
                    type="button"
                    onClick={() => removeItem(reason)}
                    className="hover:bg-blue-200 rounded-full p-0.5"
                  >
                    <LuX size={12} />
                  </button>
                )}
              </span>
            ))}
          </div>
        </div>
      )}

      <ListBox
        aria-label={label}
        className="field-shadow max-h-60 overflow-auto rounded-md bg-white border border-gray-300 p-1"
        selectionMode={multiple ? 'multiple' : 'single'}
        selectedKeys={selectedKeys as Iterable<string>}
        onSelectionChange={handleSelectionChange}
      >
        {DENIAL_REASONS.map(reason => (
          <ListBoxItem
            key={reason}
            id={reason}
            className="flex items-center gap-2 px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 focus:bg-blue-100 focus:outline-none rounded data-[selected]:bg-blue-50 data-[selected]:text-blue-900"
          >
            {multiple && (
              <input
                type="checkbox"
                checked={value.includes(reason)}
                onChange={() => {}}
                className="rounded"
                tabIndex={-1}
              />
            )}
            <span className="flex-1">{getDenialReasonLabel(reason)}</span>
            {value.includes(reason) && <span className="text-blue-600 font-medium">✓</span>}
          </ListBoxItem>
        ))}
      </ListBox>

      {value.length === 0 && <div className="text-gray-500 italic text-sm mt-2">{placeholder}</div>}

      {displayError && <div className="text-red-500 text-sm mt-1">{displayError}</div>}
    </div>
  );
}
