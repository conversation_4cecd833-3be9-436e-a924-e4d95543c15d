import type { Metadata } from 'next';

import localFont from 'next/font/local';

export const section = localFont({
  src: [
    { path: '../public/fonts/section-medium.otf', weight: '400', style: 'normal' },
    { path: '../public/fonts/section-mediumItalic.otf', weight: '400', style: 'italic' },
    { path: '../public/fonts/section-bold.otf', weight: '700', style: 'normal' },
    { path: '../public/fonts/section-light.otf', weight: '200', style: 'normal' },
  ],
  variable: '--font-section',
});

export const APP_HOME_PATH = '/';

export const metadata: Metadata = {
  title: 'Repo Base',
  description: 'Base repository, to be used in client applications, for Digheontech.',
};

export const LOGOUT_INVALID_TOKEN_ERROR = 'invalid_token';
