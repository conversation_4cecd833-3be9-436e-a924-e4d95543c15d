import { useMutation } from '@tanstack/react-query';
import { useShowError, useShowSuccess } from '@/hooks';
import { AxiosResponseError, Http } from '@/config/http';
import { IFormAdjudicatorValues } from '@/interfaces';
import { useGetUsers } from './use-get-users';

export const useCreateAdjudicator = () => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateUsers } = useGetUsers();

  const {
    mutate: create,
    isPending: isCreating,
    ...rest
  } = useMutation({
    mutationKey: ['create_adjudicator'],
    mutationFn: (payload: IFormAdjudicatorValues) => Http.post('users/adjudicator', payload),
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Adjudicador creado.' });
      invalidateUsers();
    },
  });

  return { create, isCreating, ...rest };
};
