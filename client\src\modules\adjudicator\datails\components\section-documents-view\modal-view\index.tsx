import { Dialog, DialogTrigger, Modal, ModalOverlay } from 'react-aria-components';
import { RiEyeLine } from 'react-icons/ri';
import { Button } from '@digheontech/digh.ui';
import { IDocument, useGetSignedDocument } from '../hooks';
import Image from 'next/image';

interface IModalView {
  doc: IDocument;
}

export function ModalView({ doc }: IModalView) {
  const isPdf = doc.fileType === 'application/pdf';
  const { data: signedUrl, isLoading } = useGetSignedDocument(doc.invoiceId, doc._id);

  return (
    <DialogTrigger>
      <Button
        leftIcon={<RiEyeLine size={20} />}
        buttonType="normal"
        className="rounded-full w-10 h-10 p-2 border-none hover:bg-blue-200"
      />
      <ModalOverlay
        className={({
          isEntering,
          isExiting,
        }) => `fixed inset-0 z-10 overflow-y-auto bg-black/25 flex min-h-full items-center justify-center p-4 text-center backdrop-blur
          ${isEntering ? 'animate-in fade-in duration-300 ease-out' : ''}
          ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}`}
      >
        <Modal
          className={({
            isEntering,
            isExiting,
          }) => `w-full max-w-4xl overflow-hidden rounded-2xl bg-white pt-7 pb-10 px-10 text-left align-middle shadow-xl relative
                ${isEntering ? 'animate-in zoom-in-95 ease-out duration-300' : ''}
                ${isExiting ? 'animate-out zoom-out-95 ease-in duration-200' : ''}`}
        >
          <Dialog role="dialog" className="outline-none">
            {({ close }) => (
              <>
                {isLoading ? (
                  <p className="text-center">Cargando...</p>
                ) : isPdf ? (
                  <iframe
                    src={signedUrl}
                    title={doc.originalName}
                    className="w-full h-[80vh] border-none"
                  />
                ) : (
                  <Image
                    alt={doc.originalName}
                    src={signedUrl || ''}
                    loading="lazy"
                    className="w-full h-[60vh] border-none"
                  />
                )}
                <div className="flex justify-center">
                  <Button
                    fullWidth
                    type="button"
                    className="h-10 w-52"
                    buttonType="normal"
                    onPress={() => {
                      close();
                    }}
                  >
                    Volver
                  </Button>
                </div>
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
}
