'use client';

import { ReactElement, ReactNode, useMemo } from 'react';
import { BiSolidEdit } from 'react-icons/bi';
import Link from 'next/link';
import { Button, Table } from '@digheontech/digh.ui';
import { Empty } from '@/components';
import { useGetInvoicesComplete } from '../hook/use-get-invoices-complete';

const columnClass = 'text-sm font-medium text-gray-500 ';
const rowClassName = 'text-sm text-gray-900';

export const AdjudicatorInvoiceTable = () => {
  const { invoices, isLoading } = useGetInvoicesComplete();

  const columns: TableColumn[] = useMemo(
    () => [
      { isRowHeader: true, name: 'Paciente', id: 'patient', className: columnClass },
      { name: 'Factura', id: 'type', className: columnClass },
      { name: 'Fecha', id: 'updatedAt', className: columnClass },
      { name: 'Documentos', id: 'fileCount', className: columnClass },
      { name: 'Estado', id: 'status', className: columnClass },
      {
        name: 'Acciones',
        id: 'actions',
        className: columnClass + 'flex justify-center w-30 text-center',
      },
    ],
    [],
  );

  interface TableColumn {
    name: string;
    id: string;
    isRowHeader?: boolean;
    className?: string;
  }
  interface TableRow {
    id: string;
    patient: ReactNode;
    type: string;
    updatedAt: ReactNode;
    status: ReactNode;
    fileCount?: number;
    actions: ReactNode;
  }

  const labelStatus = (status: string): ReactElement => {
    switch (status) {
      case 'draft':
        return <span className="text-gray-500">Borrador</span>;
      case 'admitted':
        return <span className="text-green-500">Admitida</span>;
      case 'rejected':
        return <span className="text-red-500">Rechazada</span>;
      case 'standby':
        return <span className="text-yellow-500">Pendiente</span>;
      default:
        return <span className="text-gray-500">Desconocido</span>;
    }
  };

  const formatDateTime = (date: string) => {
    const dateObj = new Date(date);
    return dateObj.toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const rows: TableRow[] = useMemo(
    () =>
      invoices?.map(({ _id: id, patient, type, updatedAt, status, fileCount }) => ({
        id,
        patient: <span className={rowClassName}>{patient?.fullName}</span>,
        type,
        updatedAt: <span className={rowClassName}>{formatDateTime(updatedAt)}</span>,
        status: labelStatus(status),
        fileCount,
        actions: (
          <div className="flex gap-2 justify-center items-center">
            <Link href={`/adjudicator/detail?invoiceId=${id}`} passHref>
              <Button
                leftIcon={<BiSolidEdit size={20} />}
                buttonType="normal"
                className="rounded-full w-10 h-10 p-2 border-none hover:bg-primary-200"
              />
            </Link>
          </div>
        ),
      })) || [],
    [invoices],
  );

  return (
    <div className="mt-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <p>Cargando facturas...</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <Table<TableRow>
            isLoading={isLoading}
            columns={columns}
            rows={rows}
            tableBodyProps={{
              renderEmptyState: () => <Empty />,
            }}
            wrapClasses="max-h-full"
            headerCellClasses="bg-[#DDEDF6] text-slate-700"
          />
        </div>
      )}
    </div>
  );
};
