import { useMutation } from '@tanstack/react-query';
import { useAuth, useShowError, useShowSuccess } from '@/hooks';
import { AxiosResponseError, Http } from '@/config/http';

import { SignInResponse, SignInValues } from '../components/sign-in.interface';

export const useSignIn = () => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { onLogin } = useAuth();

  const { mutate: signIn, ...rest } = useMutation({
    mutationKey: ['SIGN_IN'],
    mutationFn: (values: SignInValues) =>
      Http.post('/users/sign-in', values).then(({ data }) => data),
    onSuccess: ({ token, user }: SignInResponse) => {
      onLogin({ token, user });
      showSuccess({ title: 'Éxito', description: 'Inicio de sesión correcto.' });
    },
    onError: (error: AxiosResponseError) => showError(error),
  });

  return { signIn, ...rest };
};
