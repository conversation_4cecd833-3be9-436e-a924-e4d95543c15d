import { ReactNode } from 'react';
import clsx from 'clsx';

interface IWrapFileUpload {
  children: ReactNode;
  noStyle?: boolean;
  className?: HTMLElement['className'];
}

export function WrapFileUpload({ children, noStyle, className }: IWrapFileUpload) {
  const wrapperClasses = clsx(
    !noStyle &&
      'flex justify-center flex-col items-center w-full border-2 border-dashed border-teal-500 rounded-lg p-8 text-center bg-slate-100',
    className,
  );

  return <section className={wrapperClasses}>{children}</section>;
}
