import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { SearchPatientQuery } from './search-patient.query';
import { PatientRepository } from 'src/patients/infrastructure/repositories/patient.repository';
import { concatStrings } from 'src/shared/utils';

@QueryHandler(SearchPatientQuery)
export class SearchPatientHandler implements IQueryHandler<SearchPatientQuery> {
  constructor(private readonly patientRepository: PatientRepository) {}
  async execute(query: SearchPatientQuery) {
    const result = await this.patientRepository.search(query.termSearch);

    return this.mapPatientResponse(result);
  }

  private mapPatientResponse(data) {
    return data.map(item => ({
      id: item._id,
      name: concatStrings(item.firstName, item.middleName, item.surname),
      recordId: item.recordId,
    }));
  }
}
