import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';

import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';
import { GetInvoiceAdjustmentsDetailQuery } from './get-invoice-adjustments-detail.query';
import { InvoiceAdjustmentRepository } from 'src/invoices/infrastructure/repositories/invoice-adjustments.repository';

@QueryHandler(GetInvoiceAdjustmentsDetailQuery)
export class GetInvoiceAdjustmentsDetailHandler
  implements IQueryHandler<GetInvoiceAdjustmentsDetailQuery>
{
  constructor(
    private readonly invoiceRepository: InvoiceRepository,
    private readonly invoiceAdjustmentRepository: InvoiceAdjustmentRepository,
  ) {}

  async execute(query: GetInvoiceAdjustmentsDetailQuery) {
    const invoice = await this.invoiceRepository.findById(
      query.invoiceId,
      true,
    );
    const invoiceAdjustments =
      await this.invoiceAdjustmentRepository.findByInvoiceId(query.invoiceId);
    return { ...invoice.toObject(), invoiceAdjustments };
  }
}
