import { useState } from 'react';
import { Button } from '@digheontech/digh.ui';

import { BaseModal } from '@/components';
import { ProviderForm } from './provider-form';

export function ProvidersModal() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const closeModal = () => setIsModalOpen(false);
  const openModal = () => setIsModalOpen(true);

  return (
    <BaseModal
      title="Registrar datos"
      isOpen={isModalOpen}
      onClose={closeModal}
      trigger={
        <Button onPress={openModal} className="btn-primary">
          Registrar proveedor
        </Button>
      }
    >
      <ProviderForm onSubmitSuccess={closeModal} />
    </BaseModal>
  );
}
