import { DocumentType } from '@/enums';
import { StatusItem } from '@/modules/providers/create/components/provider-select-type/status-item';
import { getDocumentTypeLabel } from '@/tools/getDocumentTypeLabel';
import { useField } from 'formik';
import { Key } from 'react';
import { Button, ListBox, Popover, Select, SelectValue } from 'react-aria-components';
import { LuChevronsUpDown } from 'react-icons/lu';

const DOCUMENT_TYPES = [
  DocumentType.notaDeProgreso,
  DocumentType.notaDeProcedimiento,
  DocumentType.resultados,
  DocumentType.ordenMedica,
  DocumentType.referido,
] as const;

interface IDocumentTypeOption {
  documentType: DocumentType;
}

interface IDocumentTypeSelect {
  className?: HTMLElement['className'];
  disabled: boolean;
  name: string;
}

const DocumentTypeOption = ({ documentType }: IDocumentTypeOption) => (
  <StatusItem key={documentType} id={documentType}>
    <div className="flex gap-2 items-baseline">
      <span>{getDocumentTypeLabel(documentType)}</span>
    </div>
  </StatusItem>
);

export function DocumentTypeSelect({ className, disabled, name }: IDocumentTypeSelect) {
  const [field, meta, helpers] = useField<DocumentType>(name);

  const handleSelectionChange = (key: Key) => {
    helpers.setValue(key as DocumentType);
  };

  return (
    <div className={className}>
      <Select
        aria-label="Document Type"
        className="flex flex-col gap-1 w-full mb-4"
        placeholder="Select Document Type"
        defaultSelectedKey={undefined}
        selectedKey={field.value}
        onSelectionChange={handleSelectionChange}
        isDisabled={disabled}
      >
        <Button className="field-shadow h-4" aria-label="Document Type">
          <SelectValue className="flex-1 truncate placeholder-shown:italic">
            {value => getDocumentTypeLabel(value as unknown as DocumentType)}
          </SelectValue>
          <LuChevronsUpDown />
        </Button>
        <Popover className="max-h-60 w-[--trigger-width] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out">
          <ListBox className="outline-none p-1">
            {DOCUMENT_TYPES.map(documentType => (
              <DocumentTypeOption key={documentType} documentType={documentType} />
            ))}
          </ListBox>
        </Popover>
      </Select>
      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
