const booleanFields = [
  'conditionPatientRelatedToAutoAccident',
  'conditionPatientRelatedToOtherAccident',
  'conditionPatientRelatedToEmployment',
  'isOutsideLab',
  'acceptAssignment',
];

const toBoolean = (value: boolean | string | undefined | null): boolean => {
  return value === true || value === 'YES' || value === 'Yes';
};

const removeUndefinedDeep = (obj: any): any => {
  if (Array.isArray(obj)) return obj.map(removeUndefinedDeep);
  if (obj && typeof obj === 'object') {
    return Object.fromEntries(
      Object.entries(obj)
        .filter(([_, v]) => v !== undefined)
        .map(([k, v]) => [k, removeUndefinedDeep(v)]),
    );
  }
  return obj;
};

const cleanEmptyDates = (obj: any): any => {
  if (Array.isArray(obj)) return obj.map(cleanEmptyDates);
  if (typeof obj === 'object' && obj !== null) {
    return Object.fromEntries(
      Object.entries(obj).map(([key, val]) => {
        if (
          val &&
          typeof val === 'object' &&
          Object.keys(val).length === 0 &&
          (key.includes('date') || key.includes('Date'))
        ) {
          return [key, undefined];
        }
        return [key, cleanEmptyDates(val)];
      }),
    );
  }
  return obj;
};

const formatDate = (date: any): string | undefined => {
  if (!date) return undefined;
  const d = date instanceof Date ? date : new Date(date);
  return isNaN(d.getTime()) ? undefined : d.toISOString();
};

export const buildPayload = (data: any): Record<string, any> => {
  const payload: Record<string, any> = {};

  for (const key of Object.keys(data)) {
    let value = removeUndefinedDeep(data[key]);

    if (!key.toLowerCase().includes('date')) {
      value = cleanEmptyDates(value);
    }

    if (booleanFields.includes(key)) {
      payload[key] = toBoolean(data[key]);
    } else if (key.toLowerCase().includes('date')) {
      payload[key] = formatDate(data[key]);
    } else if (key === 'insured') {
      payload.insured = {
        ...value,
        anotherHealthBenefitPlan: toBoolean(value.anotherHealthBenefitPlan),
        birthDate: formatDate(data[key].birthDate),
      };
    } else if (key === 'patient') {
      payload.patient = {
        ...value,
        birthDate: formatDate(data[key].birthDate),
      };
    } else {
      payload[key] = value;
    }
  }

  return payload;
};
