import * as yup from 'yup';
import { DocumentType } from '@/enums';

const MAX_FILE_SIZE_IN_BYTES = 5 * 1024 * 1024;
const ALLOWED_MIME_TYPES = ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf'];

export const uploadFormSchema = yup.object({
  file: yup
    .mixed<File>()
    .required('El archivo es requerido')
    .test(
      'fileSize',
      'El archivo es demasiado grande',
      value => value && value.size <= MAX_FILE_SIZE_IN_BYTES,
    )
    .test(
      'fileType',
      'El tipo de archivo no es soportado',
      value => value && ALLOWED_MIME_TYPES.includes(value.type),
    ),
  fileName: yup
    .string()
    .required('El nombre del archivo es requerido')
    .max(100, 'El nombre del archivo no puede exceder 100 caracteres')
    .matches(/^[a-zA-Z0-9_\-\s.]+$/, 'El nombre del archivo contiene caracteres inválidos'),
  documentType: yup
    .mixed<DocumentType>()
    .oneOf(Object.values(DocumentType), 'Tipo de documento inválido')
    .required('El tipo de documento es requerido'),
});

export const uploadFormSchemaEdit = yup.object({
  fileName: yup
    .string()
    .required('El nombre del archivo es requerido')
    .max(100, 'El nombre del archivo no puede exceder 100 caracteres')
    .matches(/^[a-zA-Z0-9_\-\s.]+$/, 'El nombre del archivo contiene caracteres inválidos'),
  documentType: yup
    .mixed<DocumentType>()
    .oneOf(Object.values(DocumentType), 'Tipo de documento inválido')
    .required('El tipo de documento es requerido'),
});
