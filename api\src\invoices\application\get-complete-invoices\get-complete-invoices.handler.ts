import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { InvoiceRepository } from '../../infrastructure/repositories/invoice.repository';
import { GetCompleteInvoicesQuery } from './get-complete-invoices.query';
import { InvoiceStatus } from 'src/shared/enums/invoice-status.enum';

@QueryHandler(GetCompleteInvoicesQuery)
export class GetCompleteInvoicesHandler
  implements IQueryHandler<GetCompleteInvoicesQuery>
{
  constructor(private readonly invoiceRepository: InvoiceRepository) {}

  execute() {
    return this.invoiceRepository.search(
      {
        status: InvoiceStatus.Admitted,
      },
      {
        _id: true,
        patient: true,
        type: true,
        status: true,
        fileCount: true,
        updatedAt: true,
      },
    );
  }
}
