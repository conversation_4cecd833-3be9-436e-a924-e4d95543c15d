'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks';
import { MdExitToApp, MdChevronLeft, MdChevronRight } from 'react-icons/md';
import { Button } from '@digheontech/digh.ui';

import PhyCorrLogo from '../phy-corr-logo';
import { ItemsAdmin } from './items-admin';
import { ItemsProviders } from './items-provider';
import { ItemsAdjudicator } from './items-adjudicator';

export function AppMenu({
  collapsed = false,
  setCollapsed = () => {},
}: {
  collapsed?: boolean;
  setCollapsed?: (collapsed: boolean) => void;
}) {
  const { onLogout } = useAuth();

  return (
    <section
      className={`flex flex-col items-center h-full transition-all duration-200 ${
        collapsed ? 'w-0' : 'w-full'
      }`}
      style={{ minWidth: collapsed ? 40 : 220 }}
    >
      <div className="flex items-center mb-2">
        <PhyCorrLogo medium={collapsed} small={collapsed} />
      </div>
      <Button
        buttonType="normal"
        className="self-end text-xl focus:outline-none border-none text-slate-600 hover:bg-slate-200 rounded-full"
        onPress={() => setCollapsed(!collapsed)}
        aria-label={collapsed ? 'Expandir menú' : 'Colapsar menú'}
      >
        {collapsed ? <MdChevronRight /> : <MdChevronLeft />}
      </Button>
      <ItemsAdmin collapsed={collapsed} />
      <ItemsProviders collapsed={collapsed} />
      <ItemsAdjudicator collapsed={collapsed} />
      <div className="mt-auto w-full">
        <Button
          leftIcon={<MdExitToApp />}
          onPress={onLogout}
          fullWidth={!collapsed}
          buttonType="dangerOutlined"
          className={`border-none text-2xl ${collapsed ? 'justify-center' : ''}`}
        >
          {!collapsed && 'SALIR'}
        </Button>
      </div>
    </section>
  );
}
