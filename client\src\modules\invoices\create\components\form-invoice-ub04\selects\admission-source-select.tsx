import { FormField } from '@/components';

interface AdmissionSourceSelectProps {
  name: string;
  label: string;
  disabled?: boolean;
}

export function AdmissionSourceSelect({ name, label, disabled }: AdmissionSourceSelectProps) {
  const admissionSources = [
    { value: '1', label: 'Physician Referral' },
    { value: '2', label: 'Clinic Referral' },
    { value: '3', label: 'HMO Referral' },
    { value: '4', label: 'Transfer from Hospital' },
    { value: '5', label: 'Transfer from SNF' },
    { value: '6', label: 'Transfer from Another Health Care Facility' },
    { value: '7', label: 'Emergency Room' },
    { value: '8', label: 'Court/Law Enforcement' },
    { value: '9', label: 'Information Not Available' },
    { value: 'A', label: 'Transfer from Critical Access Hospital' },
    { value: 'B', label: 'Transfer from Another Home Health Agency' },
    { value: 'C', label: 'Readmission to Same Home Health Agency' },
    { value: 'D', label: 'Transfer from One Distinct Unit to Another' },
    { value: 'E', label: 'Transfer from Ambulatory Surgery Center' },
    { value: 'F', label: 'Transfer from Hospice' },
  ];

  return (
    <FormField
      name={name}
      label={label}
      type="select"
      options={admissionSources}
      disabled={disabled}
    />
  );
}
