'use client';

import { Label } from 'react-aria-components';
import { FieldDatePicker } from '@/components';
import { QualifierSelectItem14, QualifierSelectItem15 } from '../selects';

interface IItems14_16 {
  disabled: boolean;
}

export function Items14_16({ disabled }: IItems14_16) {
  return (
    <>
      <section className="grid grid-cols-2 border border-gray-300 border-r-transparent">
        <div className="flex flex-col gap-2 p-4 hover-card">
          <div className="flex justify-center gap-2">
            <div className="w-full">
              <strong className="mr-4">14.</strong>
              <Label className="text-slate-700">DATE OF CURRENT</Label>
              <FieldDatePicker
                name="dateOfCurrentIllnessInjuryPregnancy"
                disabled={disabled}
                className="field-shadow mt-2"
              />
            </div>
            <div className="min-w-60">
              <Label className="text-slate-700">QUAl</Label>
              <QualifierSelectItem14 className="mt-2" />
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-2 p-4 hover-card">
          <div className="flex justify-center gap-2">
            <div className="w-full">
              <strong className="mr-4">15.</strong>
              <Label className="text-slate-700">OTHER DATE</Label>
              <FieldDatePicker
                name="otherDateConditionOfIllnessOrTreatment"
                disabled={disabled}
                className="field-shadow mt-2"
              />
            </div>
            <div className="min-w-60">
              <Label className="text-slate-700">QUAl</Label>
              <QualifierSelectItem15 className="mt-2" />
            </div>
          </div>
        </div>
      </section>

      <section className="grid border border-gray-300">
        <div className="flex flex-col gap-4 p-4 w-full hover-card">
          <div className="flex gap-4 w-full">
            <strong>16.</strong>
            <p>DATES PATIENT UNABLE TO WORK IN CURRENT OCCUPATION</p>
          </div>

          <div className="flex gap-4 w-full justify-around">
            <FieldDatePicker label="FROM" name="unableToWorkFromDate" disabled={disabled} />
            <FieldDatePicker label="TO" name="unableToWorkToDate" disabled={disabled} />
          </div>
        </div>
      </section>
    </>
  );
}
