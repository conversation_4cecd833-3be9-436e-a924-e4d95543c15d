import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';

import { ProviderRepository } from 'src/providers/infrastructure/repositories/provider.repository';

import { ListProvidersQuery } from './list-providers.query';

@QueryHandler(ListProvidersQuery)
export class ListProvidersHandler implements IQueryHandler<ListProvidersQuery> {
  constructor(private readonly providerRepository: ProviderRepository) {}
  async execute() {
    return this.providerRepository.getAll();
  }
}
