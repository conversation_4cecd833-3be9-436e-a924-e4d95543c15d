import { FormField } from '@/components';

export function UB04Items56_65({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 p-4">
      <h3 className="text-lg font-bold mb-4">Financial Information (Fields 56-65)</h3>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <h4 className="font-semibold mb-3">National Provider Identifier</h4>
          <div className="space-y-3">
            <FormField
              name="billingProviderNPI"
              label="56. Billing Provider NPI"
              disabled={isLoading}
            />
            <FormField
              name="payToProviderNPI"
              label="57. Pay-to Provider NPI"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <h4 className="font-semibold mb-3">Insured Information</h4>
          <div className="space-y-3">
            <FormField name="insuredName" label="58. Insured Name" disabled={isLoading} />
            <FormField
              name="patientRelationshipToInsured"
              label="59. Patient Relationship to Insured"
              type="select"
              options={[
                { value: '01', label: 'Spouse' },
                { value: '18', label: 'Self' },
                { value: '19', label: 'Child' },
                { value: '20', label: 'Employee' },
                { value: '21', label: 'Unknown' },
                { value: '39', label: 'Organ Donor' },
                { value: '40', label: 'Cadaver Donor' },
                { value: '53', label: 'Life Partner' },
              ]}
              disabled={isLoading}
            />
            <FormField name="insuredUniqueId" label="60. Insured Unique ID" disabled={isLoading} />
            <FormField
              name="insuredGroupName"
              label="61. Insured Group Name"
              disabled={isLoading}
            />
            <FormField
              name="insuredGroupNumber"
              label="62. Insured Group Number"
              disabled={isLoading}
            />
          </div>
        </div>
      </div>

      <div className="mt-6">
        <h4 className="font-semibold mb-3">Treatment Authorization</h4>
        <div className="grid grid-cols-3 gap-4">
          <FormField
            name="treatmentAuthorizationCode"
            label="63. Treatment Authorization Code"
            disabled={isLoading}
          />
          <FormField
            name="documentControlNumber"
            label="64. Document Control Number"
            disabled={isLoading}
          />
          <FormField name="employerName" label="65. Employer Name" disabled={isLoading} />
        </div>
      </div>
    </section>
  );
}
