import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Patient } from '../schema/patient.schema';

@Injectable()
export class PatientRepository {
  constructor(
    @InjectModel(Patient.name)
    private patientModel: Model<Patient>,
  ) {}

  async search(termSearch: string) {
    return this.patientModel
      .find({
        $or: [
          { firstName: { $regex: termSearch, $options: 'i' } },
          { middleName: { $regex: termSearch, $options: 'i' } },
          { surname: { $regex: termSearch, $options: 'i' } },
          { recordId: { $regex: termSearch, $options: 'i' } },
        ],
      })
      .exec();
  }

  async searchByRecordNumber(recordNumber: string) {
    return this.patientModel.findOne({ recordId: recordNumber }).exec();
  }
}
