import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Http } from '@/config/http';
import { IInvoicesTable } from '@/interfaces';

const key = ['list_providers'];

export const useGetInvoices = () => {
  const queryClient = useQueryClient();

  const { data: invoices, ...rest } = useQuery<IInvoicesTable[]>({
    queryKey: key,
    queryFn: () => Http.get('invoices').then(({ data }): IInvoicesTable[] => data),
  });

  const invalidateInvoices = () => queryClient.invalidateQueries({ queryKey: key });

  return { invoices, invalidateInvoices, ...rest };
};
