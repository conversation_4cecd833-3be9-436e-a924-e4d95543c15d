import { Controller, Get, Query } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { Roles } from 'src/identity/infrastructure/roles.decorator';
import { UserRole } from 'src/shared/enums';
import { SearchPatientQuery } from '../applications/search-patient/search-patient.query';

@Controller('patients')
export class PatientsController {
  constructor(private readonly queryBus: QueryBus) {}

  @Get('/')
  @Roles(UserRole.Provider)
  searchPatients(@Query('termSearch') termSearch: string) {
    return this.queryBus.execute(new SearchPatientQuery(termSearch));
  }
}
