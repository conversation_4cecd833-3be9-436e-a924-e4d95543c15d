import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { NotFoundException } from '@nestjs/common';
import { FOLDER_INVOICE } from 'src/invoices/domain/invoice.enum';
import { AwsS3Service } from 'src/file-management/infrastructure/aws-s3.service';
import { InvoiceFileRepository } from 'src/invoices/infrastructure/repositories/invoice-file.repository';
import { GetFileSignedUrlQuery } from './get-file-signed-url.query';

@QueryHandler(GetFileSignedUrlQuery)
export class GetFileSignedUrlHandler
  implements IQueryHandler<GetFileSignedUrlQuery>
{
  constructor(
    private readonly fileService: AwsS3Service,
    private readonly invoiceFileRepository: InvoiceFileRepository,
  ) {}

  async execute(query: GetFileSignedUrlQuery) {
    const file = await this.invoiceFileRepository.findByIdAndInvoiceId(
      query.fileId,
      query.invoiceId,
    );
    if (!file) {
      throw new NotFoundException('File not found');
    }

    const filePath = `${FOLDER_INVOICE}/${file.fileName}`;

    const fileUrl = await this.fileService.getSignedUrl(filePath, 300); // 5 minutes expiration

    return { fileUrl };
  }
}
