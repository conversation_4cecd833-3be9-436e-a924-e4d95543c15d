import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { InvoiceFileRepository } from 'src/invoices/infrastructure/repositories/invoice-file.repository';
import { GetInvoiceFilesQuery } from './get-invoice-files.query';

@QueryHandler(GetInvoiceFilesQuery)
export class GetInvoiceFilesHandler
  implements IQueryHandler<GetInvoiceFilesQuery>
{
  constructor(private readonly invoiceFileRepository: InvoiceFileRepository) {}

  async execute(query: GetInvoiceFilesQuery) {
    return this.invoiceFileRepository.getByInvoiceId(query.invoiceId);
  }
}
