import { useSearchParams } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosResponseError, Http } from '@/config/http';
import { useShowError, useShowSuccess } from '@/hooks';
import { buildPayload } from '../utils';

export const useUpdateInvoiceAdjustment = () => {
  const searchParams = useSearchParams();
  const invoiceId = searchParams.get('invoiceId');

  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const queryClient = useQueryClient();
  const mutationKey = ['update_invoice', invoiceId];

  const invalidate = () => queryClient.resetQueries({ queryKey: ['adjustments', invoiceId] });

  const {
    mutate: update,
    isPending: isUpdating,
    ...rest
  } = useMutation({
    mutationKey,
    mutationFn: (data: any) => {
      if (data.supplementalInformation) {
        return Http.patch(`invoices/${invoiceId}/adjustment`, {
          supplementalInformation: data.supplementalInformation,
        });
      }

      const payload = buildPayload(data);
      return Http.patch(`invoices/${invoiceId}/adjustment`, payload);
    },
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Factura actualizada.' });
      invalidate();
    },
  });

  return { update, isUpdating, ...rest };
};
