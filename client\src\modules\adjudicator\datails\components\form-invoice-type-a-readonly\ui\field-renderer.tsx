import { useState } from 'react';
import { FieldDatePicker, FormField } from '@/components';
import { LuDollarSign } from 'react-icons/lu';
import { Label } from 'react-aria-components';
import { Gender, InsuranceCarrier, Relationship, YesOrNot } from '@/enums';
import { ResubmissionSelectItem22 } from '@/modules/invoices/create/components/form-invoice-type-a/items/item-22';
import {
  QualifierSelectItem14,
  QualifierSelectItem15,
  QualifierSelectItem17a,
  RoleSelectItem17,
} from '@/modules/invoices/create/components/form-invoice-type-a/selects';
import {
  CityZipCodeSearch,
  MultipleDiagnoses,
} from '@/modules/invoices/create/components/form-invoice-type-a/searchs';
import { SuplementalInformationEdit } from './suplemental-information-edit';

const { Female, Male } = Gender;
const { Yes, No } = YesOrNot;
const { CHAMPVA, FECA, GROUP_HEALTH_PLAN, MEDICAID, MEDICARE, OTH<PERSON>, TRI<PERSON><PERSON> } = InsuranceCarrier;
const { Child, Other, Self, Spouse } = Relationship;

interface IFieldRenderer {
  fieldName: string;
  isLoading?: boolean;
  initialValues?: any;
  formikProps?: any;
}

export const FieldRenderer = ({
  fieldName,
  isLoading = false,
  initialValues,
  formikProps,
}: IFieldRenderer) => {
  const [supplementalInfo, setSupplementalInfo] = useState<any>({});

  switch (fieldName) {
    case 'insuranceCarrier':
      return (
        <FormField
          disabled={isLoading}
          type="radio"
          name="insuranceCarrier"
          options={[
            { value: MEDICARE, label: 'Medicare', sublabel: '(Medicare #)' },
            { value: MEDICAID, label: 'Medicaid', sublabel: '(Medicaid #)' },
            { value: TRICARE, label: 'Tricare', sublabel: "(Sponsor's SSN)" },
            { value: CHAMPVA, label: 'Champva', sublabel: '(Member ID#)' },
            {
              value: GROUP_HEALTH_PLAN,
              label: 'Group Health Plan',
              sublabel: '(SSN or ID)',
            },
            { value: FECA, label: 'Feca Black Lung', sublabel: '(SSN)' },
            { value: OTHER, label: 'Other', sublabel: '(ID)' },
          ]}
        />
      );
    case 'insuranceIdNumber':
      return (
        <FormField
          disabled={isLoading}
          name="insuranceIdNumber"
          label="INSURED'S I.D. NUMBER"
          className="w-full"
        />
      );

    case 'patient.fullName':
      return (
        <FormField
          disabled={isLoading}
          name="patient.fullName"
          label="PATIENT'S NAME (Last Name, First Name, Middle Initial)"
          className="w-full"
        />
      );
    case 'patient.birthDate':
      return (
        <FieldDatePicker
          label="PATIENT´S BIRTHDATE"
          name="patient.birthDate"
          disabled={isLoading}
          labelClassName="text-primary-500 font-bold block text-sm mb-1"
        />
      );
    case 'patient.gender':
      return (
        <FormField
          label="SEX"
          type="radio"
          disabled={isLoading}
          name="patient.gender"
          options={[
            { value: Female, label: 'F' },
            { value: Male, label: 'M' },
          ]}
        />
      );
    case 'patient.address':
      return (
        <section>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex gap-4 justify-start">
              <strong className="w-4">5.</strong>
              <div className="w-full">
                <FormField
                  disabled={isLoading}
                  name="patient.address.street"
                  label="PATIENT'S ADDRESS (No., Street)"
                />
              </div>
            </div>
          </div>
          <div className="mt-4 ml-8">
            <CityZipCodeSearch
              className="gap-4"
              state="patient.address.state"
              city="patient.address.city"
              zipCode="patient.address.zipCode"
              disabled={isLoading}
            />
          </div>
        </section>
      );

    case 'insured.fullName':
      return (
        <FormField
          disabled={isLoading}
          name="insured.fullName"
          label="INSURED'S NAME (Last Name, First Name, Middle Initial)"
          className="w-full"
        />
      );
    case 'patientRelationshipToInsured':
      return (
        <FormField
          label="PATIENT´S RELATIONSHIP TO INSURED"
          type="radio"
          disabled={isLoading}
          name="patientRelationshipToInsured"
          options={[
            { value: Self, label: 'Self' },
            { value: Spouse, label: 'Spouse' },
            { value: Child, label: 'Child' },
            { value: Other, label: 'Other' },
          ]}
        />
      );
    case 'insured.address':
      return (
        <section>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex gap-4 justify-start">
              <strong className="w-4">7.</strong>
              <div className="w-full">
                <FormField
                  disabled={isLoading}
                  name="insured.address.street"
                  label="INSURED’S ADDRESS (No., Street)"
                />
              </div>
            </div>
          </div>
          <div className="mt-4 ml-8">
            <CityZipCodeSearch
              className="gap-4"
              state="insured.address.state"
              city="insured.address.city"
              zipCode="insured.address.zipCode"
              disabled={isLoading}
            />
          </div>
        </section>
      );
    case 'otherInsuredName': {
      return (
        <section>
          <div className="flex gap-4 my-2 ml-4 ">
            <strong>9.</strong>
            <FormField
              label="OTHER INSURED'S NAME (Last Name, First Name, Middle Initial)"
              name="otherInsuredName"
              disabled={isLoading}
            />
          </div>
          <div className="flex gap-4 my-2 ml-4">
            <strong>a.</strong>
            <FormField
              label="OTHER INSURED'S POLICY OR GROUP NUMBER"
              name="insured.policyGroupNumber"
              disabled={isLoading}
            />
          </div>
          <div className="flex gap-4 my-2 ml-4">
            <strong>b.</strong>
            <FormField
              label="RESERVED FOR NUCC USE"
              name=" "
              className="border-none bg-transparent"
              disabled
            />
          </div>
          <div className="flex gap-4 my-2 ml-4">
            <strong>c.</strong>
            <FormField
              label="RESERVED FOR NUCC USE"
              name=" "
              className="border-none bg-transparent"
              disabled
            />
          </div>
          <div className="flex gap-4 my-2 ml-4">
            <strong>d.</strong>
            <FormField
              label="INSURANCE PLAN NAME OR PROGRAM NAME"
              name="otherInsuredPlanNameOrProgramName"
              disabled={isLoading}
            />
          </div>
        </section>
      );
    }
    case 'conditionPatientRelatedToEmployment':
      return (
        <section>
          <div className="flex gap-4 p-4 ">
            <strong>10.</strong>
            <Label className="block text-sm font-medium">
              IS PATIENT&apos;S CONDITION RELATED TO:
            </Label>
          </div>
          <div className="flex flex-col gap-4 ml-6">
            <div className="flex gap-4 mb-2">
              <strong>a.</strong>
              <FormField
                label="EMPLOYMENT?"
                name="conditionPatientRelatedToEmployment"
                disabled={isLoading}
                type="radio"
                options={[
                  { value: Yes, label: Yes },
                  { value: No, label: No },
                ]}
              />
              <small>(Current or Previous)</small>
            </div>
            <div className="flex gap-16">
              <div className="flex gap-4 my-2">
                <strong>b.</strong>
                <FormField
                  label="AUTO ACCIDENT?"
                  name="conditionPatientRelatedToAutoAccident"
                  disabled={isLoading}
                  type="radio"
                  options={[
                    { value: Yes, label: Yes },
                    { value: No, label: No },
                  ]}
                />
              </div>
              <FormField
                label="Place (State)"
                name="conditionPatientRelatedToAutoAccidentPlace"
                type="text"
                disabled={isLoading}
              />
            </div>
            <div className="flex gap-4 my-2">
              <strong>c.</strong>
              <FormField
                label="OTHER ACCIDENT?"
                name="conditionPatientRelatedToOtherAccident"
                type="radio"
                options={[
                  { value: Yes, label: Yes },
                  { value: No, label: No },
                ]}
                disabled={isLoading}
              />
            </div>
            <div className="flex gap-4 my-2">
              <strong>d.</strong>
              <FormField
                label="CLAIM CODES (Designated by NUCC)"
                name=" "
                type="text"
                className="border-none bg-transparent"
                disabled
              />
            </div>
          </div>
        </section>
      );
    case 'otherInsuredPolicyOrGroupNumber':
      return (
        <section>
          <div className="flex gap-4 my-2 ml-2">
            <strong>11</strong>
            <FormField
              label="INSURED'S POLICY GROUP OR FECA NUMBER"
              name="otherInsuredPolicyOrGroupNumber"
              disabled={isLoading}
            />
          </div>
          <div className="flex justify-evenly p-4 my-2">
            <div className="w-full">
              <div className="flex gap-4 w-full">
                <strong>a.</strong>
                <FieldDatePicker
                  label="INSURED'S DATE OF BIRTH"
                  name="insured.birthDate"
                  disabled={isLoading}
                  labelClassName="text-primary-500 font-bold block text-sm mb-1"
                />
              </div>
            </div>
            <div className="w-1/2">
              <FormField
                label="SEX"
                name="insured.gender"
                type="radio"
                options={[
                  { value: Female, label: 'F' },
                  { value: Male, label: 'M' },
                ]}
                disabled={isLoading}
              />
            </div>
          </div>
          <div className="flex gap-4 my-2 ml-4">
            <strong>b.</strong>
            <FormField
              label="OTHER CLAIM ID (Designated by NUCC)"
              name=" "
              className="border-none bg-transparent"
              disabled
            />
          </div>
          <div className="flex gap-4 pd-4 my-2 ml-4">
            <strong>c.</strong>
            <FormField
              label="INSURANCE PLAN NAME OR PROGRAM NAME"
              name="insured.planOrProgramName"
              disabled={isLoading}
            />
          </div>
          <div className="flex gap-4 my-2 ml-4">
            <div className="flex gap-4 mb-2 w-full">
              <strong>d.</strong>
              <FormField
                label="IS THERE ANOTHER HEALTH BENEFIT PLAN?"
                name="insured.anotherHealthBenefitPlan"
                disabled={isLoading}
                type="radio"
                options={[
                  { value: Yes, label: Yes },
                  { value: No, label: No },
                ]}
              />
            </div>
            <small className="w-1/2">
              <strong>If yes,</strong> return to and complete item 9, 9a and 9d.
            </small>
          </div>
        </section>
      );

    case 'dateOfCurrentIllnessInjuryPregnancy':
      return (
        <div className="flex flex-col gap-2 p-4">
          <div className="flex justify-center gap-2">
            <div className="w-full">
              <strong className="mr-4">14.</strong>
              <Label className="text-slate-700">DATE OF CURRENT</Label>
              <FieldDatePicker
                name="dateOfCurrentIllnessInjuryPregnancy"
                disabled={isLoading}
                className="field-shadow mt-2"
              />
            </div>
            <div className="min-w-60">
              <Label className="text-slate-700">QUAl</Label>
              <QualifierSelectItem14 className="mt-2" />
            </div>
          </div>
        </div>
      );
    case 'qualifierOfCurrentIllnessInjuryAccident':
      return (
        <FormField
          disabled={isLoading}
          name="qualifierOfCurrentIllnessInjuryAccident"
          label="QUALIFIER"
          className="w-full"
        />
      );
    case 'otherDateConditionOfIllnessOrTreatment':
      return (
        <div className="flex justify-center gap-2">
          <div className="w-full">
            <strong className="mr-4">15.</strong>
            <Label className="text-slate-700">OTHER DATE</Label>
            <FieldDatePicker
              name="otherDateConditionOfIllnessOrTreatment"
              disabled={isLoading}
              className="field-shadow mt-2"
            />
          </div>
          <div className="min-w-60">
            <Label className="text-slate-700">QUAl</Label>
            <QualifierSelectItem15 className="mt-2" />
          </div>
        </div>
      );
    case 'qualifierOfOtherConditionOfIllnessOrTreatment':
      return (
        <FormField
          disabled={isLoading}
          name="qualifierOfOtherConditionOfIllnessOrTreatment"
          label="QUALIFIER"
          className="w-full"
        />
      );
    case 'unableToWorkFromDate':
      return (
        <section>
          <div className="flex gap-4 w-full justify-around">
            <FieldDatePicker label="FROM" name="unableToWorkFromDate" disabled={isLoading} />
            <FieldDatePicker label="TO" name="unableToWorkToDate" disabled={isLoading} />
          </div>
        </section>
      );
    case 'unableToWorkToDate':
      return (
        <FieldDatePicker
          label="UNABLE TO WORK TO DATE"
          name="unableToWorkToDate"
          disabled={isLoading}
          labelClassName="text-primary-500 font-bold block text-sm mb-1"
        />
      );

    case 'referringProviderName':
      return (
        <section className="grid grid-cols-2 items-center border">
          <div className="flex gap-2 flex-col">
            <div className="flex gap-4 my-2 ml-4 ">
              <strong>17.</strong>
              <div>NAME OF REFERRING PROVIDER OR OTHER SOURCE</div>
            </div>
            <div className="flex gap-2 justify-center">
              <div className="w-60 mt-2 ">
                <RoleSelectItem17 />
              </div>
              <div className="w-full">
                <FormField name="referringProviderName" className="!h-12" disabled={isLoading} />
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <div className="flex gap-2 items-center">
              <strong>17a. </strong>
              <QualifierSelectItem17a className="min-w-48 mt-6 ml-4" />
              <div className="w-full">
                <FormField
                  name="referringProviderOtherId"
                  className="!h-12 w-full"
                  disabled={isLoading}
                />
              </div>
            </div>
            <div className="flex gap-4 items-center">
              <strong>17b. </strong>
              <b>NPI</b>
              <div className="w-full">
                <FormField
                  name="referringProviderNpi"
                  className="!h-12 w-full"
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>
        </section>
      );
    case 'referringProviderQualifier':
      return (
        <FormField
          disabled={isLoading}
          name="referringProviderQualifier"
          label="QUALIFIER"
          className="w-full"
        />
      );
    case 'referringProviderOtherId':
      return (
        <FormField
          disabled={isLoading}
          name="referringProviderOtherId"
          label="OTHER ID"
          className="w-full"
        />
      );
    case 'referringProviderNpi':
      return (
        <FormField
          disabled={isLoading}
          name="referringProviderNpi"
          label="NPI"
          className="w-full"
        />
      );

    case 'hospitalizationFromDate':
      return (
        <section>
          <div className="flex gap-4 w-full justify-around">
            <FieldDatePicker label="FROM" name="hospitalizationFromDate" disabled={isLoading} />
            <FieldDatePicker label="TO" name="hospitalizationToDate" disabled={isLoading} />
          </div>
        </section>
      );
    case 'hospitalizationToDate':
      return (
        <FieldDatePicker
          label="HOSPITALIZATION TO DATE"
          name="hospitalizationToDate"
          disabled={isLoading}
          labelClassName="text-primary-500 font-bold block text-sm mb-1"
        />
      );

    case 'isOutsideLab':
      return (
        <section>
          <div className="flex justify-start gap-16 w-full">
            <div className="min-w-64">
              <FormField
                label="OUTSIDE LAB?"
                name="isOutsideLab"
                disabled={isLoading}
                type="radio"
                options={[
                  { value: Yes, label: Yes },
                  { value: No, label: No },
                ]}
              />
            </div>
            <FormField
              label="$ CHARGES"
              name="outsideLabCharges"
              type="number"
              disabled={isLoading}
            />
          </div>
        </section>
      );
    case 'outsideLabCharges':
      return (
        <FormField
          disabled={isLoading}
          name="outsideLabCharges"
          label="$ CHARGES"
          type="number"
          className="w-full"
        />
      );

    case 'icd10DiagnosisCodesForDiseasesOrInjuries':
      return (
        <MultipleDiagnoses name="icd10DiagnosisCodesForDiseasesOrInjuries" disabled={isLoading} />
      );

    case 'resubmissionCode':
      return (
        <div className="grid grid-cols-2 gap-4 p-4">
          <div className="flex flex-col gap-4 justify-start">
            <div className="flex gap-4 justify-start">
              <strong className="w-4">22.</strong>
              <Label> MEDICAID RESUBMISSION CODE</Label>
            </div>
            <ResubmissionSelectItem22 />
          </div>
          <div className="w-full">
            <FormField
              label="ORIGINAL REF. NO."
              name="originalReferenceNumber"
              className="!h-12 mt-2"
              disabled={isLoading}
            />
          </div>
        </div>
      );

    case 'priorAuthorizationNumber':
      return (
        <FormField
          disabled={isLoading}
          name="priorAuthorizationNumber"
          label="PRIOR AUTHORIZATION NUMBER"
          className="w-full"
        />
      );

    case 'federalTaxIdNumber':
      return (
        <section>
          <div className="grid grid-cols-2 gap-4 w-full">
            <FormField
              name="federalTaxIdNumber"
              label="FEDERAL TAX I.D. NUMBER"
              disabled={isLoading}
            />
            <div className="max-w-14">
              <FormField
                label="TYPE"
                className="flex"
                name="federalTaxIdNumberType"
                disabled={isLoading}
                type="radio"
                options={[
                  { value: 'SSN', label: 'SSN' },
                  { value: 'EIN', label: 'EIN' },
                ]}
              />
            </div>
          </div>
        </section>
      );
    case 'federalTaxIdNumberType':
      return (
        <FormField
          label="TYPE"
          name="federalTaxIdNumberType"
          disabled={isLoading}
          type="radio"
          options={[
            { value: 'SSN', label: 'SSN' },
            { value: 'EIN', label: 'EIN' },
          ]}
        />
      );
    case 'patientAccountNumber':
      return (
        <FormField
          disabled={isLoading}
          name="patientAccountNumber"
          label="PATIENT'S ACCOUNT NO."
          className="w-full"
        />
      );
    case 'acceptAssignment':
      return (
        <FormField
          label="ACCEPT ASSIGNMENT?"
          name="acceptAssignment"
          disabled={isLoading}
          type="radio"
          options={[
            { value: Yes, label: Yes },
            { value: No, label: No },
          ]}
        />
      );
    case 'totalCharge':
      return (
        <div className="p-4">
          <b>28. </b>
          <Label>TOTAL CHARGE</Label>
          <div className="flex items-end gap-2">
            <div className="mb-2 ml-2">
              <LuDollarSign />
            </div>
            <div className="w-full">
              <FormField name="totalCharge" type="number" disabled={isLoading} />
            </div>
          </div>
        </div>
      );
    case 'amountPaid':
      return (
        <FormField
          disabled={isLoading}
          name="amountPaid"
          label="AMOUNT PAID"
          type="number"
          className="w-full"
        />
      );

    case 'patientOrAuthorizedSignatureDate':
      return (
        <FieldDatePicker
          label="PATIENT'S OR AUTHORIZED PERSON'S SIGNATURE DATE"
          name="patientOrAuthorizedSignatureDate"
          disabled={isLoading}
          labelClassName="text-primary-500 font-bold block text-sm mb-1"
        />
      );
    case 'physicianSignature':
      return (
        <FormField
          disabled={isLoading}
          name="physicianSignature"
          label="SIGNATURE OF PHYSICIAN OR SUPPLIER"
          className="w-full"
        />
      );
    case 'physicianSignatureDate':
      return (
        <FieldDatePicker
          label="PHYSICIAN SIGNATURE DATE"
          name="physicianSignatureDate"
          disabled={isLoading}
          labelClassName="text-primary-500 font-bold block text-sm mb-1"
        />
      );

    case 'serviceFacilityLocation.name':
      return (
        <section>
          <div className="w-full grid grid-cols gap-4 mt-2">
            <FormField disabled={isLoading} name="serviceFacilityLocation.name" label="NAME" />
            <FormField
              disabled={isLoading}
              name="serviceFacilityLocation.address"
              label="ADDRESS"
            />
            <CityZipCodeSearch
              state="serviceFacilityLocation.state"
              city="serviceFacilityLocation.city"
              zipCode="serviceFacilityLocation.zipCode"
              disabled={isLoading}
            />
          </div>
          <div className="grid grid-cols-2 gap-4 mt-2">
            <FormField disabled={isLoading} name="serviceFacilityLocation.npi" label="A." />
            <FormField disabled={isLoading} name="serviceFacilityLocation.otherId" label="B." />
          </div>
        </section>
      );
    case 'serviceFacilityLocation.address':
      return (
        <FormField
          disabled={isLoading}
          name="serviceFacilityLocation.address"
          label="SERVICE FACILITY ADDRESS"
          className="w-full"
        />
      );
    case 'serviceFacilityLocation.npi':
      return (
        <FormField
          disabled={isLoading}
          name="serviceFacilityLocation.npi"
          label="SERVICE FACILITY NPI"
          className="w-full"
        />
      );
    case 'serviceFacilityLocation.otherId':
      return (
        <FormField
          disabled={isLoading}
          name="serviceFacilityLocation.otherId"
          label="SERVICE FACILITY OTHER ID"
          className="w-full"
        />
      );

    case 'billingProvider.name':
      return (
        <section>
          <div className="w-full grid grid-cols gap-4 mt-2">
            <div className="grid grid-cols-2 gap-4">
              <FormField disabled={isLoading} name="billingProvider.name" label="NAME" />
              <FormField
                disabled={isLoading}
                name="billingProvider.phoneNumber"
                label="PHONE NUMBER"
                type="phone"
              />
            </div>
            <FormField disabled={isLoading} name="billingProvider.address" label="ADDRESS" />
            <CityZipCodeSearch
              city="billingProvider.city"
              state="billingProvider.state"
              zipCode="billingProvider.zipCode"
              disabled={isLoading}
            />
          </div>
          <div className="grid grid-cols-2 gap-4 mt-2">
            <FormField disabled={isLoading} name="billingProvider.npi" label="A." />
            <FormField disabled={isLoading} name="billingProvider.otherId" label="B." />
          </div>
        </section>
      );
    case 'billingProvider.address':
      return (
        <FormField
          disabled={isLoading}
          name="billingProvider.address"
          label="BILLING PROVIDER ADDRESS"
          className="w-full"
        />
      );
    case 'billingProvider.phoneNumber':
      return (
        <FormField
          disabled={isLoading}
          name="billingProvider.phoneNumber"
          label="BILLING PROVIDER PHONE NUMBER"
          type="phone"
          className="w-full"
        />
      );
    case 'billingProvider.npi':
      return (
        <FormField
          disabled={isLoading}
          name="billingProvider.npi"
          label="BILLING PROVIDER NPI"
          className="w-full"
        />
      );
    case 'billingProvider.otherId':
      return (
        <FormField
          disabled={isLoading}
          name="billingProvider.otherId"
          label="BILLING PROVIDER OTHER ID"
          className="w-full"
        />
      );

    case 'reservedForNUCCUse':
      return (
        <FormField
          name=" "
          className="border-none bg-transparent"
          disabled
          label="RESERVED FOR NUCC USE"
        />
      );

    case fieldName.match(/^supplementalInformation\[\d+\]$/)?.input: {
      const index = parseInt(fieldName.match(/\[(\d+)\]/)?.[1] || '0');

      const specificSupplementalInfo = initialValues?.supplementalInformation?.[index] || {
        _id:
          initialValues?.supplementalInformation?.[0]?._id ||
          initialValues?._id ||
          `temp_${Date.now()}_${index}`,
      };

      return (
        <SuplementalInformationEdit
          initialValue={specificSupplementalInfo}
          supplementalInfo={supplementalInfo}
          disabled={isLoading}
          onSupplementalInfoChange={newInfo => {
            setSupplementalInfo(newInfo);

            if (formikProps) {
              formikProps.setFieldValue('supplementalInformation', newInfo);
            }
          }}
        />
      );
    }

    default:
      return <div className="text-red-500">Campo no encontrado: {fieldName}</div>;
  }
};
