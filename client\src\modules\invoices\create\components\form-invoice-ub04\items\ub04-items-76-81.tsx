import { FormField } from '@/components';
import { PhysicianSearch } from '../searchs';

export function UB04Items76_81({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 p-4">
      <h3 className="text-lg font-bold mb-4">Physician Information (Fields 76-81)</h3>

      <div className="grid grid-cols-3 gap-6">
        {/* Attending Physician */}
        <div>
          <h4 className="font-semibold mb-3">76-77. Attending Physician</h4>
          <div className="space-y-3">
            <PhysicianSearch name="attendingPhysician.name" label="Name" disabled={isLoading} />
            <FormField name="attendingPhysician.npi" label="NPI" disabled={isLoading} />
            <FormField name="attendingPhysician.upin" label="UPIN" disabled={isLoading} />
            <FormField
              name="attendingPhysician.qualifier"
              label="Qualifier"
              type="select"
              options={[
                { value: 'DN', label: 'Referring Provider' },
                { value: 'ZZ', label: 'Mutually Defined' },
                { value: '1G', label: 'Attending' },
              ]}
              disabled={isLoading}
            />
          </div>
        </div>

        {/* Operating Physician */}
        <div>
          <h4 className="font-semibold mb-3">78-79. Operating Physician</h4>
          <div className="space-y-3">
            <PhysicianSearch name="operatingPhysician.name" label="Name" disabled={isLoading} />
            <FormField name="operatingPhysician.npi" label="NPI" disabled={isLoading} />
            <FormField name="operatingPhysician.upin" label="UPIN" disabled={isLoading} />
            <FormField
              name="operatingPhysician.qualifier"
              label="Qualifier"
              type="select"
              options={[
                { value: 'DN', label: 'Referring Provider' },
                { value: 'ZZ', label: 'Mutually Defined' },
                { value: '1G', label: 'Operating' },
              ]}
              disabled={isLoading}
            />
          </div>
        </div>

        {/* Other Physician */}
        <div>
          <h4 className="font-semibold mb-3">80-81. Other Physician</h4>
          <div className="space-y-3">
            <PhysicianSearch name="otherPhysician.name" label="Name" disabled={isLoading} />
            <FormField name="otherPhysician.npi" label="NPI" disabled={isLoading} />
            <FormField name="otherPhysician.upin" label="UPIN" disabled={isLoading} />
            <FormField
              name="otherPhysician.qualifier"
              label="Qualifier"
              type="select"
              options={[
                { value: 'DN', label: 'Referring Provider' },
                { value: 'ZZ', label: 'Mutually Defined' },
                { value: '1G', label: 'Other' },
              ]}
              disabled={isLoading}
            />
          </div>
        </div>
      </div>

      <div className="mt-6">
        <h4 className="font-semibold mb-3">Additional Information</h4>
        <div className="grid grid-cols-2 gap-4">
          <FormField name="remarks" label="Remarks" textarea disabled={isLoading} />
          <FormField name="certificationNumber" label="Certification Number" disabled={isLoading} />
        </div>
      </div>
    </section>
  );
}
