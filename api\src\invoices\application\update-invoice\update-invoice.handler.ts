import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';
import { UpdateInvoiceCommand } from './update-invoice.command';
import { Invoice } from 'src/invoices/infrastructure/schema/invoice.schema';

@CommandHandler(UpdateInvoiceCommand)
export class UpdateInvoiceHandler
  implements ICommandHandler<UpdateInvoiceCommand>
{
  constructor(private readonly invoiceRepository: InvoiceRepository) {}
  async execute(command: UpdateInvoiceCommand) {
    const { id, ...data } = command;
    const totalCharge = this.calculateTotalCharge(
      data.invoiceData.supplementalInformation,
    );
    return this.invoiceRepository.update(id, {
      ...data.invoiceData,
      totalCharge,
    });
  }

  private calculateTotalCharge(
    supplementalInformation: Invoice['supplementalInformation'],
  ) {
    let totalCharge = 0;
    if (!supplementalInformation || supplementalInformation.length === 0) {
      return totalCharge;
    }

    for (const info of supplementalInformation) {
      if (!info.charges) {
        continue;
      }
      totalCharge += +info.charges;
    }
    return totalCharge;
  }
}
