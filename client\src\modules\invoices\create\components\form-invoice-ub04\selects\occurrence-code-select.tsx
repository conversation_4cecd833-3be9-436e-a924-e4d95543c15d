import { FormField } from '@/components';

interface OccurrenceCodeSelectProps {
  name: string;
  disabled?: boolean;
}

export function OccurrenceCodeSelect({ name, disabled }: OccurrenceCodeSelectProps) {
  const occurrenceCodes = [
    { value: '01', label: 'Accident/Medical Coverage' },
    { value: '02', label: 'No Fault Insurance Including Auto/Other' },
    { value: '03', label: 'Accident/Tort Liability' },
    { value: '04', label: 'Accident/Employment Related' },
    { value: '05', label: 'Accident/No Medical or Other Liability Coverage' },
    { value: '06', label: 'Crime Victim' },
    { value: '09', label: 'Start of Infertility Treatment Cycle' },
    { value: '10', label: 'Last Menstrual Period' },
    { value: '11', label: 'Onset of Symptoms/Illness' },
    { value: '12', label: 'Date of Onset for Chronically Dependent Individual' },
    { value: '17', label: 'Date Outpatient Occupational Therapy Plan Established' },
    { value: '18', label: 'Date of Retirement Patient/Beneficiary' },
    { value: '19', label: 'Date of Retirement Spouse' },
    { value: '20', label: 'Guarantee of Payment Date' },
    { value: '21', label: 'UR Notice Received' },
    { value: '22', label: 'Date Active Care Ended' },
    { value: '24', label: 'Date Insurance Denied' },
    { value: '25', label: 'Date Benefits Terminated by Primary Payer' },
    { value: '26', label: 'Date SNF Bed Became Available' },
    { value: '27', label: 'Date of Hospice Certification or Re-certification' },
    { value: '28', label: 'Date Comprehensive Outpatient Rehabilitation Plan Established' },
    { value: '29', label: 'Date Outpatient Physical Therapy Plan Established' },
    { value: '30', label: 'Date Outpatient Speech Pathology Plan Established' },
    { value: '31', label: 'Date Beneficiary Notified of Intent to Bill' },
    { value: '32', label: 'Date Beneficiary Notified of Intent to Bill' },
    { value: '33', label: 'First Day of the Medicare Coordination Period' },
    { value: '34', label: 'Date of Election of Extended Care Services' },
    { value: '35', label: 'Date Treatment Started for Speech Therapy' },
    { value: '36', label: 'Date of Inpatient Hospital Discharge for Covered Transplant Patient' },
    {
      value: '37',
      label: 'Date of Inpatient Hospital Discharge for Non-covered Transplant Patient',
    },
    { value: '40', label: 'Scheduled Date of Admission' },
    { value: '41', label: 'Date of First Test for Pre-admission Testing' },
    { value: '42', label: 'Date of Discharge' },
    { value: '43', label: 'Scheduled Date of Canceled Surgery' },
    { value: '44', label: 'Date Treatment Started for Occupational Therapy' },
    { value: '45', label: 'Date Treatment Started for Speech Therapy' },
    { value: '46', label: 'Date Treatment Started for Cardiac Rehabilitation' },
    { value: '47', label: 'Date Cost Outlier Status Begins' },
    { value: '50', label: 'Date Lien Released' },
    { value: '51', label: 'Date Treatment Started for Physical Therapy' },
    { value: '70', label: 'Qualifying Stay Dates' },
    { value: '71', label: 'Prior Stay Dates' },
    { value: '72', label: 'First/Last Visit' },
    { value: 'A1', label: 'Birthdate - Insured A' },
    { value: 'A2', label: 'Effective Date - Insured A Policy' },
    { value: 'A3', label: 'Benefits Exhausted Payer A' },
    { value: 'A4', label: 'Split Bill Date' },
    { value: 'B1', label: 'Birthdate - Insured B' },
    { value: 'B2', label: 'Effective Date - Insured B Policy' },
    { value: 'B3', label: 'Benefits Exhausted Payer B' },
    { value: 'C1', label: 'Birthdate - Insured C' },
    { value: 'C2', label: 'Effective Date - Insured C Policy' },
    { value: 'C3', label: 'Benefits Exhausted Payer C' },
    { value: 'P1', label: 'Diagnosis and/or Treatment Code' },
    { value: 'P2', label: 'Diagnosis Code - Comorbidity' },
    { value: 'P3', label: 'Diagnosis Code - Complication' },
  ];

  return (
    <FormField
      name={name}
      type="select"
      options={occurrenceCodes}
      disabled={disabled}
      placeholder="Select Occurrence Code"
    />
  );
}
