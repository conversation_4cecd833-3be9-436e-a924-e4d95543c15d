/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect } from 'react';
import { Label } from 'react-aria-components';
import { FieldDatePicker, FormField } from '@/components';
import { ItemEPSDT, YesOrNot } from '@/enums';
import {
  CPTSearch,
  ModifierCodeSearch,
  PlaceOfServiceSearch,
} from '@/modules/invoices/create/components/form-invoice-type-a/searchs';
import { SelectItem24H } from '@/modules/invoices/create/components/form-invoice-type-a/selects';
import { DenialReasonSelect } from './denial-reason-select';

const { Yes, No } = YesOrNot;

const createEmptySupplementalItem = (_id?: string) => ({
  _id: _id || undefined,
  fromDateOfService: null,
  toDateOfService: null,
  placeOfService: '',
  emergencyIndicator: No,
  proceduresCode: '',
  proceduresModifier: ['', '', '', ''],
  diagnosisPointer: '',
  charges: undefined,
  daysOrUnits: undefined,
  epsdtFamilyPlan: ItemEPSDT.NotUsed,
  idQualifier: '',
  renderingProviderId: '',
  denialReason: '',
});

interface ISuplementalInformationEdit {
  disabled: boolean;
  supplementalInfo: any;
  onSupplementalInfoChange: (newSupplementalInfo: any) => void;
  initialValue?: any;
}
export function SuplementalInformationEdit({
  disabled,
  supplementalInfo,
  onSupplementalInfoChange,
  initialValue,
}: ISuplementalInformationEdit) {
  useEffect(() => {
    if (initialValue && !supplementalInfo) {
      const initialData = {
        ...createEmptySupplementalItem(initialValue._id),
        _id: initialValue._id,
        fromDateOfService: initialValue.fromDateOfService || null,
        toDateOfService: initialValue.toDateOfService || null,
        placeOfService: initialValue.placeOfService || '',
        emergencyIndicator: initialValue.emergencyIndicator || No,
        proceduresCode: initialValue.proceduresCode || '',
        proceduresModifier: initialValue.proceduresModifier || ['', '', '', ''],
        diagnosisPointer: initialValue.diagnosisPointer || '',
        charges: initialValue.charges,
        daysOrUnits: initialValue.daysOrUnits,
        epsdtFamilyPlan: initialValue.epsdtFamilyPlan || ItemEPSDT.NotUsed,
        idQualifier: initialValue.idQualifier || '',
        renderingProviderId: initialValue.renderingProviderId || '',
        denialReason: initialValue.denialReason || '',
      };

      onSupplementalInfoChange(initialData);
    } else if (!initialValue && !supplementalInfo) {
      const emptyData = createEmptySupplementalItem();
      onSupplementalInfoChange(emptyData);
    }
  }, [initialValue?._id]);

  const handleFieldChange = (field: string, value: any) => {
    const newSupplementalInfo = { ...supplementalInfo };

    const preservedId = supplementalInfo?._id || initialValue?._id;
    if (preservedId) {
      newSupplementalInfo._id = preservedId;
    }

    if (field === 'charges' || field === 'daysOrUnits') {
      newSupplementalInfo[field] = value ? Number(value) : undefined;
    } else if (field.startsWith('proceduresModifier[')) {
      if (!newSupplementalInfo.proceduresModifier) {
        newSupplementalInfo.proceduresModifier = [];
      }
      const index = parseInt(field.match(/\[(\d+)\]/)?.[1] || '0');
      newSupplementalInfo.proceduresModifier[index] = value;
    } else {
      newSupplementalInfo[field] = value;
    }

    onSupplementalInfoChange(newSupplementalInfo);
  };

  const rowData = supplementalInfo || {
    ...createEmptySupplementalItem(),
    _id: initialValue?._id,
  };

  return (
    <div>
      <div className="relative border border-b-0 border-gray-300">
        {/* Header */}

        <div className="w-full bg-gray-100 border border-gray-300 p-2">
          <div className="flex justify-between items-baseline">
            <strong className="text-sm text-primary-500 font-bold">
              <small className="text-red-500">*</small> 24. SERVICE LINE
            </strong>
          </div>
        </div>
        {/* Column Headers */}
        <div
          className="grid grid-cols-12 bg-gray-50 border-x border-gray-300 text-xs font-semibold"
          style={{ gridTemplateColumns: '136px 136px 84px 80px 320px repeat(7, 1fr)' }}
        >
          <div className="col-span-2 p-2 border-r border-gray-300 text-center">
            A. DATE(S) OF SERVICE
            <div className="grid grid-cols-2 gap-2 mt-1">
              <Label className="text-xs">From</Label>
              <Label className="text-xs">To</Label>
            </div>
          </div>
          <div className="!min-w-24 w-fit p-2 border-r border-gray-300 text-center">
            B. PLACE OF SERVICE
          </div>
          <div className="col-span-1 p-2 border-r border-gray-300 text-center">C. EMG</div>
          <div className="col-span-2 p-2 border-r border-gray-300 text-center">
            D. PROCEDURES, SERVICES, OR SUPPLIES
            <div className="grid grid-cols-2 gap-2 mt-1">
              <Label className="text-xs">CPT / HCPCS</Label>
              <Label className="text-xs">Modifier</Label>
            </div>
          </div>
          <div className="col-span-1 p-2 border-r border-gray-300 text-center">
            E. DIAGNOSIS POINTER
          </div>
          <div className="col-span-1 p-2 border-r border-gray-300 text-center">F. $ CHARGES</div>
          <div className="col-span-1 p-2 border-r border-gray-300 text-center">
            G. DAYS OR UNITS
          </div>
          <div className="col-span-1 p-2 border-r border-gray-300 text-center">
            H. EPSDT Family Plan
          </div>
          <div className="col-span-1 p-2 border-r border-gray-300 text-center">I. ID. QUAL.</div>
          <div className="col-span-1 p-2">J. RENDERING PROVIDER ID. #</div>
        </div>
        {/* Single Row */}
        <div
          className="grid grid-cols-12 bg-gray-50 border-x border-gray-300 text-xs font-semibold"
          style={{ gridTemplateColumns: '136px 136px 84px 80px 320px repeat(7, 1fr)' }}
        >
          {/* A. DATE(S) OF SERVICE */}
          <div className="col-span-2">
            <div className=" grid grid-cols-2 gap-2 border border-gray-300 justify-center items-center border-b-transparent p-2">
              <FieldDatePicker
                name="supplementalInformation.fromDateOfService"
                value={rowData.fromDateOfService}
                disabled={disabled}
                className="w-full text-sm"
                onChange={value => {
                  handleFieldChange('fromDateOfService', value);
                }}
                aria-label="From Date of Service"
              />
              <FieldDatePicker
                name="supplementalInformation.toDateOfService"
                value={rowData.toDateOfService}
                disabled={disabled}
                className="w-full text-sm"
                onChange={value => {
                  handleFieldChange('toDateOfService', value);
                }}
                aria-label="To Date of Service"
              />
            </div>
          </div>
          {/* B. PLACE OF SERVICE */}
          <div className=" !min-w-24 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
            <PlaceOfServiceSearch
              key={`place-${rowData.placeOfService || 'empty'}`}
              initialValue={rowData.placeOfService || ''}
              onSelectPlace={code => {
                handleFieldChange('placeOfService', code);
              }}
              disabled={disabled}
              name="supplementalInformation.placeOfService"
            />
          </div>
          {/* C. EMG */}
          <div className=" border border-gray-300 flex justify-center items-center border-b-transparent px-2">
            <div className="flex flex-col justify-center items-center gap-2">
              <div className="flex flex-col justify-between gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="supplementalInformation.emergencyIndicator"
                    value={Yes}
                    checked={
                      rowData.emergencyIndicator === Yes || rowData.emergencyIndicator === 'YES'
                    }
                    onChange={() => {
                      handleFieldChange('emergencyIndicator', Yes);
                    }}
                    disabled={disabled}
                    className="mr-2 flex"
                  />
                  {Yes}
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="supplementalInformation.emergencyIndicator"
                    value={No}
                    checked={
                      rowData.emergencyIndicator === No ||
                      rowData.emergencyIndicator === 'NO' ||
                      !rowData.emergencyIndicator
                    }
                    onChange={() => {
                      handleFieldChange('emergencyIndicator', No);
                    }}
                    disabled={disabled}
                    className="mr-2 flex"
                  />
                  {No}
                </label>
              </div>
            </div>
          </div>
          {/* D. PROCEDURES, SERVICES, OR SUPPLIES */}
          <div className=" col-span-2 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
            <div className="flex justify-around gap-2">
              <CPTSearch
                key={`cpt-${rowData.proceduresCode || 'empty'}`}
                language="en"
                initialValue={rowData.proceduresCode || ''}
                onSelectCode={code => {
                  handleFieldChange('proceduresCode', code);
                }}
                disabled={disabled}
                name="supplementalInformation.proceduresCode"
              />
              <div className="grid grid-cols-4 gap-2">
                {[0, 1, 2, 3].map(modIndex => (
                  <ModifierCodeSearch
                    key={`modifier-${modIndex}-${(rowData.proceduresModifier && rowData.proceduresModifier[modIndex]) || 'empty'}`}
                    initialValue={
                      (rowData.proceduresModifier && rowData.proceduresModifier[modIndex]) || ''
                    }
                    onSelectModifierCode={code =>
                      handleFieldChange(`proceduresModifier[${modIndex}]`, code)
                    }
                    disabled={disabled}
                    language="en"
                  />
                ))}
              </div>
            </div>
          </div>
          {/* E. DIAGNOSIS POINTER */}
          <div className=" col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
            <FormField
              name="supplementalInformation.diagnosisPointer"
              value={rowData.diagnosisPointer || ''}
              disabled={false}
              className="w-full text-sm p-0"
              maxLength={4}
              onChange={e => handleFieldChange('diagnosisPointer', e.target.value)}
              aria-label="Diagnosis Pointer"
              viewLabel={false}
            />
          </div>
          {/* F. $ CHARGES */}
          <div className=" col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
            <FormField
              name="supplementalInformation.charges"
              value={rowData.charges || ''}
              type="number"
              className="w-full text-sm"
              disabled={disabled}
              onChange={e => handleFieldChange('charges', e.target.value)}
              aria-label="$ Charges"
              viewLabel={false}
            />
          </div>
          {/* G. DAYS OR UNITS */}
          <div className=" col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
            <FormField
              name="supplementalInformation.daysOrUnits"
              value={rowData.daysOrUnits || ''}
              type="number"
              className="w-full text-sm"
              disabled={false}
              onChange={e => handleFieldChange('daysOrUnits', e.target.value)}
              aria-label="Days or Units"
              viewLabel={false}
            />
          </div>
          {/* H. EPSDT/Family Plan */}
          <div className=" col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
            <SelectItem24H
              key={`epsdt-${rowData.epsdtFamilyPlan || 'empty'}`}
              disabled={disabled}
              className="w-full text-sm flex justify-center items-center"
              value={(rowData.epsdtFamilyPlan as ItemEPSDT) || ''}
              name="supplementalInformation.epsdtFamilyPlan"
              onChange={(value: ItemEPSDT) => handleFieldChange('epsdtFamilyPlan', value)}
            />
          </div>
          {/* I. ID QUAL */}
          <div className=" col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
            <FormField
              name="supplementalInformation.idQualifier"
              value={rowData.idQualifier || ''}
              disabled={false}
              className="w-full text-sm"
              onChange={e => handleFieldChange('idQualifier', e.target.value)}
              aria-label="ID Qualifier"
              viewLabel={false}
            />
          </div>
          {/* J. RENDERING PROVIDER ID. # */}
          <div className=" col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
            <FormField
              name="supplementalInformation.renderingProviderId"
              value={rowData.renderingProviderId || ''}
              disabled={false}
              className="w-full text-sm"
              onChange={e => handleFieldChange('renderingProviderId', e.target.value)}
              aria-label="Rendering Provider ID"
              viewLabel={false}
            />
          </div>
        </div>
      </div>
      <h4 className="mt-4 font-medium text-lg">Seleccione el motivo de la denegación</h4>
      <DenialReasonSelect
        key={`denial-${rowData.denialReason || 'empty'}`}
        name="denialReason"
        value={rowData.denialReason || ''}
        onChange={value => handleFieldChange('denialReason', value)}
      />
    </div>
  );
}
