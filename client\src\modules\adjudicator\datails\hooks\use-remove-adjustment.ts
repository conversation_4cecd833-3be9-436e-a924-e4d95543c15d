import { useMutation } from '@tanstack/react-query';
import { AxiosResponseError } from '@/config/http';
import { useShowError, useShowSuccess } from '@/hooks';
import { Http } from '@/config/http';
import { useGetInvoice } from './use-get-invoice';

export const useRemoveAdjustment = () => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateAdjustments } = useGetInvoice();

  const {
    mutate: removeAdjustment,
    isPending: isRemoving,
    ...rest
  } = useMutation({
    mutationKey: ['remove_adjustment'],
    mutationFn: ({ originalInvoiceId }: { originalInvoiceId: string }) =>
      Http.delete(`/invoices/${originalInvoiceId}/remove-adjustment`),
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Todos los ajustes han sido descartados.' });
      invalidateAdjustments();
    },
  });

  return { removeAdjustment, isRemoving, ...rest };
};
