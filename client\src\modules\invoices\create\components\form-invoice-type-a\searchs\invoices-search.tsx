import { SetStateAction, useState, useEffect } from 'react';
import { SearchField, Label } from 'react-aria-components';
import { Input } from '@digheontech/digh.ui';
import { InvoiceType } from '@/enums';
import { FaSearch } from 'react-icons/fa';

const invoiceOptions = Object.values(InvoiceType).map(invoice => ({
  id: invoice,
  label: invoice,
}));

interface IInvoicesSearch {
  onSelectInvoice: (invoiceType: string) => void;
  initialValue: string;
}

export function InvoicesSearch({ initialValue, onSelectInvoice }: IInvoicesSearch) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  const [inputValue, setInputValue] = useState(initialValue || '');

  useEffect(() => {
    setInputValue(initialValue || '');
    setSearchTerm(initialValue || '');
  }, [initialValue]);

  useEffect(() => {
    if (initialValue) {
      onSelectInvoice(initialValue);
    }
  }, [initialValue, onSelectInvoice]);

  const handleSearch = (value: SetStateAction<string>) => {
    const newValue = value.toString();
    setSearchTerm(newValue);
    setInputValue(newValue);
    setShowResults(!!newValue);
  };

  const handleSelectInvoice = (invoice: string) => {
    setSearchTerm(invoice);
    setInputValue(invoice);
    setShowResults(false);
    onSelectInvoice(invoice);
  };

  const filteredInvoices = invoiceOptions.filter(invoice =>
    invoice.label.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <div className="w-full max-w-md relative">
      <Label className="font-bold">Buscar tipo de factura</Label>
      <SearchField
        value={searchTerm}
        onChange={handleSearch}
        className="relative flex items-center w-full mt-2"
        aria-label="Tipo de factura"
      >
        <Input
          aria-label="Tipo de factura"
          value={searchTerm}
          onChange={e => handleSearch(e.target.value)}
          placeholder="Número de factura: 1500"
          className="w-full outline-none text-gray-700 placeholder-gray-400 p-4 cursor-pointer"
        />
      </SearchField>

      {showResults && filteredInvoices.length > 0 && (
        <ul className="absolute w-full mt-1 bg-white border border-gray-200 rounded-lg divide-y divide-gray-200 shadow-lg z-10">
          {filteredInvoices.map(invoice => (
            <li
              key={invoice.id}
              className="px-4 py-2 text-gray-700 cursor-pointer hover:bg-gray-100"
              onClick={() => handleSelectInvoice(invoice.label)}
            >
              <div className="flex justify-start gap-4">
                <small>
                  <strong>Tipo:</strong> {invoice.id}
                </small>
              </div>
            </li>
          ))}
        </ul>
      )}

      {searchTerm && !showResults && filteredInvoices.length === 0 && (
        <p className="mt-2 text-gray-500">No se encontraron facturas.</p>
      )}
    </div>
  );
}
