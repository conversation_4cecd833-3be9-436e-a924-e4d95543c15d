declare module 'react-signature-canvas' {
  import * as React from 'react';

  export interface ReactSignatureCanvasProps {
    canvasProps?: React.CanvasHTMLAttributes<HTMLCanvasElement>;
    clearOnResize?: boolean;
    penColor?: string;
    velocityFilterWeight?: number;
    minWidth?: number;
    maxWidth?: number;
    dotSize?: number;
    minDistance?: number;
    onEnd?: () => void;
  }

  export default class SignatureCanvas extends React.Component<ReactSignatureCanvasProps> {
    clear(): void;
    fromDataURL(dataUrl: string): void;
    getTrimmedCanvas(): HTMLCanvasElement;
    toDataURL(type?: string): string;
    getSignaturePad(): any;
  }
}
