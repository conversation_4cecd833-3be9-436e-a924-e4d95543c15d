import { v4 as uuid } from 'uuid';
import * as multerS3 from 'multer-s3';
import type { StorageEngine } from 'multer';
import { Injectable } from '@nestjs/common';
import { S3Client } from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';
import {
  MulterModuleOptions,
  MulterOptionsFactory,
} from '@nestjs/platform-express';

@Injectable()
export class MulterConfigService implements MulterOptionsFactory {
  static readonly MAX_FILE_SIZE_IN_BYTES = 1e7; // 10MB
  private readonly storage: StorageEngine;

  constructor(private readonly config: ConfigService) {
    this.storage = this.createStorageEngine();
  }

  private createStorageEngine(): StorageEngine {
    return multerS3({
      s3: this.getS3Client(),
      contentType: (_, file, callback) => {
        callback(null, file.mimetype);
      },
      acl: 'private',
      bucket: this.config.get('aws.bucket'),
      key: (_, __, callback) => callback(null, uuid()),
      metadata: (_, __, callback) => callback(null, {}),
    });
  }

  private getS3Client() {
    return new S3Client({
      credentials: {
        accessKeyId: this.config.get('aws.accessKey'),
        secretAccessKey: this.config.get('aws.secretAccessKey'),
      },
      region: this.config.get('aws.region'),
    });
  }

  createMulterOptions(): MulterModuleOptions {
    return {
      storage: this.storage,
      limits: {
        fileSize: MulterConfigService.MAX_FILE_SIZE_IN_BYTES,
      },
    };
  }
}
