'use client';

import { Table } from '@digheontech/digh.ui';
import { ReactElement, ReactNode, useMemo, useState } from 'react';
import { LuSearch } from 'react-icons/lu';

import { Status, Empty } from '@/components';
import { ProviderStatus, UserRole } from '@/enums';
import { IProviderUsersTable } from '@/interfaces';
import { getProviderStatusLabel } from '@/tools';
import { useGetUsers } from '../hooks/use-get-users';
import { AdjudicatorModal } from './create/adjudicator-modal';
import { getUserRoleLabel } from '@/tools/getUserRoleLabel';

interface TableColumn {
  name: string;
  id: keyof IProviderUsersTable | 'name';
  isRowHeader?: boolean;
  className?: string;
}

interface TableRow {
  key: string;
  id: string;
  name: ReactNode;
  lastName: ReactNode;
  email: ReactNode;
  role: ReactNode;
  status: ReactNode;
}

const rowClassName = 'text-sm font-light';
const columnClassName = 'text-sm font-bold';

const STATUS_COLORS: Record<ProviderStatus, string> = {
  [ProviderStatus.Active]: 'bg-blue-400',
  [ProviderStatus.Inactive]: 'bg-red-400',
  [ProviderStatus.Unlinked]: 'bg-slate-400',
};

const colorStatus = (status: ProviderStatus): ReactElement => (
  <Status className={STATUS_COLORS[status]} />
);

export const UsersTable = () => {
  const { usersProviders, isLoading } = useGetUsers();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [roleFilter, setRoleFilter] = useState<string>('');

  const columns: TableColumn[] = useMemo(
    () => [
      {
        isRowHeader: true,
        name: 'Nombre',
        id: 'name',
        className: columnClassName,
      },
      { name: 'Apellido', id: 'lastName', className: columnClassName },
      { name: 'Correo', id: 'email', className: columnClassName },
      { name: 'Roles', id: 'role', className: columnClassName },
      { name: 'Estado', id: 'status', className: columnClassName },
    ],
    [],
  );

  const filteredUsers = useMemo(() => {
    if (!usersProviders) return [];

    return usersProviders.filter(user => {
      const searchMatch =
        !searchTerm ||
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase());

      const statusMatch = !statusFilter || user.status === statusFilter;

      const roleMatch = !roleFilter || user.role === roleFilter;

      return searchMatch && statusMatch && roleMatch;
    });
  }, [usersProviders, searchTerm, statusFilter, roleFilter]);

  const rows: TableRow[] = useMemo(
    () =>
      filteredUsers?.map(({ name, lastName, email, role, status }) => ({
        key: name,
        id: name,
        name: <span className={rowClassName}>{name}</span>,
        lastName: <span className={rowClassName}>{lastName}</span>,
        email: <span className={rowClassName}>{email}</span>,
        role: (
          <span className={rowClassName}>
            {role ? getUserRoleLabel(role as UserRole) : '- - -'}
          </span>
        ),
        status: (
          <div className="flex items-center justify-start gap-2">
            {colorStatus(status)}
            {getProviderStatusLabel(status)}
          </div>
        ),
      })) || [],
    [filteredUsers],
  );

  return (
    <main>
      <header className="flex justify-between items-center py-4">
        <h1 className="text-xl font-semibold text-primary-600">Usuarios del sistema</h1>
        <AdjudicatorModal />
      </header>

      <div className="mb-4 flex flex-wrap gap-4 items-center">
        {/* Buscador */}
        <div className="relative flex-1 min-w-[200px]">
          <div className="flex justify-start items-center gap-2 border border-primary-300 rounded-md w-[310px]">
            <input
              type="text"
              placeholder="Buscar nombre, apellido o correo..."
              className="w-[300px] ml-8 p-2 border-none focus:border-none focus:outline-none rounded-md"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
            <div className="absolute left-0-20 top-2">
              <LuSearch className="text-gray-400 text-2xl" aria-label="Buscar" />
            </div>
          </div>
        </div>

        <div className="min-w-[150px]">
          <select
            className="w-full p-2 border border-gray-300 rounded-md"
            value={statusFilter}
            onChange={e => setStatusFilter(e.target.value)}
          >
            <option value="">Todos los estados</option>
            <option value={ProviderStatus.Active}>Activo</option>
            <option value={ProviderStatus.Inactive}>Inactivo</option>
            <option value={ProviderStatus.Unlinked}>No vinculado</option>
          </select>
        </div>

        <div className="min-w-[150px]">
          <select
            className="w-full p-2 border border-gray-300 rounded-md"
            value={roleFilter}
            onChange={e => setRoleFilter(e.target.value)}
          >
            <option value="">Todos los roles</option>
            <option value={UserRole.Provider}>Proveedor</option>
            <option value={UserRole.SuperAdmin}>Administrador</option>
            <option value={UserRole.Adjudicator}>Adjudicador</option>
          </select>
        </div>
      </div>

      <div className="mt-6 bg-white p-5 rounded-md">
        <div className="flex justify-around flex-col gap-4">
          <Table<TableRow>
            isLoading={isLoading}
            columns={columns}
            rows={rows}
            tableBodyProps={{
              renderEmptyState: () => <Empty />,
            }}
            wrapClasses="max-h-full"
          />
        </div>
      </div>
    </main>
  );
};
