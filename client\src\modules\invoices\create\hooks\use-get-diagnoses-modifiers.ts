import { useQuery } from '@tanstack/react-query';
import { Http } from '@/config/http';

const EP = 'https://test.ehr.digheontech.com/api/diagnoses/modifiers-codes';

export interface IModifierCode {
  code: string;
  description: {
    en: string;
    es: string;
  };
}

export function useGetDiagnosesModifiers(term = '', language: 'es' | 'en' = 'es') {
  const { data: modifierCodes, ...rest } = useQuery<IModifierCode[]>({
    queryKey: ['diagnoses-modifiers', term, language],
    queryFn: () =>
      Http.get(`${EP}?language=${language}&term=${encodeURIComponent(term)}`).then(
        ({ data }) => data,
      ),
    enabled: !!term,
  });

  return { modifierCodes, ...rest };
}
