'use client';

import Link from 'next/link';
import { RedirectButton } from '../redirect-button';
import { TiArrowBack } from 'react-icons/ti';
import { Button } from '@digheontech/digh.ui';

interface IHeaderWithButtons {
  textPath?: string;
  path?: string;
  back?: string;
  textBack?: string;
}
export function HeaderWithButtons({ path, textPath, back, textBack }: IHeaderWithButtons) {
  return (
    <header className="flex justify-between items-center">
      {back && (
        <Link href={back}>
          <Button
            buttonType="normal"
            leftIcon={<TiArrowBack size={16} />}
            className="bg-transparent border-none text-slate-600"
          >
            {textBack || 'Atrás'}
          </Button>
        </Link>
      )}
      {path && <RedirectButton path={path}>{textPath}</RedirectButton>}
    </header>
  );
}
