import { FormField } from '@/components';
import { CityZipCodeSearch } from '../searchs/city-zip-code-search';

export function Items32({ disabled }: { disabled: boolean }) {
  return (
    <section className="col-span-1 border border-gray-300 border-y-transparent p-4 hover-card">
      <div className="w-full mt-2">
        <b>32.</b> SERVICE FACILITY LOCATION INFORMATION
      </div>

      <section className="w-full grid grid-cols gap-4 mt-2">
        <FormField disabled={disabled} name="serviceFacilityLocation.name" label="NAME" />
        <FormField disabled={disabled} name="serviceFacilityLocation.address" label="ADDRESS" />

        <CityZipCodeSearch
          state="serviceFacilityLocation.state"
          city="serviceFacilityLocation.city"
          zipCode="serviceFacilityLocation.zipCode"
          disabled={disabled}
        />
      </section>
      <section className="grid grid-cols-2 gap-4 mt-2">
        <FormField disabled={disabled} name="serviceFacilityLocation.npi" label="A." />
        <FormField disabled={disabled} name="serviceFacilityLocation.otherId" label="B." />
      </section>
    </section>
  );
}
