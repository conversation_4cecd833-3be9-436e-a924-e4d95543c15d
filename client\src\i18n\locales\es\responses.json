{"SUCCESS": {"CREATE_USERS": {"title": "<PERSON><PERSON><PERSON> usuario", "description": "Usuario creado con éxito"}}, "ERRORS": {"unknown": "Ha ocurrido un error.", "alreadyExists": "Ya existe.", "byHttpCode": {"400": "<PERSON><PERSON> los campos, {{field}}", "401": "No autorizado", "403": "No tienes permiso para realizar esta acción", "404": "Recurso no encontrado", "500": "Error interno del servidor", "502": "Error en la pasarela", "503": "Servicio no disponible", "504": "Tiempo de espera agotado en la pasarela"}, "byDomainCode": {"SESSION_TOKEN_EXPIRED": "Token de sesión ha expirado", "TOKEN_INVALID": "El token es inválido.", "USER_ALREADY_REGISTERED": "Usuario ya registrado."}, "upload": {"required": "Archivo requerido", "unsupported": "Tipo de archivo no soportado", "fileTooLarge": "Archivo demasiado grande"}, "CREATE_USERS": {"title": "Error al crear usuario", "description": "Usuario no creado"}}}