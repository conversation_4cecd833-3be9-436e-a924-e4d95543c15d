import * as yup from 'yup';

import i18next from 'i18next';

const getErrorMessage = (key: string) => () => i18next.t(key, { ns: 'password-validations' });

interface IValidation {
  regExp: RegExp;
  message: yup.Message;
}

const validationPattern: { [key: string]: IValidation } = {
  lowerCase: {
    regExp: /[a-z]/,
    message: getErrorMessage('password.lowerCase'),
  },
  upperCase: {
    regExp: /[A-Z]/,
    message: getErrorMessage('password.upperCase'),
  },
  numbers: {
    regExp: /\d/,
    message: getErrorMessage('password.numbers'),
  },
  symbols: {
    regExp: /[@#$%^&*\-_!+=[\]{}|\\:',.?/`~"()<>;]/,
    message: getErrorMessage('password.symbols'),
  },
};

const PASSWORD_MIN_LENGTH = 8;
const PASSWORD_LENGTH_MESSAGE = getErrorMessage('password.min');

const passwordValidation = yup
  .string()
  .required(getErrorMessage('password.required'))
  .min(PASSWORD_MIN_LENGTH, PASSWORD_LENGTH_MESSAGE)
  .matches(validationPattern.lowerCase.regExp, validationPattern.lowerCase.message)
  .matches(validationPattern.upperCase.regExp, validationPattern.upperCase.message)
  .matches(validationPattern.numbers.regExp, validationPattern.numbers.message)
  .matches(validationPattern.symbols.regExp, validationPattern.symbols.message);

const confirmPasswordValidation = yup
  .string()
  .required(getErrorMessage('passwordConfirmation.required'))
  .oneOf([yup.ref('password'), ''], getErrorMessage('passwordConfirmation.match'));

export { passwordValidation, confirmPasswordValidation };
