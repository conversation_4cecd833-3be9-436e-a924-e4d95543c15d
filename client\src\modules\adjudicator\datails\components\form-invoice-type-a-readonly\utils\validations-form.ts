import * as Yup from 'yup';
import {
  Gender,
  InsuranceCarrier,
  Item14Qualifier,
  Item15Qualifier,
  Item17Role,
  Relationship,
  YesOrNot,
  Item22ResubmissionCode,
} from '@/enums';
import { IFormInvoiceTypeAPayload } from '@/interfaces';

const { OTHER } = InsuranceCarrier;

export const initialValues: IFormInvoiceTypeAPayload = {
  insuranceCarrier: OTHER,
  insuranceIdNumber: '',
  patientRelationshipToInsured: undefined,
  patient: {
    fullName: '',
    gender: undefined,
    phoneNumber: '',
    birthDate: undefined,
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
    },
  },
  insured: {
    fullName: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
    },
    phoneNumber: '',
    policyGroupNumber: '',
    birthDate: undefined,
    gender: undefined,
    planOrProgramName: '',
    anotherHealthBenefitPlan: false,
  },
  serviceFacilityLocation: {
    name: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    npi: '',
    otherId: '',
  },
  billingProvider: {
    name: '',
    phoneNumber: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    npi: '',
    otherId: '',
  },
  conditionPatientRelatedToAutoAccident: false,
  conditionPatientRelatedToAutoAccidentPlace: '',
  conditionPatientRelatedToEmployment: false,
  conditionPatientRelatedToOtherAccident: false,
  dateOfCurrentIllnessInjuryPregnancy: undefined,
  hospitalizationFromDate: undefined,
  hospitalizationToDate: undefined,
  insuredOrAuthorizedSignature: '',
  isOutsideLab: false,
  otherDateConditionOfIllnessOrTreatment: undefined,
  otherInsuredName: '',
  otherInsuredPlanNameOrProgramName: '',
  otherInsuredPolicyOrGroupNumber: '',
  outsideLabCharges: undefined,
  patientOrAuthorizedSignature: '',
  patientOrAuthorizedSignatureDate: undefined,
  qualifierOfCurrentIllnessInjuryAccident: undefined,
  qualifierOfOtherConditionOfIllnessOrTreatment: undefined,
  referringProviderName: '',
  referringProviderNpi: '',
  referringProviderOtherId: '',
  referringProviderOtherIdQualifier: '',
  referringProviderQualifier: undefined,
  unableToWorkFromDate: undefined,
  unableToWorkToDate: undefined,
  originalReferenceNumber: '',
  priorAuthorizationNumber: '',
  resubmissionCode: undefined,
  icd10DiagnosisCodesForDiseasesOrInjuries: [],
  supplementalInformation: [],
  federalTaxIdNumber: '',
  federalTaxIdNumberType: undefined,
  patientAccountNumber: '',
  acceptAssignment: false,
  totalCharge: undefined,
  amountPaid: undefined,
  physicianSignature: '',
  physicianSignatureDate: undefined,
  invoiceAdjustments: null,
};

export const validationSchema = Yup.object().shape({
  insuranceCarrier: Yup.mixed<InsuranceCarrier>().oneOf(Object.values(InsuranceCarrier)).optional(),
  insuranceIdNumber: Yup.string().trim().optional().max(29, 'Máximo 29 caracteres'),

  patientRelationshipToInsured: Yup.mixed<Relationship>()
    .oneOf(Object.values(Relationship))
    .optional(),
  patient: Yup.object().shape({
    fullName: Yup.string().optional().max(50, 'Máximo 50 caracteres'),
    birthDate: Yup.date().optional(),
    gender: Yup.mixed<Gender>().oneOf(Object.values(Gender)).optional(),
    address: Yup.object().shape({
      street: Yup.string().optional(),
      city: Yup.string().optional(),
      state: Yup.string().optional(),
      zipCode: Yup.string().optional(),
    }),
    phoneNumber: Yup.string().optional().max(13, 'Máximo 13 caracteres'),
  }),

  insured: Yup.object().shape({
    fullName: Yup.string().optional().max(50, 'Máximo 50 caracteres'),
    address: Yup.object().shape({
      street: Yup.string().optional(),
      city: Yup.string().optional(),
      state: Yup.string().optional(),
      zipCode: Yup.string().optional(),
    }),
    phoneNumber: Yup.string().optional().max(13, 'Máximo 13 caracteres'),
    policyGroupNumber: Yup.string().optional(),
    birthDate: Yup.date().optional(),
    gender: Yup.mixed<Gender>().oneOf(Object.values(Gender)).optional(),
    planOrProgramName: Yup.string().optional(),
    anotherHealthBenefitPlan: Yup.mixed<YesOrNot>().oneOf(Object.values(YesOrNot)).optional(),
  }),

  serviceFacilityLocation: Yup.object().shape({
    name: Yup.string().optional(),
    address: Yup.string().optional(),
    city: Yup.string().optional(),
    state: Yup.string().optional(),
    zipCode: Yup.string().optional(),
    npi: Yup.string().optional(),
    otherId: Yup.string().optional(),
  }),

  billingProvider: Yup.object().shape({
    name: Yup.string().optional(),
    phoneNumber: Yup.string().optional().max(13, 'Máximo 13 caracteres'),
    address: Yup.string().optional(),
    city: Yup.string().optional(),
    state: Yup.string().optional(),
    zipCode: Yup.string().optional(),
    npi: Yup.string().optional(),
    otherId: Yup.string().optional(),
  }),

  conditionPatientRelatedToAutoAccident: Yup.mixed<YesOrNot>()
    .oneOf(Object.values(YesOrNot))
    .optional(),
  conditionPatientRelatedToAutoAccidentPlace: Yup.string().optional(),
  conditionPatientRelatedToEmployment: Yup.mixed<YesOrNot>()
    .oneOf(Object.values(YesOrNot))
    .optional(),
  conditionPatientRelatedToOtherAccident: Yup.mixed<YesOrNot>()
    .oneOf(Object.values(YesOrNot))
    .optional(),
  dateOfCurrentIllnessInjuryPregnancy: Yup.date().optional(),
  hospitalizationFromDate: Yup.date().optional(),
  hospitalizationToDate: Yup.date().optional(),
  isOutsideLab: Yup.mixed<YesOrNot>().oneOf(Object.values(YesOrNot)).optional(),
  otherDateConditionOfIllnessOrTreatment: Yup.date().optional(),
  otherInsuredName: Yup.string().optional(),
  otherInsuredPlanNameOrProgramName: Yup.string().optional(),
  otherInsuredPolicyOrGroupNumber: Yup.string().optional(),
  outsideLabCharges: Yup.number().optional(),
  patientOrAuthorizedSignatureDate: Yup.date().optional(),
  qualifierOfCurrentIllnessInjuryAccident: Yup.mixed<Item14Qualifier>()
    .oneOf(Object.values(Item14Qualifier))
    .optional(),
  qualifierOfOtherConditionOfIllnessOrTreatment: Yup.mixed<Item15Qualifier>()
    .oneOf(Object.values(Item15Qualifier))
    .optional(),
  referringProviderName: Yup.string().optional(),
  referringProviderQualifier: Yup.mixed<Item17Role>().oneOf(Object.values(Item17Role)).optional(),
  referringProviderOtherId: Yup.string().optional(),
  referringProviderOtherIdQualifier: Yup.string().optional(),
  referringProviderNpi: Yup.string().optional(),
  unableToWorkFromDate: Yup.date().optional(),
  unableToWorkToDate: Yup.date().optional(),
  originalReferenceNumber: Yup.string().optional(),
  priorAuthorizationNumber: Yup.string().optional(),
  resubmissionCode: Yup.mixed<Item22ResubmissionCode>()
    .oneOf(Object.values(Item22ResubmissionCode))
    .optional(),
  icd10DiagnosisCodesForDiseasesOrInjuries: Yup.array().of(Yup.string()).optional(),
  supplementalInformation: Yup.array().of(
    Yup.object().shape({
      fromDateOfService: Yup.date().nullable(),
      toDateOfService: Yup.date().nullable(),
      placeOfService: Yup.string().optional(),
      emergencyIndicator: Yup.string().optional(),
      proceduresCode: Yup.string().optional(),
      proceduresModifier: Yup.array().of(Yup.string().optional()),
      diagnosisPointer: Yup.string().optional(),
      charges: Yup.number()
        .transform((value, originalValue) =>
          originalValue === '' || originalValue === null ? undefined : Number(originalValue),
        )
        .nullable(),
      daysOrUnits: Yup.number()
        .transform((value, originalValue) =>
          originalValue === '' || originalValue === null ? undefined : Number(originalValue),
        )
        .nullable(),
      epsdtFamilyPlan: Yup.string().optional(),
      idQualifier: Yup.string().optional(),
      renderingProviderId: Yup.string().optional(),
    }),
  ),
  federalTaxIdNumber: Yup.string().optional(),
  federalTaxIdNumberType: Yup.string().optional(),
  patientAccountNumber: Yup.string().optional(),
  acceptAssignment: Yup.mixed<YesOrNot>().oneOf(Object.values(YesOrNot)).optional(),
  totalCharge: Yup.number().optional(),
  amountPaid: Yup.number().optional(),
  physicianSignature: Yup.string().optional(),
  physicianSignatureDate: Yup.date().optional(),
});
