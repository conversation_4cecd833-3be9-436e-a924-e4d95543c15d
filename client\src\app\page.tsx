'use client';

import { useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';

import { LoadingFallback } from '@/components/loading-fallback';

import useLoggedUser from '@/hooks/use-logged-user';
import { UserRole } from '@/enums';

export default function Home() {
  const router = useRouter();
  const { loggedUser } = useLoggedUser();
  const { Provider, SuperAdmin, Adjudicator } = UserRole;

  const redirectToDashboard = useCallback(() => {
    if (!loggedUser) {
      router.push('/sign-in');
      return;
    }

    if (loggedUser.role === Provider) {
      router.push('/providers/validate');
      return;
    }

    const roleToRouteMap: Record<UserRole, string> = {
      [SuperAdmin]: '/admin/dashboard',
      [Provider]: '/providers/dashboard',
      [Adjudicator]: '/adjudicator/dashboard',
    };

    const route = roleToRouteMap[loggedUser.role as UserRole];
    if (route) {
      router.push(route);
    }
  }, [loggedUser, Provider, SuperAdmin, Adjudicator, router]);

  useEffect(() => {
    redirectToDashboard();
  }, [redirectToDashboard]);

  return (
    <section className="flex h-screen w-full items-center justify-center">
      <LoadingFallback />
    </section>
  );
}
