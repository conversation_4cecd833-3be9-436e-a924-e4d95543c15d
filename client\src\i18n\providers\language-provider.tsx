'use client';

import { ReactNode, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import '@/i18n';

import { Language } from '../enums';

interface LanguageProviderProps {
  children: ReactNode;
}

const LanguageProvider = ({ children }: LanguageProviderProps) => {
  const { i18n } = useTranslation();

  useEffect(() => {
    const savedLang = localStorage.getItem('lang') || Language.Spanish;
    i18n.changeLanguage(savedLang);
  }, [i18n]);

  return children;
};

export default LanguageProvider;
