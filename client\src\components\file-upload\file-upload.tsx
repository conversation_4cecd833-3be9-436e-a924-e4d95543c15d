'use client';

import { Button } from '@digheontech/digh.ui';
import { useFormikContext } from 'formik';
import { FileTrigger } from 'react-aria-components';
import { MdFeed, MdFileUpload } from 'react-icons/md';

interface IFileUpload {
  name: string;
  typesLabel: string;
  maxSizeLabel: string;
  mimeTypes: string[];
}

export function FileUpload({ name, mimeTypes, typesLabel, maxSizeLabel }: IFileUpload) {
  const formik = useFormikContext();
  const file = (formik.values as any)[name] as File | null;

  const handleFileSelect = (files: FileList | null) => {
    const selectedFile = files && files.length > 0 ? files[0] : null;
    formik.setFieldTouched(name, true);
    formik.setFieldValue(name, selectedFile, true);
  };

  return (
    <FileTrigger defaultCamera="user" onSelect={handleFileSelect} acceptedFileTypes={mimeTypes}>
      <section className="flex flex-col justify-center items-center gap-2">
        {file ? (
          <>
            <MdFeed className="text-teal-600 text-4xl" />
            <p className="text-sm text-slate-600 [overflow-wrap:anywhere]">{file.name}</p>
          </>
        ) : (
          <>
            <Button
              dataCy="file-upload-button"
              className="flex justify-center bg-transparent w-40 border-none hover:!bg-teal-100 hover:!border-teal-400 active:!bg-teal-300 active:!border-teal-300"
              type="button"
            >
              <MdFileUpload className="text-teal-600 text-xl" />
            </Button>

            <p className="text-sm text-slate-600 font-semibold">Seleccione</p>
            <p className="text-sm text-slate-500">
              Tipos de archivo permitidos: <strong>{typesLabel}</strong>
            </p>
            <p className="text-sm text-slate-500">
              Tamaño máximo de archivo: <strong>{maxSizeLabel}</strong>
            </p>
          </>
        )}
      </section>
    </FileTrigger>
  );
}
