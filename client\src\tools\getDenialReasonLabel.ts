import { DenialReason } from '@/enums';

export const getDenialReasonLabel = (reason: DenialReason) => {
  const labels = {
    [DenialReason.AdjustmentNotProceed]: 'Ajuste no procede',
    [DenialReason.DocumentsNotVisible]: 'Documentos no visibles',
    [DenialReason.EvidenceDoesNotMatch]: 'Evidencia médica no concuerda según facturado',
    [DenialReason.MissingOrIncorrectCode]:
      'Falta código de diagnóstico o el código de procedimiento es incorrecto',
    [DenialReason.MissingDischargeSummary]: 'Falta "discharge summary" o "attestation report"',
    [DenialReason.MissingMedicalEvidence]:
      'Falta de evidencia médica (Notas de progreso, Resultados de estudios y/o Nota de procedimiento)',
    [DenialReason.DatesDoNotMatch]: 'Fechas no concuerdan',
    [DenialReason.NotPhysicianCorrectional]:
      'No corresponde a servicios prestados por Physician Correctional',
    [DenialReason.PatientNotBelongs]: 'Paciente no pertenece a Physician Correctional',
    [DenialReason.IncorrectProvider]: 'Proveedor incorrecto',
    [DenialReason.TimeLimitExceeded]: '"Time Limit" excede los 90 días',
  };
  return labels[reason];
};
