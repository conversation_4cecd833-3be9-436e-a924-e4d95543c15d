import { toast } from 'react-toastify';

interface IToast {
  title?: string;
  description?: string | string[];
  type?: 'success' | 'error' | 'info' | 'warning';
}

export const useToast = () => {
  const triggerToast = ({ title, description, type = 'error' }: IToast) => {
    let formattedDescription = '';

    if (Array.isArray(description)) {
      formattedDescription = description.join('\n');
    } else if (typeof description === 'string') {
      formattedDescription = description;
    } else if (description) {
      formattedDescription = JSON.stringify(description);
    }

    const message = title
      ? `${title}${formattedDescription ? ': ' + formattedDescription : ''}`
      : formattedDescription;

    toast[type](message, {
      position: 'top-center',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  return { triggerToast };
};
