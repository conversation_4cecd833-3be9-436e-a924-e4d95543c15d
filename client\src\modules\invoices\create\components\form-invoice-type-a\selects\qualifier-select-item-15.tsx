import { useField } from 'formik';
import { Key } from 'react';
import { <PERSON>ton, ListBox, Popover, Select, SelectValue } from 'react-aria-components';
import { LuChevronsUpDown } from 'react-icons/lu';

import { Item15Qualifier } from '@/enums';
import { StatusItem } from '@/modules/providers/create/components/provider-select-type/status-item';
import { getItem15QualifierLabel } from '@/tools/getItem15QualifierLabel';

const QUALIFIERS = [
  Item15Qualifier.Accident,
  Item15Qualifier.AcuteCondition,
  Item15Qualifier.FirstVisit,
  Item15Qualifier.InitialTreatment,
  Item15Qualifier.LastXRay,
  Item15Qualifier.LatestVisit,
  Item15Qualifier.Prescription,
  Item15Qualifier.ReportEnd,
  Item15Qualifier.ReportStart,
] as const;

interface IQualifierOption {
  qualifier: Item15Qualifier;
}

interface IQualifierSelectItem15 {
  className?: HTMLElement['className'];
}

const QualifierOption = ({ qualifier }: IQualifierOption) => (
  <StatusItem key={qualifier} id={qualifier}>
    <div className="flex gap-2 items-baseline">
      <span>{qualifier}</span> <small>{getItem15QualifierLabel(qualifier)}</small>
    </div>
  </StatusItem>
);

export function QualifierSelectItem15({ className }: IQualifierSelectItem15) {
  const [field, meta, helpers] = useField<Item15Qualifier>(
    'qualifierOfOtherConditionOfIllnessOrTreatment',
  );

  const handleSelectionChange = (key: Key) => {
    helpers.setValue(key as Item15Qualifier);
  };

  return (
    <div className={className}>
      <Select
        aria-label="Qualifier"
        className="flex flex-col gap-1 w-full mb-4"
        selectedKey={field.value}
        onSelectionChange={handleSelectionChange}
        defaultSelectedKey={undefined}
        placeholder="select"
      >
        <Button className="field-shadow h-4">
          <SelectValue className="flex-1 truncate placeholder-shown:italic" aria-label="Select">
            {value => getItem15QualifierLabel(value as unknown as Item15Qualifier)}
          </SelectValue>
          <LuChevronsUpDown />
        </Button>
        <Popover className="max-h-60 w-[--trigger-width] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out">
          <ListBox className="outline-none p-1">
            {QUALIFIERS.map(qualifier => (
              <QualifierOption key={qualifier} qualifier={qualifier} />
            ))}
          </ListBox>
        </Popover>
      </Select>
      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
