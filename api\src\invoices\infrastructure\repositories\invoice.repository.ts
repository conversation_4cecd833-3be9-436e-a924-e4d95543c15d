import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types, UpdateQuery } from 'mongoose';
import { InvoiceStatus } from 'src/shared/enums/invoice-status.enum';

import { Invoice, InvoiceDocument } from '../schema/invoice.schema';

type SearchFilters = {
  status?: InvoiceStatus;
};

type InvoiceSelect = {
  [key in keyof (InvoiceDocument & {
    createdAt: Date;
    updatedAt: Date;
  })]?: boolean;
};

@Injectable()
export class InvoiceRepository {
  constructor(
    @InjectModel('Invoice') private invoiceModel: Model<InvoiceDocument>,
  ) {}

  async create(data: Partial<Invoice>) {
    return this.invoiceModel.create(data);
  }

  async findById(id: string, failIfNotFound = false) {
    const invoice = await this.invoiceModel
      .findById(new Types.ObjectId(id))
      .exec();
    if (!invoice && failIfNotFound) {
      throw new NotFoundException(`Invoice '${id}' not found`);
    }
    return invoice;
  }

  async update(invoiceId: string, data: UpdateQuery<InvoiceDocument>) {
    return this.invoiceModel
      .findByIdAndUpdate(invoiceId, data, {
        new: true,
      })
      .exec();
  }

  async findByStatus(status: InvoiceStatus, failIfNotFound = false) {
    const invoices = await this.invoiceModel.find({ status }).exec();
    if (!invoices && failIfNotFound) {
      throw new NotFoundException(`Invoices with status '${status}' not found`);
    }
    return invoices;
  }

  async search(filters: SearchFilters, select: InvoiceSelect = {}) {
    return this.invoiceModel.find(filters).select(select).exec();
  }

  async delete(invoiceId: string, failIfNotFound = false) {
    const result = await this.invoiceModel.findByIdAndDelete(invoiceId).exec();
    if (!result && failIfNotFound) {
      throw new NotFoundException(`Invoice '${invoiceId}' not found`);
    }
    return result;
  }
}
