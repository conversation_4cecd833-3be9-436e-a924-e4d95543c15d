{"name": "phycorr-claims", "version": "0.1.0", "private": true, "engines": {"node": ">=22.13.0"}, "engineStrict": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "prepare": "husky install", "commit-msg": "commitlint --edit $1", "test": "jest --passWithNoTests", "test:ci": "jest --ci --verbose --bail --run-in-band --passWithNoTests", "test:watch": "jest --watch"}, "lint-staged": {"*.{ts,js,tsx}": ["eslint --fix", "jest --bail --findRelatedTests --passWithNoTests"], "*.{js,ts,md,json,html}": "prettier --write"}, "dependencies": {"@digheontech/digh.ui": "^1.9.0", "@tanstack/react-query": "^5.63.0", "@tanstack/react-query-devtools": "^5.63.0", "@tanstack/react-query-next-experimental": "^5.63.0", "@types/cors": "^2.8.17", "@types/react-signature-canvas": "^1.0.7", "@types/yup": "^0.32.0", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "cors": "^2.8.5", "dayjs": "^1.11.13", "formik": "^2.4.6", "i18next": "^24.2.1", "next": "15.1.4", "react": "^19.0.0", "react-aria-components": "^1.3.3", "react-datepicker": "^8.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.0", "react-icons": "^5.4.0", "signature_pad": "^5.0.4", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "yup": "^1.6.1"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@testing-library/jest-dom": "6.5.0", "@testing-library/react": "16.0.1", "@types/jest": "29.5.12", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "clsx": "^2.1.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.10", "eslint-config-prettier": "9.1.0", "eslint-plugin-jest": "28.8.3", "eslint-plugin-prettier": "5.2.1", "husky": "9.1.6", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "lint-staged": "15.2.10", "postcss": "^8", "prettier": "3.3.3", "tailwindcss-react-aria-components": "^1.1.5", "ts-node": "10.9.2", "typescript": "^5"}, "resolutions": {"wrap-ansi": "7.0.0", "string-width": "4.1.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}