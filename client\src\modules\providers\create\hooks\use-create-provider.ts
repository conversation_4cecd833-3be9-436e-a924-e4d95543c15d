import { useMutation } from '@tanstack/react-query';

import { useShowError, useShowSuccess } from '@/hooks';
import { AxiosResponseError, Http } from '@/config/http';
import { IFormProvidersValues } from '@/interfaces';
import { useGetProviders } from '../../list/hooks/use-get-providers';

export const useCreateProvider = () => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateProviders } = useGetProviders();

  const {
    mutate: create,
    isPending: isCreating,
    ...rest
  } = useMutation({
    mutationKey: ['create_providers'],
    mutationFn: (payload: IFormProvidersValues) => Http.post('providers', payload),
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: '<PERSON>xi<PERSON>', description: '<PERSON><PERSON><PERSON><PERSON> creado.' });
      invalidateProviders();
    },
  });

  return { create, isCreating, ...rest };
};
