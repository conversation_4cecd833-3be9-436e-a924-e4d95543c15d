import { FieldArray } from 'formik';
import { FormField } from '@/components';
import { Button } from '@digheontech/digh.ui';
import { DiagnosisCodeSearch } from '../searchs';
import { Key } from 'react';

export function UB04Items66_75({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 p-4">
      <h3 className="text-lg font-bold mb-4">Diagnosis Codes (Fields 66-75)</h3>

      <FieldArray name="diagnosisCodes">
        {({ push, remove, form }) => (
          <div>
            <div className="grid grid-cols-4 gap-2 mb-3 font-semibold">
              <span>Diagnosis Code</span>
              <span>Description</span>
              <span>Type</span>
              <span>Actions</span>
            </div>

            {form.values.diagnosisCodes?.map((_: any, index: Key | null | undefined) => (
              <div key={index} className="grid grid-cols-4 gap-2 mb-2">
                <DiagnosisCodeSearch
                  label="Diagnosis Code"
                  name={`diagnosisCodes.${index}.code`}
                  disabled={isLoading}
                />
                <FormField
                  name={`diagnosisCodes.${index}.description`}
                  disabled={isLoading}
                  readOnly
                />
                <FormField
                  name={`diagnosisCodes.${index}.type`}
                  type="select"
                  options={[
                    { value: 'principal', label: 'Principal' },
                    { value: 'secondary', label: 'Secondary' },
                    { value: 'admitting', label: 'Admitting' },
                    { value: 'external', label: 'External Cause' },
                  ]}
                  disabled={isLoading}
                />
                <Button onPress={() => remove(Number(index))}>Remove</Button>
              </div>
            ))}

            <Button
              onPress={() =>
                push({
                  code: '',
                  description: '',
                  type: 'secondary',
                })
              }
            >
              Add Diagnosis Code
            </Button>
          </div>
        )}
      </FieldArray>

      <div className="mt-6">
        <h4 className="font-semibold mb-3">Special Fields</h4>
        <div className="grid grid-cols-2 gap-4">
          <FormField
            name="patientReasonForVisit"
            label="70. Patient Reason for Visit"
            disabled={isLoading}
          />
          <FormField
            name="prospectivePaymentSystemCode"
            label="71. PPS Code"
            disabled={isLoading}
          />
          <FormField
            name="externalCauseOfInjury"
            label="72. External Cause of Injury"
            disabled={isLoading}
          />
          <FormField
            name="principalProcedureCode"
            label="74. Principal Procedure Code"
            disabled={isLoading}
          />
        </div>
      </div>
    </section>
  );
}
