import { Label } from 'react-aria-components';

import { FormField } from '@/components';
import { MultipleDiagnoses } from '../searchs/multiple-diagnoses';
import { ResubmissionSelectItem22 } from './item-22';

interface IIItems21_23 {
  disabled: boolean;
}

export function IItems21_23({ disabled }: IIItems21_23) {
  return (
    <section className="grid grid-cols-2 ">
      <MultipleDiagnoses name="icd10DiagnosisCodesForDiseasesOrInjuries" disabled={disabled} />
      <section className="grid border border-gray-300 border-t-transparent border-l-transparent">
        <div className="grid grid-cols-2 gap-4 p-4 hover-card">
          <div className="flex flex-col gap-4 justify-start">
            <div className="flex gap-4 justify-start">
              <strong className="w-4">22.</strong>
              <Label> MEDICAID RESUBMISSION CODE</Label>
            </div>
            <ResubmissionSelectItem22 />
          </div>
          <div className="w-full">
            <FormField
              label="ORIGINAL REF. NO."
              name="originalReferenceNumber"
              className="!h-12 mt-2"
              disabled={disabled}
            />
          </div>
        </div>
        <div className="p-4 border border-gray-300 border-r-transparent border-l-transparent border-b-transparent hover-card">
          <div className="flex gap-4 justify-start">
            <strong className="w-4">23.</strong>
            <FormField
              label="PRIOR AUTHORIZATION NUMBER"
              name="priorAuthorizationNumber"
              className="!h-12 w-64"
              disabled={disabled}
            />
          </div>
        </div>
      </section>
    </section>
  );
}
