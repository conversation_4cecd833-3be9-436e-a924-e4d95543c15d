import { useState } from 'react';
import { Button } from '@digheontech/digh.ui';
import { BiArrowFromTop } from 'react-icons/bi';
import { Form, Formik, FormikProps } from 'formik';
import { IFormInvoiceTypeAPayload } from '@/interfaces';
import { Disclosure, DisclosureGroup, DisclosurePanel, Heading } from 'react-aria-components';

import { InsuranceCarrier, YesOrNot } from '@/enums';
import { SectionDocuments } from '../section-documents';
import { validationSchema } from './validations-form';
import {
  IItems19_20,
  IItems21_23,
  Item24,
  Items01_01a,
  Items02_03_04,
  Items05,
  Items06_08,
  Items07,
  Items09,
  Items10,
  Items11,
  Items12,
  Items13,
  Items14_16,
  Items17_18,
  Items25_30,
  Items31,
  Items32,
  Items33,
} from './items';

const statePR = 'PR';
const { OTHER } = InsuranceCarrier;

interface FormInvoiceTypeAProps {
  onSubmit: (values: IFormInvoiceTypeAPayload) => void;
  isLoading: boolean;
  innerRef?: React.Ref<FormikProps<IFormInvoiceTypeAPayload>>;
  initialValues: IFormInvoiceTypeAPayload;
  invoiceId: string;
}

const { Yes } = YesOrNot;

const isYes = (value: YesOrNot | boolean) => value === Yes;

export function FormInvoiceTypeA({
  onSubmit,
  isLoading,
  innerRef,
  initialValues,
  invoiceId,
}: FormInvoiceTypeAProps) {
  const [supplementalInfo, setSupplementalInfo] = useState(
    initialValues.supplementalInformation || [],
  );

  const handleSupplementalInfoChange = (
    newSupplementalInfo: IFormInvoiceTypeAPayload['supplementalInformation'],
  ) => {
    setSupplementalInfo(newSupplementalInfo);
  };

  const calculateTotalCharge = () => {
    if (!supplementalInfo || supplementalInfo.length === 0) return 0;

    return supplementalInfo.reduce((total, item) => {
      const charge = parseFloat(Number(item.charges).toFixed(2));
      return total + (isNaN(charge) ? 0 : charge);
    }, 0);
  };

  const handleSubmit = (values: IFormInvoiceTypeAPayload) => {
    const totalCharge = calculateTotalCharge();

    const processedInvoiceValues: IFormInvoiceTypeAPayload = {
      ...values,
      supplementalInformation: supplementalInfo,
      patient: {
        ...values.patient,
        address: {
          ...values.patient?.address,
          state: statePR,
        },
      },
      insured: {
        ...values.insured,
        address: {
          ...values.insured?.address,
          state: statePR,
        },
        anotherHealthBenefitPlan: isYes(values.insured.anotherHealthBenefitPlan),
      },
      billingProvider: {
        ...values.billingProvider,
        state: statePR,
      },
      serviceFacilityLocation: {
        ...values.serviceFacilityLocation,
        state: statePR,
      },
      conditionPatientRelatedToEmployment: isYes(values.conditionPatientRelatedToEmployment),
      conditionPatientRelatedToAutoAccident: isYes(values.conditionPatientRelatedToAutoAccident),
      conditionPatientRelatedToOtherAccident: isYes(values.conditionPatientRelatedToOtherAccident),
      isOutsideLab: isYes(values.isOutsideLab),
      acceptAssignment: isYes(values.acceptAssignment),
      dateOfCurrentIllnessInjuryPregnancy: values.dateOfCurrentIllnessInjuryPregnancy
        ? new Date(values.dateOfCurrentIllnessInjuryPregnancy).toISOString()
        : undefined,
      otherDateConditionOfIllnessOrTreatment: values.otherDateConditionOfIllnessOrTreatment
        ? new Date(values.otherDateConditionOfIllnessOrTreatment).toISOString()
        : undefined,
      totalCharge: totalCharge,
      amountPaid: values.amountPaid ? Number(values.amountPaid) : undefined,
      patientRelationshipToInsured: values.patientRelationshipToInsured || undefined,
    };

    onSubmit(processedInvoiceValues);
  };

  return (
    <>
      <div className="mb-4 text-sm">
        <span className="text-red-500 text-xs mr-1">*</span>
        <span className="text-gray-600">Campos requeridos para enviar la factura</span>
      </div>
      <Formik
        initialValues={{
          ...initialValues,
          insuranceCarrier: OTHER,
          supplementalInformation: supplementalInfo,
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        innerRef={innerRef}
        enableReinitialize={true}
      >
        {formikProps => (
          <Form className="w-full">
            <DisclosureGroup
              defaultExpandedKeys={['PATIENT', 'PHYSICIAN']}
              allowsMultipleExpanded
              className="w-auto"
            >
              <Disclosure id="PATIENT">
                <Heading className="flex justify-between items-center">
                  <Button
                    rightIcon={<BiArrowFromTop />}
                    slot="trigger"
                    buttonType="normal"
                    className="w-full border-none bg-slate-100 hover-card"
                  >
                    <h2 className="text-balance text-lg">PATIENT AND INSURED INFORMATION</h2>
                  </Button>
                </Heading>
                <DisclosurePanel>
                  <Items01_01a isLoading={isLoading} />
                  <Items02_03_04 isLoading={isLoading} />
                  <section className="grid grid-cols-3 border border-gray-300">
                    <Items05 isLoading={isLoading} />
                    <Items06_08 isLoading={isLoading} />
                    <Items07 isLoading={isLoading} />
                  </section>
                  <section className="grid grid-cols-3 border-gray-300">
                    <Items09 isLoading={isLoading} />
                    <Items10 isLoading={isLoading} />
                    <Items11 isLoading={isLoading} />
                  </section>
                  <section className="grid grid-cols-2 border-gray-300">
                    <Items12 disabled={isLoading} />
                    <Items13 />
                  </section>
                </DisclosurePanel>
              </Disclosure>

              <Disclosure id="PHYSICIAN">
                <Heading className="flex justify-between items-center">
                  <Button
                    rightIcon={<BiArrowFromTop />}
                    slot="trigger"
                    buttonType="normal"
                    className="w-full border-none bg-slate-100 hover-card"
                  >
                    <h2 className="text-balance text-lg">PHYSICIAN OR SUPPLIER INFORMATION</h2>
                  </Button>
                </Heading>
                <DisclosurePanel>
                  <section className="grid grid-cols-2 border-gray-300">
                    <Items14_16 disabled={isLoading} />
                    <Items17_18 disabled={isLoading} />
                    <IItems19_20 disabled={isLoading} />
                  </section>
                  <IItems21_23 disabled={isLoading} />
                  <Item24
                    length={supplementalInfo.length}
                    disabled={isLoading}
                    supplementalInfo={supplementalInfo}
                    onSupplementalInfoChange={handleSupplementalInfoChange}
                    formikProps={formikProps}
                  />
                  <Items25_30 disabled={isLoading} totalCharge={calculateTotalCharge()} />
                  <section
                    className="grid grid-cols-3 border border-gray-300 border-x-transparent"
                    style={{ gridTemplateColumns: '437px 520px repeat(1, 1fr)' }}
                  >
                    <Items31 disabled={isLoading} />
                    <Items32 disabled={isLoading} />
                    <Items33 disabled={isLoading} />
                  </section>
                </DisclosurePanel>
              </Disclosure>
            </DisclosureGroup>
          </Form>
        )}
      </Formik>
      <SectionDocuments invoiceId={invoiceId} />
    </>
  );
}
