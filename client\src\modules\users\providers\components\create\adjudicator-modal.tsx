import { useState } from 'react';
import { Button } from '@digheontech/digh.ui';

import { BaseModal } from '@/components';
import { AdjudicatorForm } from './adjudicator-form';

export function AdjudicatorModal() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const closeModal = () => setIsModalOpen(false);
  const openModal = () => setIsModalOpen(true);

  return (
    <BaseModal
      title="Registrar Adjudicador"
      className="max-w-2xl"
      isOpen={isModalOpen}
      onClose={closeModal}
      trigger={
        <Button onPress={openModal} className="btn-primary">
          Registrar Adjudicador
        </Button>
      }
    >
      <AdjudicatorForm onSubmitSuccess={closeModal} />
    </BaseModal>
  );
}
