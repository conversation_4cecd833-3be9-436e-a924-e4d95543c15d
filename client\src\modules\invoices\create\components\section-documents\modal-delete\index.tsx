import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-icons/gr';
import { But<PERSON> } from '@digheontech/digh.ui';
import { RiDeleteBin4Line } from 'react-icons/ri';
import { Dialog, DialogTrigger, Heading, Modal, ModalOverlay } from 'react-aria-components';

import { IDocument, useDeleteDocument } from '../hooks';

interface IModalDelete {
  doc: IDocument;
}

export function ModalDelete({ doc }: IModalDelete) {
  const { deleteDocument } = useDeleteDocument(doc.invoiceId);

  const handleSubmit = ({ fileId }: { fileId: string }) => {
    deleteDocument({
      fileId,
    });
  };

  return (
    <DialogTrigger>
      <Button
        leftIcon={<RiDeleteBin4Line size={20} />}
        buttonType="normal"
        className="rounded-full w-10 h-10 p-2 border-none hover:bg-red-200"
      />
      <ModalOverlay
        className={({ isEntering, isExiting }) => `
          fixed inset-0 z-10 overflow-y-auto bg-black/25 flex min-h-full items-center justify-center p-4 text-center backdrop-blur
          ${isEntering ? 'animate-in fade-in duration-300 ease-out' : ''}
          ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}
        `}
      >
        <Modal
          className={({ isEntering, isExiting }) => `
                w-full max-w-md overflow-hidden rounded-2xl bg-white pt-7 pb-10 px-14 text-left align-middle shadow-xl relative
                ${isEntering ? 'animate-in zoom-in-95 ease-out duration-300' : ''}
                ${isExiting ? 'animate-out zoom-out-95 ease-in duration-200' : ''}
              `}
        >
          <Dialog role="dialog" className="outline-none">
            {({ close }) => (
              <>
                <div className="flex gap-4">
                  <GrAlert size={24} className="w-6 h-6 text-red-500 stroke-2" />
                  <Heading
                    slot="title"
                    className="text-xl font-semibold leading-6 my-0 text-red-600 uppercase"
                  >
                    Eliminar Documento
                  </Heading>
                </div>
                <p className="mt-3 text-pretty text-slate-700">
                  ¿Estás seguro de que quieres borrar el documento? <b>{doc.originalName} </b>
                  <br />
                  <br />
                  <i> Todo el contenido se borrará permanentemente permanentemente.</i>
                </p>
                <div className="mt-6 flex justify-center gap-2">
                  <Button buttonType="normal" onPress={close}>
                    Volver
                  </Button>
                  <Button
                    onPress={() => handleSubmit({ fileId: doc._id })}
                    buttonType="dangerOutlined"
                  >
                    Eliminar
                  </Button>
                </div>
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
}
