import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { HashService } from 'src/shared/hash';
import { UserRole } from 'src/shared/enums';

import { User } from 'src/identity/domain/user.model';
import { UserRepository } from 'src/identity/infrastructure/repositories/user.repository';

import { CreateSuperAdminCommand } from './create-super-admin.commands';

@CommandHandler(CreateSuperAdminCommand)
export class CreateSuperAdminHandler
  implements ICommandHandler<CreateSuperAdminCommand>
{
  constructor(
    private readonly userRepository: UserRepository,
    private readonly hashService: HashService,
  ) {}
  async execute(command: CreateSuperAdminCommand) {
    const formattedUser = await this.formatUser(command);
    const user = await this.userRepository.create(formattedUser);
    return User.fromModel(user);
  }

  private async formatUser(data: Record<string, any>) {
    return {
      email: data.email,
      password: await this.hashService.hash(data.password),
      profile: {
        firstName: data.firstName,
        lastName: data.lastName,
      },
      address: {
        street: data.street,
        municipality: data.municipality,
        zipCode: data.zipCode,
      },
      phoneNumber: data.phoneNumber,
      role: UserRole.SuperAdmin,
    };
  }
}
