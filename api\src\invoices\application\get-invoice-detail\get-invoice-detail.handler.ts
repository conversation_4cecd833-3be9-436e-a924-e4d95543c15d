import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';

import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';
import { GetInvoiceDetailQuery } from './get-invoice-detail.query';

@QueryHandler(GetInvoiceDetailQuery)
export class GetInvoiceDetailHandler
  implements IQueryHandler<GetInvoiceDetailQuery>
{
  constructor(private readonly invoiceRepository: InvoiceRepository) {}

  async execute(query: GetInvoiceDetailQuery) {
    const invoice = await this.invoiceRepository.findById(query.id, true);
    return invoice;
  }
}
