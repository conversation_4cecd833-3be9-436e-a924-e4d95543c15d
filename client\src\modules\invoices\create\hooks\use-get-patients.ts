import { useQuery } from '@tanstack/react-query';
import { Http } from '@/config/http';

export function useGetPatients(termSearch = '') {
  const { data: patients, ...rest } = useQuery({
    queryKey: ['patients', termSearch],
    queryFn: () =>
      Http.get(`/patients?termSearch=${encodeURIComponent(termSearch)}`).then(({ data }) => data),
    enabled: !!termSearch,
  });

  return { patients, ...rest };
}
