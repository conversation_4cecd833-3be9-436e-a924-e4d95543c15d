import { Item17aQualifier } from '@/enums';
import { StatusItem } from '@/modules/providers/create/components/provider-select-type/status-item';
import { getItem17aQualifierLabel } from '@/tools/getItem17aQualifierLabel';
import { useField } from 'formik';
import { Key } from 'react';
import { Button, ListBox, Popover, Select, SelectValue } from 'react-aria-components';
import { LuChevronsUpDown } from 'react-icons/lu';

const QUALIFIERS = [
  Item17aQualifier.StateLicense,
  Item17aQualifier.ProviderUPIN,
  Item17aQualifier.ProviderCommercial,
  Item17aQualifier.LocationNumber,
] as const;

interface IQualifierOption {
  qualifier: Item17aQualifier;
}

interface IQualifierSelectItem17a {
  className?: HTMLElement['className'];
}

const QualifierOption = ({ qualifier }: IQualifierOption) => (
  <StatusItem key={qualifier} id={qualifier}>
    <span>{qualifier}</span> <small>{getItem17aQualifierLabel(qualifier)}</small>
  </StatusItem>
);

export function QualifierSelectItem17a({ className }: IQualifierSelectItem17a) {
  const [field, meta, helpers] = useField<Item17aQualifier>('referringProviderOtherIdQualifier');

  const handleSelectionChange = (key: Key) => {
    helpers.setValue(key as Item17aQualifier);
  };

  return (
    <div className={className}>
      <Select
        aria-label="Qualifier"
        className="flex flex-col gap-1 w-full mb-4"
        selectedKey={field.value}
        onSelectionChange={handleSelectionChange}
        defaultSelectedKey={undefined}
        placeholder="select"
      >
        <Button className="field-shadow h-4">
          <SelectValue className="flex-1 truncate placeholder-shown:italic">
            {qual => getItem17aQualifierLabel(qual as unknown as Item17aQualifier)}
          </SelectValue>
          <LuChevronsUpDown />
        </Button>
        <Popover className="max-h-60 w-[--trigger-width] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out">
          <ListBox className="outline-none p-1">
            {QUALIFIERS.map(qualifier => (
              <QualifierOption key={qualifier} qualifier={qualifier} />
            ))}
          </ListBox>
        </Popover>
      </Select>
      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
