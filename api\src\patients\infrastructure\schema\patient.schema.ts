import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class Address {
  @Prop()
  street?: string;

  @Prop()
  city?: string;

  @Prop()
  state?: string;

  @Prop()
  zipCode?: string;
}

@Schema({ timestamps: true, collection: 'patients' })
export class Patient {
  @Prop()
  firstName: string;

  @Prop()
  middleName: string;

  @Prop()
  surname: string;

  @Prop()
  birthdate: string;

  @Prop()
  gender: string;

  @Prop({
    _id: false,
    type: SchemaFactory.createForClass(Address),
  })
  address?: Address;

  @Prop()
  phoneNumber: string;

  @Prop()
  recordId: string;
}

export type PatientDocument = Patient & Document;

export const PatientSchema = SchemaFactory.createForClass(Patient);
