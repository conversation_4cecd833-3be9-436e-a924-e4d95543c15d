import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Http } from '@/config/http';
import { IProviderUsersTable } from '@/interfaces';

const key = ['users'];

export const useGetUsers = () => {
  const queryClient = useQueryClient();

  const { data: usersProviders, ...rest } = useQuery<IProviderUsersTable[]>({
    queryKey: key,
    queryFn: () => Http.get('users').then(({ data }): IProviderUsersTable[] => data),
  });

  const invalidateUsers = () => queryClient.invalidateQueries({ queryKey: key });

  return { usersProviders, invalidateUsers, ...rest };
};
