import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { NotFoundException } from '@nestjs/common';

import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';
import { DomainError } from 'src/shared/domain';
import { InvoiceStatus } from 'src/shared/enums/invoice-status.enum';
import { InvoiceFileRepository } from 'src/invoices/infrastructure/repositories/invoice-file.repository';
import { AwsS3Service } from 'src/file-management/infrastructure/aws-s3.service';
import { FOLDER_INVOICE } from 'src/invoices/domain/invoice.enum';

import { DeleteInvoiceCommand } from './delete-invoice.command';

@CommandHandler(DeleteInvoiceCommand)
export class DeleteInvoiceHandler
  implements ICommandHandler<DeleteInvoiceCommand>
{
  constructor(
    private readonly invoiceRepository: InvoiceRepository,
    private readonly invoiceFileRepository: InvoiceFileRepository,
    private readonly fileService: AwsS3Service,
  ) {}

  async execute(command: DeleteInvoiceCommand) {
    const invoice = await this.invoiceRepository.findById(
      command.invoiceId,
      true,
    );

    if (invoice.status === InvoiceStatus.Admitted) {
      throw new DomainError(
        'CANNOT_DELETE_INVOICE',
        `Cannot delete invoice, Invoice is not in admitted state`,
      );
    }

    await this.deleteFiles(command.invoiceId);
    await this.invoiceRepository.delete(command.invoiceId);
    return { invoiceId: command.invoiceId };
  }

  private async deleteFiles(invoiceId: string) {
    const files = await this.invoiceFileRepository.getByInvoiceId(invoiceId);
    if (!files) {
      return;
    }
    for (const file of files) {
      const filePath = `${FOLDER_INVOICE}/${file.fileName}`;
      await this.fileService.remove(filePath);
    }
    await this.invoiceFileRepository.deleteByInvoiceId(invoiceId);
  }
}
