import { FormField } from '@/components';

interface ValueAmountCodeSelectProps {
  name: string;
  disabled?: boolean;
}

export function ValueAmountCodeSelect({ name, disabled }: ValueAmountCodeSelectProps) {
  const valueAmountCodes = [
    { value: '01', label: 'Most Common Semi-Private Room Rate' },
    { value: '02', label: 'Hospital Has No Semi-Private Rooms' },
    { value: '03', label: 'Reserved for National Assignment' },
    { value: '04', label: 'Inpatient Professional Component Charges' },
    { value: '05', label: 'Professional Component Included in Charges' },
    { value: '06', label: 'Medicare Blood Deductible' },
    { value: '07', label: 'Medicare Coinsurance Amount' },
    { value: '08', label: 'Medicare Lifetime Reserve Amount' },
    { value: '09', label: 'Medicare Coinsurance Amount for Lifetime Reserve Days' },
    { value: '10', label: 'Lifetime Reserve Amount in First Calendar Year' },
    { value: '11', label: 'Coinsurance Amount in First Calendar Year' },
    { value: '12', label: 'Working Aged Beneficiary/Spouse with Employer Group Health Plan' },
    { value: '13', label: 'ESRD Beneficiary in a Medicare Coordination Period' },
    { value: '14', label: 'No-Fault Including Auto/Other' },
    { value: '15', label: "Workers' Compensation" },
    { value: '16', label: 'PHS or Other Federal Agency' },
    { value: '17', label: 'Payer Amount' },
    { value: '30', label: 'Pre-admission Testing' },
    { value: '31', label: 'Patient Liability Amount' },
    { value: '37', label: 'Payer Patient Responsibility Amount' },
    { value: '38', label: 'Payer Patient Responsibility Amount - Periodic Interim Payment (PIP)' },
    { value: '39', label: 'Payer Patient Responsibility Amount - Interim Bill' },
    { value: '40', label: 'Payer Patient Responsibility Amount - First Interim Bill' },
    { value: '41', label: 'Payer Patient Responsibility Amount - Last Interim Bill' },
    { value: '42', label: 'Old Capital Amount' },
    { value: '43', label: 'New Capital Amount' },
    { value: '44', label: 'Disabled Beneficiary Under Age 65 with LGHP' },
    { value: '45', label: 'Accident Hour' },
    { value: '46', label: 'Number of Grace Days' },
    { value: '47', label: 'Any Liability Insurance' },
    { value: '48', label: 'Hemoglobin Reading' },
    { value: '49', label: 'Hematocrit Reading' },
    { value: '50', label: 'Physical Therapy Visits' },
    { value: '51', label: 'Occupational Therapy Visits' },
    { value: '52', label: 'Speech Therapy Visits' },
    { value: '53', label: 'Cardiac Rehabilitation Visits' },
    { value: '54', label: 'Newborn Birth Weight in Grams' },
    { value: '55', label: 'Eligibility Threshold for Charity Care' },
    { value: '56', label: 'Skilled Nurse - Home Visit Hours' },
    { value: '57', label: 'Home Health Aide - Home Visit Hours' },
    { value: '58', label: 'Arterial Blood Gas (PO2)' },
    { value: '59', label: 'Oxygen Saturation (O2 Sat)' },
    { value: '60', label: 'HHA Branch MSA' },
    { value: '61', label: 'Location Where Service is Furnished (HHA and Hospice)' },
    { value: '67', label: 'Peritoneal Dialysis' },
    { value: '68', label: 'EPO-Drug' },
    { value: '69', label: 'State Charity Care Percent' },
    { value: '70', label: 'Qualifying Stay From Date' },
    { value: '71', label: 'Qualifying Stay To Date' },
    { value: '72', label: 'Spell of Illness From Date' },
    { value: '73', label: 'Spell of Illness To Date' },
    { value: '74', label: 'Non-Covered Level of Care/Leave of Absence - From Date' },
    { value: '75', label: 'Non-Covered Level of Care/Leave of Absence - To Date' },
    { value: '76', label: 'Patient Liability - Spell of Illness' },
    { value: '77', label: 'Provider Liability - Spell of Illness' },
    { value: '78', label: 'SNF Prior Stay Dates - From Date' },
    { value: '79', label: 'SNF Prior Stay Dates - To Date' },
    { value: '80', label: 'Psychiatric Facility or Unit - From Date' },
    { value: '81', label: 'Psychiatric Facility or Unit - To Date' },
    { value: 'A0', label: 'Special Zip Code Reporting' },
    { value: 'A1', label: 'Deductible Payer A' },
    { value: 'A2', label: 'Coinsurance Payer A' },
    { value: 'A3', label: 'Estimated Responsibility Payer A' },
    { value: 'A4', label: 'Covered Self-Administrable Drug Emergency Supply' },
    {
      value: 'A5',
      label:
        'Covered Self-Administrable Drug Not Self-Administered in Form and Situation Furnished to Patient',
    },
    { value: 'A6', label: 'Covered Level of Care/Leave of Absence - From Date' },
    { value: 'A7', label: 'Covered Level of Care/Leave of Absence - To Date' },
    { value: 'A8', label: 'Covered SNF Prior Stay Dates - From Date' },
    { value: 'A9', label: 'Covered SNF Prior Stay Dates - To Date' },
    { value: 'B1', label: 'Deductible Payer B' },
    { value: 'B2', label: 'Coinsurance Payer B' },
    { value: 'B3', label: 'Estimated Responsibility Payer B' },
    { value: 'C1', label: 'Deductible Payer C' },
    { value: 'C2', label: 'Coinsurance Payer C' },
    { value: 'C3', label: 'Estimated Responsibility Payer C' },
  ];

  return (
    <FormField
      name={name}
      type="select"
      options={valueAmountCodes}
      disabled={disabled}
      placeholder="Select Value Amount Code"
    />
  );
}
