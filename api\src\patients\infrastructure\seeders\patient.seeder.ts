import { readFileSync } from 'fs';
import { join } from 'path';
import { InjectModel } from '@nestjs/mongoose';
import { Seeder } from 'nestjs-seeder';
import { Patient } from '../schema/patient.schema';
import { Model } from 'mongoose';

export class PatientSeeder implements Seeder {
  private readonly seedData;
  constructor(
    @InjectModel(Patient.name)
    private readonly patientModel: Model<Patient>,
  ) {
    this.seedData = this.loadSeedData();
  }

  private loadSeedData() {
    const filePath = join(__dirname, './patients-data.json');
    const fileContent = readFileSync(filePath, 'utf-8');
    return JSON.parse(fileContent);
  }

  async seed(): Promise<any> {
    return this.patientModel.insertMany(this.seedData);
  }

  async drop(): Promise<any> {
    return this.patientModel.deleteMany({}).exec();
  }
}
