import { FormField, FieldDatePicker } from '@/components';

export function UB04Items01_08({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="bg-white">
      {/* <PERSON><PERSON> de la sección */}
      <div className="bg-gray-100 px-4 py-2 border-b border-gray-300">
        <h3 className="font-bold text-sm text-gray-800">
          PROVIDER AND PATIENT INFORMATION (FIELDS 1-8)
        </h3>
      </div>

      {/* Fila 1: Provider Information */}
      <div className="grid grid-cols-12 border-b border-gray-300">
        <div className="col-span-6 border-r border-gray-300 p-3">
          <FormField
            name="providerName"
            label="1. PROVIDER NAME"
            disabled={isLoading}
            className="text-xs"
          />
        </div>
        <div className="col-span-3 border-r border-gray-300 p-3">
          <FormField
            name="providerNumber"
            label="PROVIDER NUMBER"
            disabled={isLoading}
            className="text-xs"
          />
        </div>
        <div className="col-span-3 p-3">
          <FormField
            name="providerAddress"
            label="PROVIDER ADDRESS"
            disabled={isLoading}
            className="text-xs"
          />
        </div>
      </div>

      {/* Fila 2: Patient Information */}
      <div className="grid grid-cols-12 border-b border-gray-300">
        <div className="col-span-8 border-r border-gray-300 p-3">
          <FormField
            name="patientName"
            label="2. PATIENT NAME"
            disabled={isLoading}
            className="text-xs"
          />
        </div>
        <div className="col-span-4 p-3">
          <FormField
            name="patientControlNumber"
            label="3. PATIENT CONTROL NUMBER"
            disabled={isLoading}
            className="text-xs"
          />
        </div>
      </div>

      {/* Fila 3: Patient Details */}
      <div className="grid grid-cols-12">
        <div className="col-span-6 border-r border-gray-300 p-3">
          <FormField
            name="patientAddress"
            label="4. PATIENT ADDRESS"
            disabled={isLoading}
            className="text-xs"
          />
        </div>
        <div className="col-span-2 border-r border-gray-300 p-3">
          <FieldDatePicker
            name="patientBirthDate"
            label="5. BIRTH DATE"
            disabled={isLoading}
            className="text-xs"
          />
        </div>
        <div className="col-span-2 border-r border-gray-300 p-3">
          <FormField
            name="patientSex"
            label="6. SEX"
            type="radio"
            options={[
              { value: 'M', label: 'M' },
              { value: 'F', label: 'F' },
            ]}
            disabled={isLoading}
            className="text-xs"
          />
        </div>
        <div className="col-span-2 p-3">
          <FormField
            name="admissionDate"
            label="7. ADMISSION DATE"
            disabled={isLoading}
            className="text-xs"
          />
        </div>
      </div>
    </section>
  );
}
