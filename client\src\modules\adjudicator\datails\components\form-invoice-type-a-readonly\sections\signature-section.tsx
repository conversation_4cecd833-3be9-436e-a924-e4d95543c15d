import React from 'react';
import { AuditableSection } from '../../auditable-section';
import { FieldCMS } from '../ui/cms-field';
import { isFieldInAdjustments, getOriginalFieldValue } from '../utils';
import { SectionProps } from '../interfaces';

export const SignatureSection = ({ invoice }: SectionProps) => {
  return (
    <div className="mt-2">
      <div className="grid grid-cols-2 gap-1">
        <FieldCMS
          className="h-32"
          label="12. PATIENT'S OR AUTHORIZED PERSON'S SIGNATURE I authorize the release of
                any medical or other information necessary to process this claim. I also request
                payment of government benefits either to myself or to the party who accepts
                assignment below."
          value={
            <div className="w-full flex items-center gap-12">
              <div className="text-xl w-full flex justify-center items-center h-12">
                Signature on File
              </div>
              <div className="w-1/2 mb-4">
                <AuditableSection title="Auditoria" fieldName="patientOrAuthorizedSignatureDate">
                  <FieldCMS
                    label="DATE:"
                    value={
                      getOriginalFieldValue(
                        invoice,
                        'patientOrAuthorizedSignatureDate',
                        invoice.patientOrAuthorizedSignatureDate,
                      ) ? (
                        new Date(
                          getOriginalFieldValue(
                            invoice,
                            'patientOrAuthorizedSignatureDate',
                            invoice.patientOrAuthorizedSignatureDate,
                          ),
                        ).toLocaleDateString()
                      ) : (
                        <span className="text-gray-400">-</span>
                      )
                    }
                    isVerified={isFieldInAdjustments(invoice, 'patientOrAuthorizedSignatureDate')}
                  />
                </AuditableSection>
              </div>
            </div>
          }
        />

        <FieldCMS
          className="h-32"
          label="13. INSURED'S OR AUTHORIZED PERSON'S SIGNATURE I authorize payment of
                medical benefits to the undersigned physician or supplier for services described
                below."
          value={
            <div className="text-xl w-full flex justify-center items-center h-12">
              Signature on File
            </div>
          }
        />
      </div>
    </div>
  );
};
