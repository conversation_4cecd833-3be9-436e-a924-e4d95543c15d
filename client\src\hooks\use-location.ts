'use client';

import { usePathname, useSearchParams } from 'next/navigation';

export function useLocation() {
  const pathName = usePathname();
  const searchParams = useSearchParams();
  const queryParamsString = searchParams?.toString();

  const fullUrl = [pathName, queryParamsString ? '?' : '', queryParamsString]
    .toString()
    .replaceAll(',', '');

  return { pathName, searchParams, fullUrl };
}
