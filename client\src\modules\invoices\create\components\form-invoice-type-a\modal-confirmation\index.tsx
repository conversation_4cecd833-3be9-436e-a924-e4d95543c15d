import { Formik } from 'formik';
import { Button } from '@digheontech/digh.ui';
import { Dialog, DialogTrigger, Form, Modal, ModalOverlay } from 'react-aria-components';
import { FieldDatePicker, FieldSignature } from '@/components';
import { useConfirmInvoice } from '../../../hooks';
import { IFormInvoiceTypeAPayload, IFormInvoiceUB04Payload } from '@/interfaces';

interface ModalConfirmationProps {
  invoiceId: string;
  invoiceData: IFormInvoiceTypeAPayload | IFormInvoiceUB04Payload;
}

export function ModalConfirmation({ invoiceId, invoiceData }: ModalConfirmationProps) {
  const { confirm, isConfirming } = useConfirmInvoice();

  // Make sure we always have defined values
  const INITIAL_VALUES = {
    signature: '',
    date: new Date(),
  };

  const onSubmit = (values: { signature: string; date: Date }, callback: () => void) => {
    confirm({
      invoiceId,
      confirmSign: values.signature,
      confirmDate: values.date,
      invoiceData,
    });
    callback();
  };

  return (
    <DialogTrigger>
      <Button primary buttonType="secondary" className="mt-7" size="large">
        Completar Factura
      </Button>
      <ModalOverlay
        className={({
          isEntering,
          isExiting,
        }) => `fixed inset-0 z-10 overflow-y-auto bg-black/25 flex min-h-full items-center justify-center p-4 text-center backdrop-blur
          ${isEntering ? 'animate-in fade-in duration-300 ease-out' : ''}
          ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}`}
      >
        <Modal
          className={({
            isEntering,
            isExiting,
          }) => `w-full max-w-screen-md overflow-hidden rounded-2xl bg-white pt-7 pb-10 px-10 text-left align-middle shadow-xl relative
                ${isEntering ? 'animate-in zoom-in-95 ease-out duration-300' : ''}
                ${isExiting ? 'animate-out zoom-out-95 ease-in duration-200' : ''}`}
        >
          <Dialog role="dialog" className="outline-none">
            {({ close }) => (
              <>
                <p className="text-xl text-gray-700 mb-6 italic text-balance">
                  &quot;Bajo pena de nulidad absoluta certifico que ningún servidor público del
                  Programa de Servicios de Salud Correccional es parte o tiene algún interés en las
                  ganancias o beneficios producto del contrato objeto de esta factura y de ser parte
                  o tener interés en las ganancias o beneficios producto del contrato ha mediado una
                  dispensa previa. La única consideración para suministrar los bienes o servicios
                  objeto del contrato ha sido el pago acordado con el representante autorizado de la
                  agencia. El importe de esta factura es justo y correcto. Los trabajos de
                  construcción han sido realizados, los productos han sido entregados, (los
                  servicios prestados) y no han sido pagados.&quot;
                </p>
                <Formik
                  initialValues={INITIAL_VALUES}
                  onSubmit={values => {
                    onSubmit(values, () => {
                      close();
                    });
                  }}
                >
                  {({ resetForm, isSubmitting, submitForm }) => (
                    <Form>
                      <section className="grid grid-cols-2  items-end">
                        <FieldSignature
                          width={400}
                          name="signature"
                          disabled={isSubmitting || isConfirming}
                          className="w-full mt-2"
                        />
                        <div className="w-full flex justify-center mb-7">
                          <FieldDatePicker
                            label="DATE"
                            name="date"
                            disabled={isSubmitting || isConfirming}
                            className="w-full"
                          />
                        </div>
                      </section>
                      <div className="flex justify-center gap-2 pt-4">
                        <Button
                          disabled={isSubmitting || isConfirming}
                          fullWidth
                          type="button"
                          className="h-10 w-52"
                          buttonType="normal"
                          onPress={() => {
                            resetForm();
                            close();
                          }}
                        >
                          Cerrar
                        </Button>
                        <Button
                          disabled={isSubmitting || isConfirming}
                          fullWidth
                          type="button"
                          className="h-10 w-52"
                          primary
                          onPress={() => {
                            submitForm();
                          }}
                        >
                          Confirmar
                        </Button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
}
