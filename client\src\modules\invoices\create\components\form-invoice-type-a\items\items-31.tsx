'use client';

import { FieldDatePicker, FieldSignature } from '@/components';

interface Items12 {
  disabled: boolean;
}

export function Items31({ disabled }: Items12) {
  return (
    <section className="col-span-1 border border-gray-300 border-y-transparent p-4 hover-card">
      <div className="flex gap-2 mt-4">
        <strong>31.</strong>
        <div className="flex gap-2">
          <small className="text-red-500 block">*</small>

          <p className="text-primary-500 font-bold block">
            SIGNATURE OF PHYSICIAN OR SUPPLIER INCLUDING DEGREES OR CREDENTIALS
          </p>
        </div>
      </div>
      <small>
        (l certify that the statements on the reverse apply to this bill and are made a part
        thereof.)
      </small>
      <FieldSignature
        width={400}
        name="physicianSignature"
        disabled={disabled}
        className="w-full mt-2"
      />
      <div className="w-full flex justify-center">
        <FieldDatePicker
          label="DATE"
          name="physicianSignatureDate"
          disabled={disabled}
          className="w-full"
          labelClassName="text-primary-500 font-bold block text-sm mb-1"
        />
      </div>
    </section>
  );
}
