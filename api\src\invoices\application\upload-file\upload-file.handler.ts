import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { uuid as uuidv4 } from 'uuidv4';

import { AwsS3Service } from 'src/file-management/infrastructure/aws-s3.service';
import { InvoiceFileRepository } from 'src/invoices/infrastructure/repositories/invoice-file.repository';
import { FOLDER_INVOICE } from 'src/invoices/domain/invoice.enum';

import { UploadFileCommand } from './upload-file.command';
import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';

@CommandHandler(UploadFileCommand)
export class UploadFileHandler implements ICommandHandler<UploadFileCommand> {
  constructor(
    private readonly fileService: AwsS3Service,
    private readonly invoiceFileRepository: InvoiceFileRepository,
    private readonly invoiceRepository: InvoiceRepository,
  ) {}

  async execute(command: UploadFileCommand) {
    const fileName = `${uuidv4()}_${command.fileName}`;
    const fileUrl = await this.uploadFile(command.file, fileName);

    await this.invoiceFileRepository.create({
      invoiceId: command.id,
      url: fileUrl,
      originalName: command.fileName,
      size: command.file.size,
      fileType: command.file.mimetype,
      fileName: fileName,
      documentType: command.documentType,
    });

    await this.invoiceRepository.update(command.id, { $inc: { fileCount: 1 } });

    return fileUrl;
  }

  private uploadFile(file: Express.MulterS3.File, fileName: string) {
    const filePath = `${FOLDER_INVOICE}/${fileName}`;

    return this.fileService.upload(filePath, file, 'private');
  }
}
