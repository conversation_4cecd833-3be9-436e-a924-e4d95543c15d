import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { Recaptcha } from '@nestlab/google-recaptcha';

import { Public } from 'src/shared/validation';

import { CreateUserCommand } from '../application/create-user/create-user.command';
import { SignInCommand } from '../application/sign-in/sign-in.command';
import { GetLoggedUserQuery } from '../application/get-logged-user/get-logged-user.query';
import { GetProviderUsersQuery } from '../application/get-user-providers/get-provider-users.query';

import { CreateUserDto } from './dto/create-user.dto';
import { SignInDto } from './dto/sign-in.dto';
import { Roles } from './roles.decorator';
import { UserRole } from 'src/shared/enums';
import { GetUsersQuery } from '../application/get-users/get-users.query';

@Controller('users')
export class UsersController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBuys: QueryBus,
  ) {}

  @Post('/sign-in')
  @Public()
  async signIn(@Body() body: SignInDto) {
    return this.commandBus.execute(new SignInCommand(body));
  }

  @Post('/sign-up')
  @Public()
  @Recaptcha()
  async signUp(@Body() body: CreateUserDto) {
    return this.commandBus.execute(new CreateUserCommand(body));
  }

  @Get('/me')
  async getMe(@Req() req: any) {
    return this.commandBus.execute(new GetLoggedUserQuery(req.user.id));
  }

  @Get('/providers')
  @Roles(UserRole.SuperAdmin)
  async getUserProviders() {
    return this.queryBuys.execute(new GetProviderUsersQuery());
  }

  @Post('/adjudicator')
  @Roles(UserRole.SuperAdmin)
  async createClaimant(@Body() body: CreateUserDto) {
    return this.commandBus.execute(
      new CreateUserCommand({ ...body, role: UserRole.Adjudicator }),
    );
  }

  @Get('/')
  @Roles(UserRole.SuperAdmin)
  async getUsers() {
    return this.queryBuys.execute(new GetUsersQuery());
  }
}
