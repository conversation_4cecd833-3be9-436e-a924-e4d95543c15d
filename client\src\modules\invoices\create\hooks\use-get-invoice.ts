import { Http } from '@/config/http';
import { useQuery, useQueryClient } from '@tanstack/react-query';

export function useGetInvoice(id?: string | null) {
  const queryClient = useQueryClient();

  const key = ['invoices', id];

  const { data: invoices, ...rest } = useQuery({
    queryKey: key,
    queryFn: () => Http.get(`/invoices/${id}`).then(({ data }) => data),
    enabled: !!id,
  });

  const invalidateInvoiceId = () => queryClient.invalidateQueries({ queryKey: key });

  return { invoices, invalidateInvoiceId, ...rest };
}
