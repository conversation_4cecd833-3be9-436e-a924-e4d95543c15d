import { SetStateAction, useState, useEffect } from 'react';
import { SearchField } from 'react-aria-components';
import { Input } from '@digheontech/digh.ui';

interface IDiagnosisCodeSearch {
  name: string;
  label: string;
  onSelectDiagnosis?: (diagnosis: any) => void;
  initialValue?: string;
  disabled?: boolean;
}

export function DiagnosisCodeSearch({
  name,
  label,
  onSelectDiagnosis,
  initialValue = '',
  disabled,
}: IDiagnosisCodeSearch) {
  const [inputValue, setInputValue] = useState(initialValue);
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  // TODO: Replace with actual diagnosis search hook
  const diagnoses: any[] = [];
  const isLoading = false;

  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  const handleSearch = (value: SetStateAction<string>) => {
    const newValue = value as string;
    setInputValue(newValue);
    setShowResults(!!newValue);
    setSearchTerm(newValue);
  };

  const handleSelectDiagnosis = (diagnosis: any) => {
    setInputValue(`${diagnosis.code} - ${diagnosis.description}`);
    setShowResults(false);
    if (onSelectDiagnosis) {
      onSelectDiagnosis(diagnosis);
    }
  };

  return (
    <div className="w-full relative">
      <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
      <SearchField
        value={inputValue}
        onChange={handleSearch}
        className="relative flex items-center w-full"
        aria-label={label}
      >
        <Input
          name={name}
          aria-label={label}
          value={inputValue}
          placeholder="Buscar código de diagnóstico..."
          className="field-shadow !h-12"
          disabled={disabled}
        />
      </SearchField>

      {showResults && diagnoses?.length > 0 && (
        <ul className="absolute z-10 w-full max-h-60 overflow-auto mt-1 bg-white border border-gray-200 rounded-lg divide-y divide-gray-200 shadow-lg">
          {diagnoses.map((diagnosis: any, index: number) => (
            <li
              key={index}
              className="px-4 py-2 text-gray-700 cursor-pointer hover:bg-gray-100"
              onClick={() => handleSelectDiagnosis(diagnosis)}
            >
              <div className="flex justify-start gap-4">
                <small>
                  <strong>Código:</strong> {diagnosis.code}
                </small>
                <small>
                  <strong>Descripción:</strong> {diagnosis.description}
                </small>
              </div>
            </li>
          ))}
        </ul>
      )}

      {isLoading && <p className="mt-2 text-gray-500">Cargando...</p>}

      {searchTerm && !isLoading && diagnoses?.length === 0 && (
        <p className="mt-2 text-red-400 text-sm">No se encontraron códigos de diagnóstico.</p>
      )}
    </div>
  );
}
