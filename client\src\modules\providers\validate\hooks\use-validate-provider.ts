import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';

import { AxiosResponseError, Http } from '@/config/http';
import { useAuth, useShowError, useShowSuccess } from '@/hooks';
import { IFormValidateProviders, IOnLogin } from '@/interfaces';

export const useValidateProvider = () => {
  const router = useRouter();
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { onLogin } = useAuth();

  const {
    mutate: validate,
    isPending: isValidating,
    ...rest
  } = useMutation({
    mutationKey: ['validate_providers'],
    mutationFn: async (payload: IFormValidateProviders): Promise<IOnLogin> => {
      const { data } = await Http.post('providers/validate-npi', payload);
      return data;
    },
    onSuccess: ({ token, user }: IOnLogin) => {
      onLogin({ token, user });
      showSuccess({ title: 'Éxito', description: 'Proveedor valido.' });
      router.push('/providers/dashboard');
    },
    onError: (error: AxiosResponseError) => showError(error),
  });

  return { validate, isValidating, ...rest };
};
