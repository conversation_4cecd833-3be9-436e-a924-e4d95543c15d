import { useState, useEffect, Key } from 'react';
import {
  SearchField,
  Button,
  ListBox,
  Popover,
  Select,
  SelectValue,
  Label,
} from 'react-aria-components';
import { Input } from '@digheontech/digh.ui';
import { useField, useFormikContext } from 'formik';
import { LuChevronsUpDown } from 'react-icons/lu';

import { ICityZipCodes, CITY_ZIP_CODES } from './city_zip_codes';
import { FormField } from '@/components';
import { StatusItem } from '@/modules/providers/create/components/provider-select-type/status-item';

interface ICityZipCodeSearch {
  onSelectPlace?: (code: string) => void;
  initialValue?: string;
  disabled?: boolean;
  city: string;
  className?: string;
  zipCode: string;
  state: string;
}

export function CityZipCodeSearch({
  className,
  onSelectPlace,
  initialValue = '',
  disabled,
  city,
  zipCode,
  state,
}: ICityZipCodeSearch) {
  const [field, meta, helpers] = useField(city);
  const [zipField, zipMeta, zipHelpers] = useField(zipCode);
  const { setFieldValue } = useFormikContext();

  const [inputValue, setInputValue] = useState(initialValue || field.value || '');
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  const [filteredResults, setFilteredResults] = useState<ICityZipCodes[]>([]);
  const [selectedCity, setSelectedCity] = useState<ICityZipCodes | null>(null);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const valueToUse = field.value || initialValue;

    if (valueToUse) {
      const city = CITY_ZIP_CODES.find(c => c.city.toLowerCase() === valueToUse.toLowerCase());
      if (city) {
        setInputValue(city.city);
        setSelectedCity(city);
      } else {
        setInputValue(valueToUse);
      }
      setSearchTerm(valueToUse);
    } else {
      setInputValue('');
      setSearchTerm('');
    }
  }, [initialValue, field.value, zipCode]);

  useEffect(() => {
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      const results = CITY_ZIP_CODES.filter(
        item =>
          item.city.toLowerCase().includes(lowerSearchTerm) ||
          item.zipCodes.some(zip => zip.toLowerCase().includes(lowerSearchTerm)),
      );
      setFilteredResults(results);
    } else {
      setFilteredResults([]);
    }
  }, [searchTerm]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    setShowResults(!!newValue);

    if (searchTimeout) clearTimeout(searchTimeout);

    if (newValue) {
      const timeout = setTimeout(() => setSearchTerm(newValue), 500);
      setSearchTimeout(timeout as NodeJS.Timeout);
    } else {
      setSearchTerm('');
      setFilteredResults([]);
      helpers?.setValue('');
      setFieldValue(zipCode, '');
      setSelectedCity(null);
    }
  };

  const handleSelectPlace = (value: ICityZipCodes) => {
    setInputValue(value.city);
    setSearchTerm(value.city);
    setShowResults(false);
    setSelectedCity(value);

    helpers.setValue(value.city);
    helpers.setTouched(true);

    setFieldValue(zipCode, '');

    if (onSelectPlace) onSelectPlace(value.city);
  };

  const handleZipCodeChange = (key: Key) => {
    zipHelpers.setValue(key as string);
    setFieldValue(zipCode, key as string);
  };

  useEffect(() => {
    return () => {
      if (searchTimeout) clearTimeout(searchTimeout);
    };
  }, [searchTimeout]);

  return (
    <section>
      <div className={`relative ${className || ''} grid grid-cols-3 gap-4 `}>
        <div className="flex flex-col gap-3 mt-1">
          <Label className="text-primary-500 font-bold block text-sm mb-1">CITY</Label>
          <SearchField aria-label="City Search" className="relative flex items-center" {...field}>
            <Input
              value={inputValue}
              onChange={handleSearch}
              placeholder="Search..."
              className="field-shadow h-10 flex items-center justify-between px-3 py-2 border rounded-md"
              disabled={disabled}
            />
          </SearchField>
          {searchTerm && filteredResults.length === 0 && (
            <small className="text-red-400 text-sm w-96">Error City...</small>
          )}

          {showResults && filteredResults.length > 0 && (
            <ul className="absolute z-10 min-w-max max-h-60 overflow-auto mt-1 bg-white border border-gray-200 rounded-lg divide-y divide-gray-200 shadow-lg">
              {filteredResults.map(city => (
                <li
                  key={city.city}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  onClick={() => handleSelectPlace(city)}
                >
                  <div className="flex flex-col font-semibold">{city.city}</div>
                </li>
              ))}
            </ul>
          )}

          {meta.touched && meta.error && (
            <div className="text-red-500 text-sm mt-1">{meta.error}</div>
          )}
        </div>
        <FormField label="STATE" value="PR" name={state} height={40} disabled />
        <div className="flex flex-col gap-2">
          <Label className="text-primary-500 font-bold block text-sm mb-1">ZIP CODE</Label>
          <Select
            aria-label="Código Postal"
            className="flex flex-col w-full"
            name={zipCode}
            selectedKey={zipField.value || ''}
            onSelectionChange={handleZipCodeChange}
            defaultSelectedKey=""
            isDisabled={!selectedCity}
          >
            <Button className="field-shadow h-4 flex" aria-label="Código Postal">
              <SelectValue className="flex-1 truncate placeholder-shown:italic">
                {({ selectedText }) => selectedText || 'Select'}
              </SelectValue>
              <LuChevronsUpDown />
            </Button>
            <Popover className="max-h-60 w-[--trigger-width] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out">
              <ListBox className="outline-none p-1">
                {selectedCity?.zipCodes.map(code => (
                  <StatusItem key={code} id={code} aria-label={code}>
                    <div className="flex gap-2 items-baseline">
                      <span>{code}</span>
                    </div>
                  </StatusItem>
                ))}
              </ListBox>
            </Popover>
          </Select>
          {zipMeta.touched && zipMeta.error && (
            <div className="text-red-500 text-sm mt-1">{zipMeta.error}</div>
          )}
        </div>
      </div>
    </section>
  );
}
