'use client';

import { AppMenu, SidebarHeaderTemplate } from '@/components';
import { AdjudicatorListPage } from '@/modules/adjudicator';
import { useSidebarCollapsed } from '@/hooks/use-sidebar-collapsed';

export default function AdjudicatorList() {
  const [collapsed, setCollapsed] = useSidebarCollapsed();

  return (
    <SidebarHeaderTemplate
      sidebar={<AppMenu collapsed={collapsed} setCollapsed={setCollapsed} />}
      header={null}
      collapsed={collapsed}
    >
      <AdjudicatorListPage />
    </SidebarHeaderTemplate>
  );
}
