import { IFormInvoiceTypeAPayload } from '@/interfaces';

export const isFieldInAdjustments = (data: IFormInvoiceTypeAPayload, fieldName: string) => {
  if (!data.invoiceAdjustments) return false;

  if (Array.isArray(data.invoiceAdjustments)) {
    return data.invoiceAdjustments.some(adj => adj.fieldName === fieldName);
  }

  if (fieldName.includes('.')) {
    const [parent, child] = fieldName.split('.');
    return data.invoiceAdjustments[parent] && data.invoiceAdjustments[parent][child] !== undefined;
  }

  const fieldValue = data.invoiceAdjustments[fieldName];
  if (fieldValue === undefined) return false;

  if (Array.isArray(fieldValue)) {
    return fieldValue.length > 0;
  }

  return fieldValue !== null && fieldValue !== '';
};

export const getAdjustmentValue = (data: IFormInvoiceTypeAPayload, fieldName: string) => {
  if (!data.invoiceAdjustments) return null;

  if (Array.isArray(data.invoiceAdjustments)) {
    const adjustment = data.invoiceAdjustments.find(adj => adj.fieldName === fieldName);
    return adjustment ? adjustment.newValue : null;
  }

  if (fieldName.includes('.')) {
    const [parent, child] = fieldName.split('.');
    return data.invoiceAdjustments[parent] && data.invoiceAdjustments[parent][child] !== undefined
      ? data.invoiceAdjustments[parent][child]
      : null;
  }

  return data.invoiceAdjustments[fieldName] !== undefined
    ? data.invoiceAdjustments[fieldName]
    : null;
};

export const getOriginalFieldValue = (
  data: IFormInvoiceTypeAPayload,
  fieldName: string,
  originalValue: any,
) => {
  return originalValue;
};

export const getFieldValue = (
  data: IFormInvoiceTypeAPayload,
  fieldName: string,
  originalValue: any,
) => {
  const adjustmentValue = getAdjustmentValue(data, fieldName);
  return adjustmentValue !== null ? adjustmentValue : originalValue;
};

export const getDiagnosisValue = (val: any) => {
  if (!val) return '';
  if (typeof val === 'string') return val;
  if (typeof val === 'object' && typeof val.code === 'string') return val.code;
  return '';
};
