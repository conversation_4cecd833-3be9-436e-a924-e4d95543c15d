'use client';

import { Suspense, ReactNode } from 'react';
import { Toast } from '@digheontech/digh.ui';
import { section } from '@/constants';

import { TokenInterceptorProvider } from '@/contexts/token-interceptor-provider';
import AppProvider from '@/contexts/app-provider';

import { LoadingFallback } from '../components/loading-fallback';
import VerifyToken from './verify-token';

import { ReactQueryProvider } from '@/tools/react-query-provider';

import 'react-datepicker/dist/react-datepicker.css';
import './globals.css';
import '@digheontech/digh.ui/style';

interface IRootLayout {
  children: ReactNode;
}

export default function RootLayout({ children }: IRootLayout) {
  return (
    <html lang="es" className={`!text-slate-600 text-sm ${section.variable} font-section hydrated`}>
      <head>
        <meta charSet="UTF-8" />
        <link rel="icon" type="image/png" href="/favicon.png" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="view-transition" content="same-origin" />
        <title>PhyCorr Claims</title>
      </head>
      <body className="min-h-screen">
        <ReactQueryProvider>
          <AppProvider>
            <TokenInterceptorProvider />
            <Suspense fallback={<LoadingFallback />}>
              <VerifyToken>{children}</VerifyToken>
              <Toast position="top-center" />
            </Suspense>
          </AppProvider>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
