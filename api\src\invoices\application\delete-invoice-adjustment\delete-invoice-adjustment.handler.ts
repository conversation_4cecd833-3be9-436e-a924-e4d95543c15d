import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';

import { InvoiceAdjustmentRepository } from 'src/invoices/infrastructure/repositories/invoice-adjustments.repository';

import { DeleteInvoiceAdjustmentCommand } from './delete-invoice-adjustment.command';

@CommandHandler(DeleteInvoiceAdjustmentCommand)
export class DeleteInvoiceAdjustmentHandler
  implements ICommandHandler<DeleteInvoiceAdjustmentCommand>
{
  constructor(
    private readonly invoiceAdjustmentRepository: InvoiceAdjustmentRepository,
  ) {}

  async execute(command: DeleteInvoiceAdjustmentCommand) {
    await this.invoiceAdjustmentRepository.removeByOriginalInvoiceId(
      command.originalInvoiceId,
    );

    return { invoiceId: command.originalInvoiceId };
  }
}
