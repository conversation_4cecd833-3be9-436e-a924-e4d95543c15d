import { Key } from 'react';
import { FieldArray } from 'formik';
import { Form<PERSON>ield, FieldDatePicker } from '@/components';
import { Button } from '@digheontech/digh.ui';
import { ConditionCodeSelect, OccurrenceCodeSelect, ValueAmountCodeSelect } from '../selects';

export function UB04Items18_28({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 p-4">
      <h3 className="text-lg font-bold mb-4">Condition and Occurrence Codes (Fields 18-28)</h3>

      {/* Campos 18-28: Condition Codes */}
      <div className="mb-6">
        <h4 className="font-semibold mb-2">18-28. Condition Codes</h4>
        <FieldArray name="conditionCodes">
          {({ push, remove, form }) => (
            <div>
              <div className="grid grid-cols-6 gap-2 mb-2">
                {form.values.conditionCodes?.map((_: any, index: number) => (
                  <div key={index} className="flex gap-1">
                    <ConditionCodeSelect name={`conditionCodes.${index}`} disabled={isLoading} />
                    <Button type="button" onPress={() => remove(index)}>
                      ×
                    </Button>
                  </div>
                ))}
              </div>
              <Button
                type="button"
                onPress={() => push('')}
                disabled={form.values.conditionCodes?.length >= 11}
              >
                Add Condition Code
              </Button>
            </div>
          )}
        </FieldArray>
      </div>

      {/* Occurrence Codes */}
      <div className="mb-6">
        <h4 className="font-semibold mb-2">Occurrence Codes</h4>
        <FieldArray name="occurrenceCodes">
          {({ push, remove, form }) => (
            <div>
              <div className="grid grid-cols-2 gap-4 mb-2">
                <span className="font-medium">Code</span>
                <span className="font-medium">Date</span>
              </div>
              {form.values.occurrenceCodes?.map((_: any, index: Key | null | undefined) => (
                <div key={index} className="grid grid-cols-2 gap-4 mb-2">
                  <OccurrenceCodeSelect
                    name={`occurrenceCodes.${index}.code`}
                    disabled={isLoading}
                  />
                  <div className="flex gap-1">
                    <FieldDatePicker name={`occurrenceCodes.${index}.date`} disabled={isLoading} />
                    <Button type="button" onPress={() => remove(Number(index))}>
                      ×
                    </Button>
                  </div>
                </div>
              ))}
              <Button type="button" onPress={() => push({ code: '', date: '' })}>
                Add Occurrence Code
              </Button>
            </div>
          )}
        </FieldArray>
      </div>

      {/* Value Amount Codes */}
      <div>
        <h4 className="font-semibold mb-2">Value Amount Codes</h4>
        <FieldArray name="valueAmountCodes">
          {({ push, remove, form }) => (
            <div>
              <div className="grid grid-cols-2 gap-4 mb-2">
                <span className="font-medium">Code</span>
                <span className="font-medium">Amount</span>
              </div>
              {form.values.valueAmountCodes?.map((_: any, index: Key | null | undefined) => (
                <div key={index} className="grid grid-cols-2 gap-4 mb-2">
                  <ValueAmountCodeSelect
                    name={`valueAmountCodes.${index}.code`}
                    disabled={isLoading}
                  />
                  <div className="flex gap-1">
                    <FormField
                      name={`valueAmountCodes.${index}.amount`}
                      type="number"
                      step={0.01}
                      disabled={isLoading}
                    />
                    <Button type="button" onPress={() => remove(Number(index))}>
                      ×
                    </Button>
                  </div>
                </div>
              ))}
              <Button type="button" onPress={() => push({ code: '', amount: 0 })}>
                Add Value Amount Code
              </Button>
            </div>
          )}
        </FieldArray>
      </div>
    </section>
  );
}
