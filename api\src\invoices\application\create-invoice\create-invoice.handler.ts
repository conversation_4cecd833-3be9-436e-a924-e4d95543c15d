import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>CommandHand<PERSON> } from '@nestjs/cqrs';
import { CreateInvoiceCommand } from './create-invoice.command';
import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';
import { InvoiceStatus } from 'src/shared/enums/invoice-status.enum';
import { PatientRepository } from 'src/patients/infrastructure/repositories/patient.repository';
import { concatStrings } from 'src/shared/utils';

@CommandHandler(CreateInvoiceCommand)
export class CreateInvoiceHandler
  implements ICommandHandler<CreateInvoiceCommand>
{
  constructor(
    private readonly invoiceRepository: InvoiceRepository,
    private readonly patientRepository: PatientRepository,
  ) {}

  async execute(command: CreateInvoiceCommand) {
    const { userId, ...rest } = command;
    let patientParsed;

    const patient = await this.patientRepository.searchByRecordNumber(
      command.patientRecordNumber,
    );

    if (patient) {
      patientParsed = {
        fullName: concatStrings(
          patient.firstName,
          patient.middleName,
          patient.surname,
        ),
        birthDate: patient.birthdate,
        gender: patient.gender,
        address: patient.address,
        phoneNumber: patient.phoneNumber,
      };
    }

    return this.invoiceRepository.create({
      ...rest,
      createdBy: userId,
      status: InvoiceStatus.Draft,
      patient: patientParsed,
    });
  }
}
