import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Http } from '@/config/http';
import { ICompletedInvoice } from '@/interfaces';

const key = ['list_invoices_complete'];

export const useGetInvoicesComplete = () => {
  const queryClient = useQueryClient();

  const {
    data: invoices,
    isLoading,
    isError,
    error,
  } = useQuery<ICompletedInvoice[]>({
    queryKey: key,
    queryFn: () => Http.get('invoices/completed').then(({ data }): ICompletedInvoice[] => data),
  });

  const invalidateInvoices = () => queryClient.invalidateQueries({ queryKey: key });

  return { invoices, invalidateInvoices, isLoading, isError, error };
};
