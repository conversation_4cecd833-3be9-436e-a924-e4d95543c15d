import { usePathname } from 'next/navigation';
import { LoadingFallback } from '@/components/loading-fallback';
import useLoggedUser from '@/hooks/use-logged-user';
import GuestRoutes from './guest-routes';
import { useVerifyToken } from '@/hooks/use-verify-token';

interface VerifyTokenProps {
  children: React.ReactNode;
}

const VerifyToken = ({ children }: VerifyTokenProps) => {
  const pathname = usePathname();
  const { isVerifyingToken } = useVerifyToken();
  const { loggedUser } = useLoggedUser();

  if (isVerifyingToken) {
    return (
      <section className="flex h-screen w-full items-center justify-center">
        <LoadingFallback />
      </section>
    );
  }

  return <>{loggedUser ? children : <GuestRoutes pathname={pathname} />}</>;
};

export default VerifyToken;
