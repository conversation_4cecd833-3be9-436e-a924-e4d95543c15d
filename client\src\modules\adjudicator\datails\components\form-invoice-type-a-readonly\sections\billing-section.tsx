import React from 'react';
import Image from 'next/image';
import { AuditableSection } from '../../auditable-section';
import { FieldCMS, CMSCheckbox } from '../ui/cms-field';
import { isFieldInAdjustments, getOriginalFieldValue } from '../utils';
import { SectionProps } from '../interfaces';

export const BillingSection = ({ invoice }: SectionProps) => {
  return (
    <section className="my-4">
      <div className="grid grid-cols-2 gap-1">
        <div className="grid grid-cols-3 gap-2">
          <AuditableSection title="Auditoria" fieldName="federalTaxIdNumber">
            <FieldCMS
              className="h-16"
              label="25. Federal Tax ID Number"
              value={
                <div className="flex justify-center items-center gap-2">
                  {getOriginalFieldValue(invoice, 'federalTaxIdNumber', invoice.federalTaxIdNumber)}{' '}
                  -
                  <div className=" flex items-center gap-2">
                    <span className="text-primary-700 font-bold text-sm">Type</span>
                    <span className="flex items-center text-xs mr-2">
                      <CMSCheckbox
                        checked={
                          getOriginalFieldValue(
                            invoice,
                            'federalTaxIdNumberType',
                            invoice.federalTaxIdNumberType,
                          ) === 'EIN'
                        }
                      />{' '}
                      EIN
                    </span>
                    <span className="flex items-center text-xs mr-2">
                      <CMSCheckbox
                        checked={
                          getOriginalFieldValue(
                            invoice,
                            'federalTaxIdNumberType',
                            invoice.federalTaxIdNumberType,
                          ) === 'SSN'
                        }
                      />{' '}
                      SSN
                    </span>
                  </div>
                </div>
              }
              isVerified={isFieldInAdjustments(invoice, 'federalTaxIdNumber')}
            />
          </AuditableSection>

          <AuditableSection title="Auditoria" fieldName="patientAccountNumber">
            <FieldCMS
              className="h-16"
              label="26. Patient Account No."
              value={getOriginalFieldValue(
                invoice,
                'patientAccountNumber',
                invoice.patientAccountNumber,
              )}
              isVerified={isFieldInAdjustments(invoice, 'patientAccountNumber')}
            />
          </AuditableSection>

          <AuditableSection title="Auditoria" fieldName="acceptAssignment">
            <FieldCMS
              className="h-16"
              label="27. Accept Assignment"
              value={
                <span className="flex gap-2">
                  <span>
                    YES{' '}
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'acceptAssignment',
                          invoice.acceptAssignment,
                        ) === 'YES'
                      }
                    />
                  </span>
                  <span>
                    NO{' '}
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'acceptAssignment',
                          invoice.acceptAssignment,
                        ) === 'NO'
                      }
                    />
                  </span>
                </span>
              }
              isVerified={isFieldInAdjustments(invoice, 'acceptAssignment')}
            />
          </AuditableSection>
        </div>

        <div className="grid grid-cols-3 gap-1 mb-2">
          <FieldCMS
            label="28. Total Charge"
            value={getOriginalFieldValue(invoice, 'totalCharge', invoice.totalCharge)}
            className="h-16"
            isVerified={isFieldInAdjustments(invoice, 'totalCharge')}
          />

          <AuditableSection title="Auditoria" fieldName="amountPaid">
            <FieldCMS
              label="29. Amount Paid"
              value={getOriginalFieldValue(invoice, 'amountPaid', invoice.amountPaid)}
              className="h-16"
              isVerified={isFieldInAdjustments(invoice, 'amountPaid')}
            />
          </AuditableSection>

          <FieldCMS
            label="30. Reserved For NUCC use"
            value={null}
            className="h-16"
            isVerified={false}
          />
        </div>
      </div>
      <div className="grid grid-cols-3 gap-1 mb-2">
        <FieldCMS
          label="31. Signature of physician or supplier including degrees or credentials"
          value={
            <div className="flex flex-col gap-12">
              <small>
                (l certify that the statements on the reverse apply to this bill and are made a part
                thereof.)
              </small>

              <div className="flex justify-evenly">
                <div>
                  <span className="text-primary-700 font-bold text-sm">Signature</span>
                  {getOriginalFieldValue(
                    invoice,
                    'physicianSignature',
                    invoice.physicianSignature,
                  ) ? (
                    <Image
                      src={getOriginalFieldValue(
                        invoice,
                        'physicianSignature',
                        invoice.physicianSignature,
                      )}
                      alt="Physician Signature"
                      width={200}
                      height={200}
                    />
                  ) : (
                    '-'
                  )}
                </div>
                <AuditableSection title="Auditoria" fieldName="physicianSignatureDate">
                  <FieldCMS
                    label="DATE:"
                    value={
                      getOriginalFieldValue(
                        invoice,
                        'physicianSignatureDate',
                        invoice.physicianSignatureDate,
                      )
                        ? new Date(
                            getOriginalFieldValue(
                              invoice,
                              'physicianSignatureDate',
                              invoice.physicianSignatureDate,
                            ),
                          ).toLocaleDateString()
                        : '-'
                    }
                    isVerified={isFieldInAdjustments(invoice, 'physicianSignatureDate')}
                  />
                </AuditableSection>
              </div>
            </div>
          }
        />

        <AuditableSection title="Auditoria" fieldName="serviceFacilityLocation.name">
          <FieldCMS
            label="32. Service Facility Location"
            value={
              <address className="flex flex-col gap-2 text-slate-700 text-base mt-2">
                <p>
                  {getOriginalFieldValue(
                    invoice,
                    'serviceFacilityLocation.name',
                    invoice.serviceFacilityLocation?.name,
                  ) ||
                    getOriginalFieldValue(
                      invoice,
                      'serviceFacilityLocation.address',
                      invoice.serviceFacilityLocation?.address,
                    )}
                </p>
                <p className="text-slate-600">
                  {`${getOriginalFieldValue(invoice, 'serviceFacilityLocation.city', invoice.serviceFacilityLocation?.city)}, ${getOriginalFieldValue(invoice, 'serviceFacilityLocation.state', invoice.serviceFacilityLocation?.state)} ${getOriginalFieldValue(invoice, 'serviceFacilityLocation.zipCode', invoice.serviceFacilityLocation?.zipCode)}`}
                </p>
                <p className="text-primary-500">
                  A:
                  <span className="text-slate-600 text-base mr-2">
                    {getOriginalFieldValue(
                      invoice,
                      'serviceFacilityLocation.npi',
                      invoice.serviceFacilityLocation?.npi,
                    )}
                  </span>
                </p>
                <p className="text-primary-500">
                  B:
                  <span className="text-slate-600 text-base mr-2">
                    {getOriginalFieldValue(
                      invoice,
                      'serviceFacilityLocation.otherId',
                      invoice.serviceFacilityLocation?.otherId,
                    )}
                  </span>
                </p>
              </address>
            }
            isVerified={isFieldInAdjustments(invoice, 'serviceFacilityLocation.name')}
          />
        </AuditableSection>

        <AuditableSection title="Auditoria" fieldName="billingProvider.name">
          <FieldCMS
            label="33. Billing Provider Info & PH#"
            value={
              <address className="flex flex-col gap-2 text-slate-700 text-base mt-2">
                <p>
                  {getOriginalFieldValue(
                    invoice,
                    'billingProvider.name',
                    invoice.billingProvider?.name,
                  ) ||
                    getOriginalFieldValue(
                      invoice,
                      'billingProvider.address',
                      invoice.billingProvider?.address,
                    )}
                </p>
                <p className="text-slate-600">
                  {`${getOriginalFieldValue(invoice, 'billingProvider.city', invoice.billingProvider?.city)}, ${getOriginalFieldValue(invoice, 'billingProvider.state', invoice.billingProvider?.state)} ${getOriginalFieldValue(invoice, 'billingProvider.zipCode', invoice.billingProvider?.zipCode)}`}
                </p>
                <p className="text-primary-500">
                  <span className="text-slate-600 text-base mr-2">
                    Phone:{' '}
                    {getOriginalFieldValue(
                      invoice,
                      'billingProvider.phoneNumber',
                      invoice.billingProvider?.phoneNumber,
                    )}
                  </span>
                </p>
                <p className="text-primary-500">
                  A:
                  <span className="text-slate-600 text-base mr-2">
                    {getOriginalFieldValue(
                      invoice,
                      'billingProvider.npi',
                      invoice.billingProvider?.npi,
                    )}
                  </span>
                </p>

                <p className="text-primary-500">
                  B:
                  <span className="text-slate-600 text-base mr-2">
                    {getOriginalFieldValue(
                      invoice,
                      'billingProvider.otherId',
                      invoice.billingProvider?.otherId,
                    )}
                  </span>
                </p>
              </address>
            }
            isVerified={isFieldInAdjustments(invoice, 'billingProvider.name')}
          />
        </AuditableSection>
      </div>
    </section>
  );
};
