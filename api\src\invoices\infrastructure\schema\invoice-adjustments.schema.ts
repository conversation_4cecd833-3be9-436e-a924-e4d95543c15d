import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { SupplementalInformation } from './invoice.schema';

@Schema()
export class Address {
  @Prop()
  street?: string;

  @Prop()
  city?: string;

  @Prop()
  state?: string;

  @Prop()
  zipCode?: string;
}

@Schema()
export class Patient {
  @Prop()
  fullName?: string;

  @Prop()
  birthDate?: Date;

  @Prop()
  gender?: string;

  @Prop({
    _id: false,
    type: SchemaFactory.createForClass(Address),
  })
  address?: Address;

  @Prop()
  phoneNumber?: string;
}

@Schema()
export class Insured {
  @Prop()
  fullName?: string;

  @Prop({
    _id: false,
    type: SchemaFactory.createForClass(Address),
  })
  address?: Address;

  @Prop()
  phoneNumber?: string;

  @Prop()
  policyGroupNumber?: string;

  @Prop()
  birthDate?: Date;

  @Prop()
  gender?: string;

  @Prop()
  planOrProgramName?: string;

  @Prop()
  anotherHealthBenefitPlan?: boolean;
}

@Schema()
export class SupplementalInformationAdjustment extends SupplementalInformation {
  @Prop()
  reason?: string[];
}
@Schema()
export class ServiceFacilityLocation {
  @Prop()
  name?: string;

  @Prop()
  address?: string;

  @Prop()
  city?: string;

  @Prop()
  state?: string;

  @Prop()
  zipCode?: string;

  @Prop()
  npi?: string;

  @Prop()
  otherId?: string;
}

@Schema({ _id: false })
export class BillingProvider {
  @Prop()
  name?: string;

  @Prop()
  phoneNumber?: string;

  @Prop()
  address?: string;

  @Prop()
  city?: string;

  @Prop()
  state?: string;

  @Prop()
  zipCode?: string;

  @Prop()
  npi?: string;

  @Prop()
  otherId?: string;
}

@Schema({ collection: 'invoice-adjustments', timestamps: true })
export class InvoiceAdjustment {
  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  patientRecordNumber: string;

  @Prop({ required: true })
  createdBy: string;

  @Prop()
  status: string;

  @Prop()
  insuranceCarrier?: string;

  @Prop()
  insuranceIdNumber?: string;

  @Prop()
  patientRelationshipToInsured?: string;

  @Prop({
    _id: false,
    type: SchemaFactory.createForClass(Patient),
  })
  patient?: Patient;

  @Prop({
    _id: false,
    type: SchemaFactory.createForClass(Insured),
  })
  insured?: Insured;

  @Prop()
  otherInsuredName?: string;

  @Prop()
  otherInsuredPolicyOrGroupNumber?: string;

  @Prop()
  otherInsuredPlanNameOrProgramName?: string;

  @Prop()
  conditionPatientRelatedToEmployment?: boolean;

  @Prop()
  conditionPatientRelatedToAutoAccident?: boolean;

  @Prop()
  conditionPatientRelatedToAutoAccidentPlace?: string;

  @Prop()
  conditionPatientRelatedToOtherAccident?: boolean;

  @Prop()
  patientOrAuthorized?: string;

  @Prop()
  patientOrAuthorizedSignatureDate?: Date;

  @Prop()
  insuredOrAuthorizedSignature?: string;

  // item 14
  @Prop()
  dateOfCurrentIllnessInjuryPregnancy?: Date;

  @Prop()
  qualifierOfCurrentIllnessInjuryAccident?: string;

  // item 15
  @Prop()
  otherDateConditionOfIllnessOrTreatment?: Date;

  @Prop()
  qualifierOfOtherConditionOfIllnessOrTreatment?: string;

  // item 16
  @Prop()
  unableToWorkFromDate?: Date;

  @Prop()
  unableToWorkToDate?: Date;

  // item 17
  @Prop()
  referringProviderName?: string;

  @Prop()
  referringProviderQualifier?: string;

  // item 17a
  @Prop()
  referringProviderOtherId?: string;

  @Prop()
  referringProviderOtherIdQualifier?: string;

  // item 17b
  @Prop()
  referringProviderNpi?: string;

  // item 18
  @Prop()
  hospitalizationFromDate?: Date;

  @Prop()
  hospitalizationToDate?: Date;

  // item 20
  @Prop()
  isOutsideLab?: boolean;

  @Prop()
  outsideLabCharges?: number;

  // item 21
  @Prop()
  icd10DiagnosisCodesForDiseasesOrInjuries?: string[];

  // item 22
  @Prop()
  resubmissionCode?: string;

  @Prop()
  originalReferenceNumber?: string;

  // item 23
  @Prop()
  priorAuthorizationNumber?: string;

  // item 24
  @Prop()
  supplementalInformation?: SupplementalInformationAdjustment[];

  // item 25
  @Prop()
  federalTaxIdNumber?: string;

  @Prop()
  federalTaxIdNumberType?: string;

  // item 26
  @Prop()
  patientAccountNumber?: string;

  // item 27
  @Prop()
  acceptAssignment?: boolean;

  // item 28
  @Prop()
  totalCharge?: number;

  // item 29
  @Prop()
  amountPaid?: number;

  // item 31
  @Prop()
  physicianSignature?: string;

  @Prop()
  physicianSignatureDate?: Date;

  // item 32
  @Prop({
    _id: false,
    type: SchemaFactory.createForClass(ServiceFacilityLocation),
  })
  @Prop()
  serviceFacilityLocation?: ServiceFacilityLocation;

  // item 33

  @Prop({
    default: {},
    type: SchemaFactory.createForClass(BillingProvider),
  })
  billingProvider?: BillingProvider;

  @Prop({ default: 0 })
  fileCount?: number;

  @Prop()
  confirmSign?: string;

  @Prop()
  confirmDate?: Date;

  @Prop()
  originalInvoiceId?: string;
}

export type InvoiceAdjustmentDocument = InvoiceAdjustment & Document;

export const InvoiceAdjustmentSchema =
  SchemaFactory.createForClass(InvoiceAdjustment);
