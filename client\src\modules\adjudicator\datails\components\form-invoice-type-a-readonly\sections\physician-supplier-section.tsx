import React from 'react';
import { AuditableSection } from '../../auditable-section';
import { FieldCMS, CMSCheckbox } from '../ui/cms-field';
import { isFieldInAdjustments, getOriginalFieldValue } from '../utils';
import { SectionProps } from '../interfaces';

export const PhysicianSupplierSection = ({ invoice }: SectionProps) => {
  return (
    <div className="my-4">
      <div className="text-primary-700 font-bold uppercase text-xl mb-2 border-b border-sky-200">
        Physician or Supplier Information
      </div>

      <div className="grid grid-cols-2 gap-1 mb-2">
        <div className="p-2 bg-white">
          <div className="grid grid-cols-2 gap-12 mb-2">
            <AuditableSection title="Auditoria" fieldName="dateOfCurrentIllnessInjuryPregnancy">
              <div className="flex justify-start gap-14">
                <FieldCMS
                  label="14. Date of current"
                  value={
                    getOriginalFieldValue(
                      invoice,
                      'dateOfCurrentIllnessInjuryPregnancy',
                      invoice.dateOfCurrentIllnessInjuryPregnancy,
                    ) ? (
                      new Date(
                        getOriginalFieldValue(
                          invoice,
                          'dateOfCurrentIllnessInjuryPregnancy',
                          invoice.dateOfCurrentIllnessInjuryPregnancy,
                        ),
                      ).toLocaleDateString()
                    ) : (
                      <span className="text-gray-400">-</span>
                    )
                  }
                  isVerified={isFieldInAdjustments(invoice, 'dateOfCurrentIllnessInjuryPregnancy')}
                />
                <FieldCMS
                  label="QUAL"
                  value={
                    getOriginalFieldValue(
                      invoice,
                      'qualifierOfCurrentIllnessInjuryAccident',
                      invoice.qualifierOfCurrentIllnessInjuryAccident,
                    ) || <span className="text-gray-400">-</span>
                  }
                  isVerified={isFieldInAdjustments(
                    invoice,
                    'qualifierOfCurrentIllnessInjuryAccident',
                  )}
                />
              </div>
            </AuditableSection>
            <AuditableSection title="Auditoria" fieldName="otherDateConditionOfIllnessOrTreatment">
              <div className="flex justify-start gap-14">
                <FieldCMS
                  label="15. Other Date"
                  value={
                    getOriginalFieldValue(
                      invoice,
                      'otherDateConditionOfIllnessOrTreatment',
                      invoice.otherDateConditionOfIllnessOrTreatment,
                    ) ? (
                      new Date(
                        getOriginalFieldValue(
                          invoice,
                          'otherDateConditionOfIllnessOrTreatment',
                          invoice.otherDateConditionOfIllnessOrTreatment,
                        ),
                      ).toLocaleDateString()
                    ) : (
                      <span className="text-gray-400">-</span>
                    )
                  }
                  isVerified={isFieldInAdjustments(
                    invoice,
                    'otherDateConditionOfIllnessOrTreatment',
                  )}
                />
                <FieldCMS
                  label="QUAL"
                  value={
                    getOriginalFieldValue(
                      invoice,
                      'qualifierOfOtherConditionOfIllnessOrTreatment',
                      invoice.qualifierOfOtherConditionOfIllnessOrTreatment,
                    ) || <span className="text-gray-400">-</span>
                  }
                  isVerified={isFieldInAdjustments(
                    invoice,
                    'qualifierOfOtherConditionOfIllnessOrTreatment',
                  )}
                />
              </div>
            </AuditableSection>
          </div>
        </div>
        <AuditableSection title="Auditoria" fieldName="unableToWorkFromDate">
          <div className="p-2 bg-white">
            <div className="flex gap-2">
              <FieldCMS
                label="16. Dates patient unable to work in current occupation"
                value={
                  <div className="flex gap-2 items-center">
                    <span className="text-xs">
                      <span className="ml-4 text-[10px] text-primary-700 font-bold">FROM: </span>
                      {getOriginalFieldValue(
                        invoice,
                        'unableToWorkFromDate',
                        invoice.unableToWorkFromDate,
                      ) ? (
                        new Date(
                          getOriginalFieldValue(
                            invoice,
                            'unableToWorkFromDate',
                            invoice.unableToWorkFromDate,
                          ),
                        ).toLocaleDateString()
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </span>
                    <span className="text-xs">
                      <span className="ml-4 text-[10px] text-primary-700 font-bold">TO: </span>
                      {getOriginalFieldValue(
                        invoice,
                        'unableToWorkToDate',
                        invoice.unableToWorkToDate,
                      ) ? (
                        new Date(
                          getOriginalFieldValue(
                            invoice,
                            'unableToWorkToDate',
                            invoice.unableToWorkToDate,
                          ),
                        ).toLocaleDateString()
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </span>
                  </div>
                }
                isVerified={isFieldInAdjustments(invoice, 'unableToWorkFromDate')}
              />
            </div>
          </div>
        </AuditableSection>
      </div>

      <div className="grid grid-cols-2 gap-1 mb-2">
        <AuditableSection title="Auditoria" fieldName="referringProviderName">
          <div className="p-2 bg-white flex gap-16">
            <FieldCMS
              label="17. Name of Referring Provider or Other Source"
              value={
                getOriginalFieldValue(
                  invoice,
                  'referringProviderName',
                  invoice.referringProviderName,
                ) || <span className="text-gray-400">-</span>
              }
              isVerified={isFieldInAdjustments(invoice, 'referringProviderName')}
            />
            <FieldCMS
              label="17a. QUAL / Other ID"
              value={
                <span className="flex gap-2 items-center">
                  <span>
                    {getOriginalFieldValue(
                      invoice,
                      'referringProviderQualifier',
                      invoice.referringProviderQualifier,
                    ) || <span className="text-gray-400">-</span>}
                  </span>
                  <span>
                    {getOriginalFieldValue(
                      invoice,
                      'referringProviderOtherId',
                      invoice.referringProviderOtherId,
                    ) || <span className="text-gray-400">-</span>}
                  </span>
                </span>
              }
              isVerified={isFieldInAdjustments(invoice, 'referringProviderQualifier')}
            />
            <FieldCMS
              label="17b. NPI"
              value={
                getOriginalFieldValue(
                  invoice,
                  'referringProviderNpi',
                  invoice.referringProviderNpi,
                ) || <span className="text-gray-400">-</span>
              }
              isVerified={isFieldInAdjustments(invoice, 'referringProviderNpi')}
            />
          </div>
        </AuditableSection>

        <AuditableSection title="Auditoria" fieldName="hospitalizationFromDate">
          <div className="p-2 bg-white">
            <div className="flex gap-2">
              <FieldCMS
                label="18. Hospitalization dates related to current services"
                value={
                  <div className="flex gap-2 items-center">
                    <span className="ml-2 text-xs">
                      FROM:{' '}
                      {getOriginalFieldValue(
                        invoice,
                        'hospitalizationFromDate',
                        invoice.hospitalizationFromDate,
                      ) ? (
                        new Date(
                          getOriginalFieldValue(
                            invoice,
                            'hospitalizationFromDate',
                            invoice.hospitalizationFromDate,
                          ),
                        ).toLocaleDateString()
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </span>
                    <span className="ml-2 text-xs">
                      TO:{' '}
                      {getOriginalFieldValue(
                        invoice,
                        'hospitalizationToDate',
                        invoice.hospitalizationToDate,
                      ) ? (
                        new Date(
                          getOriginalFieldValue(
                            invoice,
                            'hospitalizationToDate',
                            invoice.hospitalizationToDate,
                          ),
                        ).toLocaleDateString()
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </span>
                  </div>
                }
                isVerified={isFieldInAdjustments(invoice, 'hospitalizationFromDate')}
              />
            </div>
          </div>
        </AuditableSection>
      </div>

      <div className="grid grid-cols-2 gap-1 mb-2">
        <FieldCMS
          label="19. Additional Claim Information (Designated by NUCC)"
          value={<span className="text-gray-400">-</span>}
          isVerified={false}
        />
        <AuditableSection title="Auditoria" fieldName="isOutsideLab">
          <div className="p-2 bg-white flex gap-16">
            <FieldCMS
              label="20. OUTSIDE LAB?"
              value={
                <span className="flex gap-2 items-center">
                  <span className="flex items-center gap-1">
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(invoice, 'isOutsideLab', invoice.isOutsideLab) ===
                        'YES'
                      }
                    />{' '}
                    Yes
                  </span>
                  <span className="flex items-center gap-1">
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(invoice, 'isOutsideLab', invoice.isOutsideLab) ===
                        'NO'
                      }
                    />{' '}
                    No
                  </span>
                  <span className="ml-4 text-[10px] text-primary-700 font-bold">$ CHARGES:</span>
                  <span>
                    {getOriginalFieldValue(
                      invoice,
                      'outsideLabCharges',
                      invoice.outsideLabCharges,
                    ) || <span className="text-gray-400">-</span>}
                  </span>
                </span>
              }
              isVerified={isFieldInAdjustments(invoice, 'isOutsideLab')}
            />
          </div>
        </AuditableSection>
      </div>

      <div className="grid grid-cols-2 gap-1 mb-2">
        <div className="p-2 bg-white flex gap-2">
          <AuditableSection title="Auditoria" fieldName="icd10DiagnosisCodesForDiseasesOrInjuries">
            <FieldCMS
              label="21. Diagnosis Or Nature Of Illness Or Injury"
              value={
                <aside className="grid grid-cols-4 gap-4 mt-2">
                  {[...Array(12)].map((_, idx) => {
                    const letter = String.fromCharCode(65 + idx); // A-L
                    const originalValue = Array.isArray(
                      invoice.icd10DiagnosisCodesForDiseasesOrInjuries,
                    )
                      ? invoice.icd10DiagnosisCodesForDiseasesOrInjuries[idx]
                      : '';
                    const value = getOriginalFieldValue(
                      invoice,
                      `icd10DiagnosisCodesForDiseasesOrInjuries.${idx}`,
                      originalValue,
                    );
                    return (
                      <div key={letter} className="flex items-center">
                        <span className="font-bold text-primary-500 ml-4">{letter}.</span>
                        <span className="bg-slate-50 rounded px-2 py-1 min-w-[120px] text-xs border border-slate-200 ml-2">
                          {value || <span className="text-gray-400">-</span>}
                        </span>
                      </div>
                    );
                  })}
                </aside>
              }
              isVerified={isFieldInAdjustments(invoice, 'icd10DiagnosisCodesForDiseasesOrInjuries')}
            />
          </AuditableSection>
        </div>
        <div className="p-2 bg-white flex flex-col items-stretch gap-2">
          <AuditableSection title="Auditoria" fieldName="resubmissionCode">
            <div className="bg-white flex gap-2">
              <FieldCMS
                label="22. Medicaid Resubmission Code"
                value={
                  getOriginalFieldValue(invoice, 'resubmissionCode', invoice.resubmissionCode) || (
                    <span className="text-gray-400">-</span>
                  )
                }
                isVerified={isFieldInAdjustments(invoice, 'resubmissionCode')}
              />
              <FieldCMS
                label="Original Ref. NO.:"
                value={
                  getOriginalFieldValue(
                    invoice,
                    'originalReferenceNumber',
                    invoice.originalReferenceNumber,
                  ) || <span className="text-gray-400">-</span>
                }
                isVerified={isFieldInAdjustments(invoice, 'originalReferenceNumber')}
              />
            </div>
          </AuditableSection>
          <AuditableSection title="Auditoria" fieldName="priorAuthorizationNumber">
            <FieldCMS
              label="23. Prior Authorization Number"
              value={
                getOriginalFieldValue(
                  invoice,
                  'priorAuthorizationNumber',
                  invoice.priorAuthorizationNumber,
                ) || <span className="text-gray-400">-</span>
              }
              isVerified={isFieldInAdjustments(invoice, 'priorAuthorizationNumber')}
            />
          </AuditableSection>
        </div>
      </div>
    </div>
  );
};
