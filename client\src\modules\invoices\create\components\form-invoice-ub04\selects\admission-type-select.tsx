import { FormField } from '@/components';

interface AdmissionTypeSelectProps {
  name: string;
  label: string;
  disabled?: boolean;
}

export function AdmissionTypeSelect({ name, label, disabled }: AdmissionTypeSelectProps) {
  const admissionTypes = [
    { value: '1', label: 'Emergency' },
    { value: '2', label: 'Urgent' },
    { value: '3', label: 'Elective' },
    { value: '4', label: 'Newborn' },
    { value: '5', label: 'Trauma Center' },
    { value: '6', label: 'Reserved' },
    { value: '7', label: 'Reserved' },
    { value: '8', label: 'Reserved' },
    { value: '9', label: 'Information Not Available' },
  ];

  return (
    <FormField
      name={name}
      label={label}
      type="select"
      options={admissionTypes}
      disabled={disabled}
    />
  );
}
