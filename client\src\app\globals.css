@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';


@view-transition {
  navigation: auto;
}


@supports not (view-transition-name: none) {
  * {
    transition: opacity 0.2s ease-in-out;
  }
}


::view-transition-old(root),
::view-transition-new(root) {
  animation-duration: 300ms;
  animation-timing-function: ease-in-out;
}


.form-container {
  view-transition-name: form-content;
}

::view-transition-old(form-content),
::view-transition-new(form-content) {
  animation-duration: 250ms;
}


.invoice-table {
  view-transition-name: invoice-data;
}

::view-transition-old(invoice-data),
::view-transition-new(invoice-data) {
  animation-duration: 200ms;
}


.menu-container {
  view-transition-name: navigation-menu;
}

::view-transition-old(navigation-menu),
::view-transition-new(navigation-menu) {
  animation-duration: 200ms;
}


@media (prefers-reduced-motion: reduce) {

  ::view-transition-old(*),
  ::view-transition-new(*) {
    animation: none !important;
  }
}

@layer base {
  * {
    box-sizing: border-box;
  }
}