'use client';

import Link from 'next/link';

import PhyCorrLogo from '@/components/phy-corr-logo';
import { SignInForm } from '../components/sign-in-form';
import { useSignIn } from '../hooks/use-sign-in';

export function SignInPage() {
  const { signIn, isPending } = useSignIn();

  return (
    <div className="flex flex-col gap-10 items-center w-full max-w-[280px]">
      <PhyCorrLogo />
      <h1 className="text-[#0f75bc] text-2xl font-bold">PhyCorr Claims</h1>
      <div className="text-slate-700 text-sm font-bold">
        Cuidado de la salud rápido y sin complicaciones.
      </div>
      <SignInForm onSubmit={signIn} isPending={isPending} />
      <div className="flex gap-2">
        ¿Aún no te registras?
        <Link className="font-semibold  text-[#0f75bc]" href="/technician/sign-up">
          Crear una cuenta
        </Link>
      </div>
    </div>
  );
}
