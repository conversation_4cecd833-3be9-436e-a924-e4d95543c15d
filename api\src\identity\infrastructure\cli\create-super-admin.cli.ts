import { CommandBus } from '@nestjs/cqrs';
import { Command, CommandRunner, Option } from 'nest-commander';

import { CreateSuperAdminCommand } from 'src/identity/application/create-admin/create-super-admin.commands';

import { User } from 'src/identity/domain/user.model';

import { defaultSuperAdmin } from './default-super-admin';

interface CreateSuperAdminCliOptions {
  email: string;
  password: string;
}

@Command({ name: 'create-super-admin' })
export class CreateSuperAdminCli extends CommandRunner {
  constructor(private readonly commandBus: CommandBus) {
    super();
  }
  async run(_: string[], options: CreateSuperAdminCliOptions) {
    const createAdmin = new CreateSuperAdminCommand({
      firstName: defaultSuperAdmin.firstName,
      lastName: defaultSuperAdmin.lastName,
      email: options.email || defaultSuperAdmin.email,
      password: options.password,
      street: defaultSuperAdmin.address.street,
      zipCode: defaultSuperAdmin.address.zipCode,
      municipality: defaultSuperAdmin.address.municipality,
      phoneNumber: defaultSuperAdmin.phoneNumber,
    });

    const admin = await this.commandBus.execute<CreateSuperAdminCommand, User>(
      createAdmin,
    );

    console.log('Admin created successfully. ID : ', admin.id);
  }

  @Option({
    flags: '--email [string]',
  })
  parseEmail(value: string) {
    return value;
  }

  @Option({
    flags: '--password [string]',
    required: true,
  })
  parsePassword(value: string) {
    return value;
  }
}
