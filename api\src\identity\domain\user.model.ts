import { Language, UserRole } from 'src/shared/enums';
import { Address, UserDocument } from '../infrastructure/schemas/user.schema';

type ExtendedUserDocument = UserDocument & {
  createdAt?: Date;
  updatedAt?: Date;
};
// type ExtendedUserParams = {};

export class User {
  readonly id: string;
  readonly firstName: string;
  readonly lastName: string;
  readonly email?: string;
  readonly phone?: string;
  readonly address?: Address | object;
  readonly language?: Language;
  readonly role?: UserRole;
  readonly providerId?: string;
  readonly createdAt?: Date;
  readonly updatedAt?: Date;

  constructor(params: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
    address?: Address;
    language?: Language;
    role: UserRole;
    providerId?: string;
    createdAt: Date;
    updatedAt: Date;
  }) {
    this.id = params.id;
    this.firstName = params.firstName;
    this.lastName = params.lastName;
    this.email = params.email;
    this.phone = params.phone;
    this.address = params.address || {};
    this.language = params.language;
    this.role = params.role;
    this.providerId = params.providerId;
    this.createdAt = params.createdAt;
    this.updatedAt = params.updatedAt;
  }

  static fromModel(
    document: ExtendedUserDocument,
    // params: ExtendedUserParams = {},
  ): User {
    return new User({
      id: String(document._id),
      firstName: document.profile.firstName,
      lastName: document.profile.lastName,
      email: document.email,
      phone: document.phone,
      address: document.profile.address,
      language: document.language,
      role: document.role,
      providerId: document.providerId,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt,
    });
  }

  getUserInfo() {
    return {
      id: this.id,
      email: this.email,
      phone: this.phone,
      providerId: this.providerId,
      profile: {
        firstName: this.firstName,
        lastName: this.lastName,
        address: this.address,
      },
      role: this.role,
      language: this.language,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
