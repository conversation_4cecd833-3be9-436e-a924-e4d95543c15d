import React, { useEffect, useState } from 'react';
import { Button } from '@digheontech/digh.ui';
import { Form, Formik } from 'formik';
import { FieldRenderer } from '../form-invoice-type-a-readonly/ui/field-renderer';
import { useUpdateInvoiceAdjustment } from '../../hooks';

interface IAuditableSection {
  title: string | React.ReactNode;
  children: React.ReactNode;
  fieldName?: string;
  isLoading?: boolean;
  initialValue?: {};
}

export const AuditableSection = ({
  title,
  children,
  fieldName,
  isLoading = false,
  initialValue = {},
}: IAuditableSection) => {
  const [open, setOpen] = useState(false);
  const { update, isSuccess } = useUpdateInvoiceAdjustment();

  useEffect(() => {
    if (isSuccess) {
      setOpen(false);
    }
  }, [isSuccess]);

  return (
    <section className="relative group">
      <div
        className="cursor-pointer hover:ring-2 hover:ring-sky-400 rounded transition"
        onClick={() => setOpen(true)}
        title={`Agregar comentario a: ${title}`}
      >
        {children}
      </div>

      {open && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white rounded shadow-lg p-8 min-w-[700px] max-w-max w-full relative">
            <h2 className="text-sky-700 font-bold text-lg mb-2">{title}</h2>
            <div className="mb-4 border rounded p-2 bg-gray-50 max-h-[60vh] overflow-auto">
              {children}
            </div>

            <label htmlFor="comment" className="block text-xs font-semibold text-sky-700 mb-1">
              Agregar el valor correcto de la auditoria
            </label>

            {fieldName ? (
              <Formik
                initialValues={initialValue}
                enableReinitialize={true}
                onSubmit={(payload: any) => {
                  if (fieldName.includes('supplementalInformation')) {
                    const supplementalData = {
                      ...payload.supplementalInformation,
                      denialReason: payload.denialReason,
                    };

                    if (!supplementalData._id) {
                      return;
                    }

                    const supplementalPayload = {
                      supplementalInformation: supplementalData,
                    };

                    update(supplementalPayload);
                  } else {
                    update(payload);
                  }
                }}
              >
                {formikProps => (
                  <Form>
                    <div className="p-4">
                      <FieldRenderer
                        fieldName={fieldName}
                        isLoading={isLoading}
                        initialValues={initialValue}
                        formikProps={formikProps}
                      />
                    </div>
                    <div className="flex justify-end gap-2 mt-2">
                      <Button buttonType="primary" primary type="submit">
                        Guardar
                      </Button>
                      <Button buttonType="secondary" onPress={() => setOpen(false)}>
                        Cancelar
                      </Button>
                    </div>
                  </Form>
                )}
              </Formik>
            ) : (
              <div className="flex justify-end gap-2 mt-2">
                <Button buttonType="primary" primary onPress={() => setOpen(false)}>
                  Cerrar
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </section>
  );
};
