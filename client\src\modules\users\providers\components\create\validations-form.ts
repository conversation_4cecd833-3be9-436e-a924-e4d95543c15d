import * as Yup from 'yup';

import { IFormAdjudicatorValues } from '@/interfaces';

export const fieldsUser = [
  { name: 'firstName', label: 'Nombre', placeholder: 'Nombre' },
  { name: 'lastName', label: 'Apellid<PERSON>', placeholder: '<PERSON>pel<PERSON><PERSON>' },
  { name: 'email', label: 'Correo Electrónico', placeholder: 'Correo Electrónico' },
  { name: 'password', label: 'Contraseña', type: 'password', placeholder: 'Contraseña' },
];

export const initialValues: IFormAdjudicatorValues = {
  firstName: '',
  lastName: '',
  email: '',
  password: '',
};

export const validationSchema = Yup.object().shape({
  firstName: Yup.string()
    .trim()
    .required('El nombre es requerido')
    .min(2, 'El nombre debe tener al menos 2 caracteres'),

  lastName: Yup.string()
    .trim()
    .required('El apellido es requerido')
    .min(2, 'El apellido debe tener al menos 2 caracteres'),

  email: Yup.string()
    .trim()
    .required('El correo electrónico es requerido')
    .email('El formato del correo electrónico no es válido')
    .matches(
      /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
      'El formato del correo electrónico no es válido',
    ),

  password: Yup.string()
    .required('La contraseña es requerida')
    .min(8, 'La contraseña debe tener al menos 8 caracteres'),
});
