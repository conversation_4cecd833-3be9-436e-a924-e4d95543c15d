import { useState, useEffect } from 'react';

export function useSidebarCollapsed(key = 'sidebar-collapsed') {
  const [collapsed, setCollapsed] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(key);
      return stored === 'true';
    }
    return false;
  });

  useEffect(() => {
    localStorage.setItem(key, String(collapsed));
  }, [collapsed, key]);

  return [collapsed, setCollapsed] as const;
}
