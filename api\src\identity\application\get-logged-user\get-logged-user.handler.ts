import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { User } from 'src/identity/domain/user.model';
import { AuthService } from 'src/identity/infrastructure/auth.service';
import { UserRepository } from 'src/identity/infrastructure/repositories/user.repository';
import { GetLoggedUserQuery } from './get-logged-user.query';

@CommandHandler(GetLoggedUserQuery)
export class GetLoggedUserHandler
  implements ICommandHandler<GetLoggedUserQuery>
{
  constructor(
    private readonly authService: AuthService,
    private readonly userRepository: UserRepository,
  ) {}

  async execute(query: GetLoggedUserQuery) {
    const user = User.fromModel(
      await this.userRepository.findById(query.userId),
    );

    return {
      token: await this.authService.generateToken({ userId: user.id }),
      user: user.getUserInfo(),
    };
  }
}
