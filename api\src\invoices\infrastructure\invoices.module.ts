import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { MongooseModule } from '@nestjs/mongoose';

import { FileManagementModule } from 'src/file-management/file-management.module';
import { PatientModule } from 'src/patients/infrastructure/patients.module';
import { CreateInvoiceHandler } from '../application/create-invoice/create-invoice.handler';
import { GetInvoiceDetailHandler } from '../application/get-invoice-detail/get-invoice-detail.handler';
import { UpdateInvoiceHandler } from '../application/update-invoice/update-invoice.handler';
import { GetInvoicesHandler } from '../application/get-invoices/get-invoices.handler';
import { UploadFileHandler } from '../application/upload-file/upload-file.handler';
import { GetInvoiceFilesHandler } from '../application/get-invoice-files/get-invoice-files.handler';
import { DeleteFileHandler } from '../application/delete-file/delete-file.handler';
import { GetFileSignedUrlHandler } from '../application/get-file-signed-url/get-file-signed-url.handler';
import { DeleteInvoiceHandler } from '../application/delete-invoice/delete-invoice.handler';
import { ConfirmInvoiceHandler } from '../application/confirm-invoice/confirm-invoice.handler';
import { UpdateFileHandler } from '../application/update-file/update-file.handler';
import { GetCompleteInvoicesHandler } from '../application/get-complete-invoices/get-complete-invoices.handler';
import { GetInvoiceAdjustmentsDetailHandler } from '../application/get-invoice-adjustments-detail/get-invoice-adjustments-detail.handler';
import { UpdateInvoiceAdjustmentHandler } from '../application/update-invoice-adjustment/update-invoice-adjustment.handler';
import { DeleteInvoiceAdjustmentHandler } from '../application/delete-invoice-adjustment/delete-invoice-adjustment.handler';
import { RemoveServiceAdjustmentHandler } from '../application/remove-service-adjustment/remove-service-adjustment.handler';
import { RemoveItemAdjustmentHandler } from '../application/remove-item-adjustment/remove-item-adjustment.handler';

import { InvoiceFile, InvoiceFileSchema } from './schema/invoice-file.schema';
import { Invoice, InvoiceSchema } from './schema/invoice.schema';
import {
  InvoiceAdjustment,
  InvoiceAdjustmentSchema,
} from './schema/invoice-adjustments.schema';
import { InvoiceRepository } from './repositories/invoice.repository';
import { InvoiceAdjustmentRepository } from './repositories/invoice-adjustments.repository';
import { InvoiceFileRepository } from './repositories/invoice-file.repository';
import { InvoicesController } from './invoices.controller';
@Module({
  imports: [
    CqrsModule,
    MongooseModule.forFeature([
      { name: Invoice.name, schema: InvoiceSchema },
      { name: InvoiceFile.name, schema: InvoiceFileSchema },
      { name: InvoiceAdjustment.name, schema: InvoiceAdjustmentSchema },
    ]),
    FileManagementModule,
    PatientModule,
  ],
  controllers: [InvoicesController],
  providers: [
    CreateInvoiceHandler,
    DeleteFileHandler,
    GetFileSignedUrlHandler,
    GetInvoiceDetailHandler,
    GetInvoiceFilesHandler,
    GetInvoicesHandler,
    InvoiceFileRepository,
    InvoiceAdjustmentRepository,
    InvoiceRepository,
    UpdateInvoiceHandler,
    UploadFileHandler,
    DeleteInvoiceHandler,
    ConfirmInvoiceHandler,
    UpdateFileHandler,
    GetCompleteInvoicesHandler,
    GetInvoiceAdjustmentsDetailHandler,
    UpdateInvoiceAdjustmentHandler,
    DeleteInvoiceAdjustmentHandler,
    RemoveServiceAdjustmentHandler,
    RemoveItemAdjustmentHandler,
  ],
  exports: [InvoiceFileRepository],
})
export class InvoicesModule {}
