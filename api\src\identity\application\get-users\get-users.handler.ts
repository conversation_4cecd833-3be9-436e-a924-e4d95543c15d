import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';

import { ProviderRepository } from 'src/providers/infrastructure/repositories/provider.repository';
import { UserRepository } from 'src/identity/infrastructure/repositories/user.repository';
import { UserStatus } from 'src/shared/enums/user-status.enum';

import { GetUsersQuery } from './get-users.query';

@QueryHandler(GetUsersQuery)
export class GetUsersHandler implements IQueryHandler<GetUsersQuery> {
  constructor(
    readonly userRepository: UserRepository,
    readonly providerRepository: ProviderRepository,
  ) {}
  async execute() {
    const users = await this.userRepository.getAllExceptSuperAdmin();
    return this.mapProviderInfo(users);
  }

  private async mapProviderInfo(data: Record<string, any>[]) {
    const providerIds = [...new Set(data.map(user => user.providerId))];
    const providers = await this.providerRepository.getAllByIds(providerIds);

    const providersNameMap = {};

    providers.forEach(provider => {
      providersNameMap[provider.id] = provider.name;
    });

    return data.map(user => {
      return {
        id: user.id,
        name: user.profile.firstName,
        lastName: user.profile.lastName,
        email: user.email,
        provider: providersNameMap[user.providerId],
        status: UserStatus.Active,
        role: user.role,
      };
    });
  }
}
