export enum Language {
  English = 'en',
  Spanish = 'es',
}

export const DEFAULT_LANGUAGE = Language.Spanish as const;

export enum HttpErrorCodesEnum {
  UNAUTHORIZED = 401,
}

export enum Gender {
  Male = 'M',
  Female = 'F',
}

export enum Relationship {
  Self = 'self',
  Spouse = 'spouse',
  Child = 'child',
  Other = 'other',
}

export enum InsuranceCarrier {
  MEDICARE = 'MEDICARE',
  MEDICAID = 'MEDICAID',
  TRICARE = 'TRICARE',
  CHAMPVA = 'CHAMPVA',
  GROUP_HEALTH_PLAN = 'GROUP HEALTH PLAN',
  FECA = 'FECA',
  OTHER = 'OTHER',
}

export enum RequestStatus {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
  Registered = 'registered',
}

export enum UserRole {
  Provider = 'provider',
  SuperAdmin = 'superAdmin',
  Adjudicator = 'adjudicator',
}

export enum ProvidersType {
  Private = 'private',
  Government = 'government',
}

export enum ProviderStatus {
  Active = 'active',
  Inactive = 'inactive',
  Unlinked = 'unlinked',
}

export enum InvoiceType {
  Type1500 = '1500',
  TypeADA = 'ADA',
  TypeUB04 = 'UB04',
  None = 'none',
}

export enum InvoiceStatus {
  Draft = 'draft',
  Admitted = 'admitted',
  Rejected = 'rejected',
  Standby = 'standby',
}

export enum YesOrNot {
  Yes = 'YES',
  No = 'NO',
}

export enum Item14Qualifier {
  Onset = '431',
  LastMenstrualPeriod = '484',
}

export enum Item15Qualifier {
  InitialTreatment = '454',
  LatestVisit = '304',
  AcuteCondition = '453',
  Accident = '439',
  LastXRay = '455',
  Prescription = '471',
  ReportStart = '090',
  ReportEnd = '091',
  FirstVisit = '444',
}

export enum Item17Role {
  ReferringProvider = 'DN',
  OrderingProvider = 'DK',
  SupervisingProvider = 'DQ',
}

export enum Item17aQualifier {
  StateLicense = '0B',
  ProviderUPIN = '1G',
  ProviderCommercial = 'G2',
  LocationNumber = 'LU',
}

export enum Item22ResubmissionCode {
  ReplacementOfPriorClaim = '7',
  VoidCancelOfPriorClaim = '8',
}

export enum ItemEPSDT {
  Available = 'AV',
  UnderTreatment = 'S2',
  NewServiceRequested = 'ST',
  NotUsed = 'NU',
}

export enum DocumentType {
  notaDeProgreso = "DOCTOR'S NOTE", // eslint-disable-line quotes
  notaDeProcedimiento = 'PROCEDURE NOTE',
  resultados = 'RESULTS',
  ordenMedica = 'MEDICAL ORDER',
  referido = 'REFERRAL',
}

export enum DenialReason {
  AdjustmentNotProceed = 'ADJUSTMENT_NOT_PROCEED',
  DocumentsNotVisible = 'DOCUMENTS_NOT_VISIBLE',
  EvidenceDoesNotMatch = 'EVIDENCE_DOES_NOT_MATCH',
  MissingOrIncorrectCode = 'MISSING_OR_INCORRECT_CODE',
  MissingDischargeSummary = 'MISSING_DISCHARGE_SUMMARY',
  MissingMedicalEvidence = 'MISSING_MEDICAL_EVIDENCE',
  DatesDoNotMatch = 'DATES_DO_NOT_MATCH',
  NotPhysicianCorrectional = 'NOT_PHYSICIAN_CORRECTIONAL',
  PatientNotBelongs = 'PATIENT_NOT_BELONGS',
  IncorrectProvider = 'INCORRECT_PROVIDER',
  TimeLimitExceeded = 'TIME_LIMIT_EXCEEDED',
}
