import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CqrsModule } from '@nestjs/cqrs';

import { SearchPatientHandler } from '../applications/search-patient/search-patient.handler';

import { PatientRepository } from './repositories/patient.repository';
import { Patient, PatientSchema } from './schema/patient.schema';
import { PatientsController } from './patients.controller';

@Module({
  imports: [
    CqrsModule,
    MongooseModule.forFeature([{ name: Patient.name, schema: PatientSchema }]),
  ],
  controllers: [PatientsController],
  providers: [SearchPatientHandler, PatientRepository],
  exports: [PatientRepository],
})
export class PatientModule {}
