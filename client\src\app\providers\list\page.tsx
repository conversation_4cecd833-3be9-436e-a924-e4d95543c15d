'use client';

import { AppMenu, SidebarHeaderTemplate } from '@/components';
import { useSidebarCollapsed } from '@/hooks/use-sidebar-collapsed';
import { ProvidersListPage } from '@/modules/providers';

export default function ProvidersList() {
  const [collapsed, setCollapsed] = useSidebarCollapsed();

  return (
    <SidebarHeaderTemplate
      sidebar={<AppMenu collapsed={collapsed} setCollapsed={setCollapsed} />}
      header={null}
      collapsed={collapsed}
    >
      <ProvidersListPage />
    </SidebarHeaderTemplate>
  );
}
