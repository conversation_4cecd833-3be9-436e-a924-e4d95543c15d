import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ collection: 'invoice-files', timestamps: true })
export class InvoiceFile extends Document {
  @Prop({ required: true, set: (id: string) => new Types.ObjectId(id) })
  invoiceId: string;

  @Prop({ required: true })
  url: string;

  @Prop()
  originalName: string;

  @Prop()
  size: number;

  @Prop()
  fileType: string;

  @Prop()
  fileName: string;

  @Prop()
  documentType: string;
}

export type InvoiceFileDocument = InvoiceFile & Document;

export const InvoiceFileSchema = SchemaFactory.createForClass(InvoiceFile);
