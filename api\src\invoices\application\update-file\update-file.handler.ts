import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { InvoiceFileRepository } from 'src/invoices/infrastructure/repositories/invoice-file.repository';

import { UpdateFileCommand } from './update-file.command';

@CommandHandler(UpdateFileCommand)
export class UpdateFileHandler implements ICommandHandler<UpdateFileCommand> {
  constructor(private readonly invoiceFileRepository: InvoiceFileRepository) {}

  async execute(command: UpdateFileCommand) {
    await this.invoiceFileRepository.findByIdAndInvoiceId(
      command.fileId,
      command.invoiceId,
      true,
    );

    const file = await this.invoiceFileRepository.update(command.fileId, {
      documentType: command.documentType,
      originalName: command.fileName,
    });

    return file;
  }
}
