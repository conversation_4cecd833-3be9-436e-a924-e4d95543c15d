import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, UpdateQuery } from 'mongoose';

import { User, UserDocument } from '../schemas/user.schema';
import { UserRole } from 'src/shared/enums';

/** Can be used to select only the specified fields from a query result */
type UserSelect = { [key in keyof UserDocument]?: boolean };

@Injectable()
export class UserRepository {
  constructor(
    @InjectModel(User.name)
    private userModel: Model<UserDocument>,
  ) {}

  async create(data: Partial<User>) {
    return this.userModel.create(data);
  }

  async findById(userId: string, failIfNotFound = false, select?: UserSelect) {
    const user = await this.userModel.findById(userId).select(select).exec();
    if (!user && failIfNotFound) {
      throw new NotFoundException(`User '${userId}' not found`);
    }
    return user;
  }

  async findByEmail(email: string, failIfNotFound = false) {
    const user = await this.userModel.findOne({ email }).exec();
    if (!user && failIfNotFound) {
      throw new NotFoundException(`User '${email}' not found`);
    }
    return user;
  }

  async findByPhone(phone: string, failIfNotFound = false) {
    const user = await this.userModel.findOne({ phone }).exec();
    if (!user && failIfNotFound) {
      throw new NotFoundException(`User '${phone}' not found`);
    }
    return user;
  }

  async update(id: string, data: UpdateQuery<UserDocument>) {
    return this.userModel
      .findByIdAndUpdate(id, data, { returnDocument: 'after' })
      .exec();
  }

  async updatePassword(userId: string, password: string) {
    return this.userModel.findByIdAndUpdate(userId, { password }).exec();
  }

  async getAllProviderUsers() {
    return this.userModel.find({ providerId: { $ne: null } }).exec();
  }

  async getAllExceptSuperAdmin() {
    return this.userModel.find({ role: { $ne: UserRole.SuperAdmin } }).exec();
  }
}
