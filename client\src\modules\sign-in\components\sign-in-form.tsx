'use client';

import Link from 'next/link';
import { Form, Formik } from 'formik';
import { Button } from '@digheontech/digh.ui';

import { FormField } from '@/components/form-field';
import { validationSchema } from './validation-schema';
import { SignInValues } from './sign-in.interface';

export function SignInForm({
  onSubmit,
  isPending,
}: {
  onSubmit: (values: SignInValues) => void;
  isPending: boolean;
}) {
  return (
    <Formik
      initialValues={{ email: '', password: '' }}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      <Form className="w-full flex flex-col gap-4">
        <FormField name="email" disabled={isPending} placeholder="Ingresar correo" />
        <FormField
          name="password"
          disabled={isPending}
          placeholder="Ingresar Contraseña"
          type="password"
        />

        <Link className="font-semibold text-slate-500 text-right" href="/password-recover">
          ¿Olvidó su contraseña?
        </Link>
        <Button
          type="submit"
          size="large"
          fullWidth
          disabled={isPending}
          className="bg-primary-100 border-primary-200 hover:bg-primary-300 text-slate-600"
        >
          Iniciar sesión
        </Button>
      </Form>
    </Formik>
  );
}
