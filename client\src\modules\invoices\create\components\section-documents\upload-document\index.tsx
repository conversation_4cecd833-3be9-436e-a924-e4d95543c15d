import { useCallback } from 'react';
import { Form, Formik } from 'formik';
import { MdFileUpload } from 'react-icons/md';
import { useMutation } from '@tanstack/react-query';
import { Label } from 'react-aria-components';
import { Button } from '@digheontech/digh.ui';
import { AxiosResponseError, Http } from '@/config/http';
import { useShowError, useShowSuccess } from '@/hooks';
import { DocumentType } from '@/enums';
import { FileUpload, FileUploadField, FormField } from '@/components';
import { uploadFormSchema } from './upload-form.schema';
import { DocumentTypeSelect } from '../../form-invoice-type-a/selects/document-type-select';
import { useDocumentList } from '../hooks';

interface UploadDocumentFormValues {
  file: File | null;
  fileName: string;
  documentType: DocumentType;
}

export function useUploadDocument(invoiceId: string) {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateFiles } = useDocumentList(invoiceId || '');

  const { mutateAsync } = useMutation({
    mutationKey: ['UPLOAD_DOCUMENT'],
    mutationFn: (formValues: UploadDocumentFormValues) => {
      const formData = new FormData();
      if (formValues.file) {
        formData.append('file', formValues.file);
        formData.append('fileName', formValues.fileName);
        formData.append('documentType', formValues.documentType);
      }
      return Http.post(`invoices/${invoiceId}/files`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
    },
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Documento subido.' });
      invalidateFiles();
    },
  });

  const onSubmit = useCallback(
    (values: UploadDocumentFormValues) => {
      mutateAsync(values);
    },
    [mutateAsync],
  );

  return { onSubmit };
}

interface IUploadDocument {
  invoiceId?: string;
}

interface UploadFormValues {
  file: File | null;
  fileName: string;
  documentType: DocumentType;
}

const INITIAL_VALUES: UploadFormValues = {
  file: null,
  fileName: '',
  documentType: DocumentType.notaDeProgreso,
};

export function UploadDocument({ invoiceId }: IUploadDocument) {
  const { onSubmit } = useUploadDocument(invoiceId || '');

  return (
    <div className="flex flex-col border border-slate-300">
      <header className="sticky top-0 w-full flex items-center justify-between text-lg text-slate-700 bg-primary-100 px-4 py-2 z-10">
        <strong>Seleccione un archivo</strong>
      </header>

      <Formik
        initialValues={INITIAL_VALUES}
        validationSchema={uploadFormSchema}
        onSubmit={(values, { resetForm }) => {
          onSubmit(values);
          resetForm();
        }}
      >
        {({ values, resetForm, isSubmitting }) => (
          <Form className="px-8 py-4">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <FormField
                disabled={isSubmitting}
                name="fileName"
                label="Nombre del archivo"
                placeholder="Nombre del archivo"
              />
              <div className="min-w-60">
                <Label className="text-slate-700">Tipo de documento</Label>
                <DocumentTypeSelect
                  className="w-full mt-1"
                  disabled={isSubmitting}
                  name="documentType"
                />
              </div>
            </div>
            <FileUploadField name="file">
              <FileUpload
                name="file"
                typesLabel="jpeg, jpg, png, pdf"
                maxSizeLabel="5 MB"
                mimeTypes={['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']}
              />
            </FileUploadField>

            {values.file && (
              <div className="flex justify-center gap-2 pt-4">
                <Button
                  disabled={isSubmitting}
                  fullWidth
                  type="button"
                  className="h-10 w-52"
                  buttonType="normal"
                  onPress={() => resetForm()}
                >
                  Cancelar
                </Button>

                <Button
                  leftIcon={<MdFileUpload />}
                  disabled={isSubmitting}
                  fullWidth
                  type="submit"
                  className="h-10 w-52"
                  primary
                >
                  Enviar
                </Button>
              </div>
            )}
          </Form>
        )}
      </Formik>
    </div>
  );
}
