{"compilerOptions": {"allowSyntheticDefaultImports": true, "baseUrl": "./", "declaration": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": false, "incremental": true, "jsx": "react-jsx", "module": "commonjs", "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "outDir": "./dist", "removeComments": true, "skipLibCheck": true, "sourceMap": true, "strictBindCallApply": false, "strictNullChecks": false, "target": "es2017", "resolveJsonModule": true, "allowJs": true}, "include": ["src/**/*", "src/**/*.json"]}