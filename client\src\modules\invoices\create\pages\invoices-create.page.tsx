'use client';

import { FormikProps } from 'formik';
import { useEffect, useState, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@digheontech/digh.ui';

import { LoadingFallback } from '@/components';
import { IFormInvoiceTypeAPayload, IFormInvoiceUB04Payload } from '@/interfaces';
import { YesOrNot, InvoiceType } from '@/enums';

import { initialValues } from '../components/form-invoice-type-a/validations-form';
import { initialValues as initialValuesUB04 } from '../components/form-invoice-ub04/validations-form';
import { FormInvoiceTypeA } from '../components/form-invoice-type-a/form-invoice-type-a';
import { FormInvoiceUB04 } from '../components/form-invoice-ub04/form-invoice-ub04';
import { useCreateInvoice, useGetInvoice, useUpdateInvoice } from '../hooks';
import { InvoicesSearch, PatientsSearch } from '../components/form-invoice-type-a/searchs';
import { ModalConfirmation } from '../components/form-invoice-type-a/modal-confirmation';
import { isRequiredForSubmission } from '../components/form-invoice-type-a/utils/required-fields';
import { useToast } from '@/hooks/useToast';

const INSURED_NAME = 'Physician Correctional (660725126)';
const { No, Yes } = YesOrNot;

export const toYesOrNot = (value: boolean | undefined | null | string): YesOrNot => {
  if (value === Yes || value === 'YES') return Yes;
  if (value === No || value === 'NO') return No;

  if (value === true) return Yes;
  return No;
};

export function InvoicesCreatePage() {
  const { triggerToast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const invoiceId = searchParams.get('invoiceId');

  const [selectedPatient, setSelectedPatient] = useState('');
  const [selectedInvoice, setSelectedInvoice] = useState('');
  const { invoices, isLoading } = useGetInvoice(invoiceId);
  const { create, isCreating, isSuccess, data } = useCreateInvoice();
  const { update, isUpdating } = useUpdateInvoice(invoiceId);

  const formikRefTypeA = useRef<FormikProps<IFormInvoiceTypeAPayload>>(null);
  const formikRefUB04 = useRef<FormikProps<IFormInvoiceUB04Payload>>(null);
  const [isFormComplete, setIsFormComplete] = useState(false);

  useEffect(() => {
    if (invoices) {
      setSelectedPatient(invoices.patientRecordNumber);
      setSelectedInvoice(invoices.type);
    }
  }, [invoices]);

  useEffect(() => {
    if (isSuccess && data) {
      router.replace(`/invoices/create?invoiceId=${data.data._id}`);
    }
  }, [isSuccess, data, router]);

  const handleStartBilling = () => {
    create({ patientRecordNumber: selectedPatient, type: selectedInvoice });
  };

  const handleUpdateInvoice = (payload: IFormInvoiceTypeAPayload | IFormInvoiceUB04Payload) => {
    if (selectedInvoice === InvoiceType.TypeUB04) {
      update({ ...payload } as unknown as IFormInvoiceTypeAPayload);
    } else {
      update(payload as IFormInvoiceTypeAPayload);
    }
  };

  const handleExternalSubmit = () => {
    const currentRef =
      selectedInvoice === InvoiceType.TypeUB04 ? formikRefUB04.current : formikRefTypeA.current;

    if (currentRef) {
      if (currentRef.isValid) {
        currentRef.submitForm();
      } else {
        if (currentRef.errors) {
          console.error(currentRef);
          triggerToast({
            title: 'Error',
            description: `Por favor, complete todos los campos obligatorios.${currentRef.errors}`,
          });
        }
      }
    }
  };

  // Crear valores iniciales para UB04
  const initialFormValuesUB04: IFormInvoiceUB04Payload = {
    ...initialValuesUB04,
    ...(invoices && selectedInvoice === InvoiceType.TypeUB04
      ? {
          providerName: invoices.providerName || '',
          providerNumber: invoices.providerNumber || '',
          patientName: invoices.patient?.fullName || '',
          patientAddress: invoices.patient?.address?.street || '',
          patientBirthDate: invoices.patient?.birthDate || '',
          patientSex: invoices.patient?.gender || '',
          admissionDate: invoices.admissionDate || '',
          admissionHour: invoices.admissionHour || '',
          patientId: invoices.patientId || '',
          federalTaxNumber: invoices.federalTaxNumber || '',
          admissionSource: invoices.admissionSource || '',
          admissionType: invoices.admissionType || '',
          dischargeHour: invoices.dischargeHour || '',
          pointOfOrigin: invoices.pointOfOrigin || '',
          dischargeStatus: invoices.dischargeStatus || '',
          conditionCodes: invoices.conditionCodes || [],
          occurrenceCodes: invoices.occurrenceCodes || [],
          occurrenceSpanCodes: invoices.occurrenceSpanCodes || [],
          valueAmountCodes: invoices.valueAmountCodes || [],
          procedureCodes: invoices.procedureCodes || [],
          attendingPhysician: invoices.attendingPhysician || { name: '', npi: '', qualifier: '' },
          operatingPhysician: invoices.operatingPhysician || { name: '', npi: '', qualifier: '' },
          otherPhysician: invoices.otherPhysician || { name: '', npi: '', qualifier: '' },
          serviceLines: invoices.serviceLines || [],
          payerInformation: invoices.payerInformation || [],
          npiProvider: invoices.npiProvider || '',
          npiOther: invoices.npiOther || '',
          estimatedAmountDue: invoices.estimatedAmountDue || 0,
          totalCharges: invoices.totalCharges || 0,
          nonCoveredCharges: invoices.nonCoveredCharges || 0,
          priorPayments: invoices.priorPayments || 0,
          amountDue: invoices.amountDue || 0,
          diagnosisCodes: invoices.diagnosisCodes || [],
          remarks: invoices.remarks || '',
        }
      : {}),
  };
  invoices && selectedInvoice === InvoiceType.TypeUB04
    ? {
        // Mapear datos existentes de la factura a la estructura UB04
        providerName: invoices.providerName || '',
        providerNumber: invoices.providerNumber || '',
        patientName: invoices.patient?.fullName || '',
        patientAddress: invoices.patient?.address?.street || '',
        patientBirthDate: invoices.patient?.birthDate || '',
        patientSex: invoices.patient?.gender || '',
        admissionDate: invoices.admissionDate || '',
        admissionHour: invoices.admissionHour || '',
        patientId: invoices.patientId || '',
        federalTaxNumber: invoices.federalTaxNumber || '',
        admissionSource: invoices.admissionSource || '',
        admissionType: invoices.admissionType || '',
        dischargeHour: invoices.dischargeHour || '',
        pointOfOrigin: invoices.pointOfOrigin || '',
        dischargeStatus: invoices.dischargeStatus || '',
        conditionCodes: invoices.conditionCodes || [],
        occurrenceCodes: invoices.occurrenceCodes || [],
        valueAmountCodes: invoices.valueAmountCodes || [],
        procedureCodes: invoices.procedureCodes || [],
        attendingPhysician: invoices.attendingPhysician || { name: '', npi: '', qualifier: '' },
        operatingPhysician: invoices.operatingPhysician || { name: '', npi: '', qualifier: '' },
        otherPhysician: invoices.otherPhysician || { name: '', npi: '', qualifier: '' },
        serviceLines: invoices.serviceLines || [],
        payerInformation: invoices.payerInformation || [],
        npiProvider: invoices.npiProvider || '',
        npiOther: invoices.npiOther || '',
        estimatedAmountDue: invoices.estimatedAmountDue || 0,
        totalCharges: invoices.totalCharges || 0,
        nonCoveredCharges: invoices.nonCoveredCharges || 0,
        priorPayments: invoices.priorPayments || 0,
        amountDue: invoices.amountDue || 0,
        diagnosisCodes: invoices.diagnosisCodes || [],
      }
    : initialValuesUB04;

  const initialFormValues: IFormInvoiceTypeAPayload = invoices
    ? {
        insuranceCarrier: invoices.insuranceCarrier || '',
        insuranceIdNumber: invoices.insuranceIdNumber || '',
        insured: invoices.insured
          ? {
              ...invoices.insured,
              anotherHealthBenefitPlan: toYesOrNot(invoices.insured.anotherHealthBenefitPlan),
            }
          : {
              anotherHealthBenefitPlan: No,
              planOrProgramName: INSURED_NAME,
            },
        patient: invoices.patient
          ? {
              ...invoices.patient,
              phoneNumber: invoices.patient.phoneNumber || '',
            }
          : {},
        patientRelationshipToInsured: invoices.patientRelationshipToInsured || '',
        otherInsuredName: invoices.otherInsuredName || '',
        otherInsuredPolicyOrGroupNumber: invoices.otherInsuredPolicyOrGroupNumber || '',
        otherInsuredPlanNameOrProgramName: invoices.otherInsuredPlanNameOrProgramName || '',
        conditionPatientRelatedToEmployment: toYesOrNot(
          invoices.conditionPatientRelatedToEmployment,
        ),
        conditionPatientRelatedToAutoAccident: toYesOrNot(
          invoices.conditionPatientRelatedToAutoAccident,
        ),
        conditionPatientRelatedToOtherAccident: toYesOrNot(
          invoices.conditionPatientRelatedToOtherAccident,
        ),
        conditionPatientRelatedToAutoAccidentPlace:
          invoices.conditionPatientRelatedToAutoAccidentPlace || '',
        patientOrAuthorizedSignature: invoices.patientOrAuthorizedSignature || '',
        physicianSignature: invoices.physicianSignature || undefined,
        insuredOrAuthorizedSignature: invoices.insuredOrAuthorizedSignature || '',
        dateOfCurrentIllnessInjuryPregnancy: invoices.dateOfCurrentIllnessInjuryPregnancy || '',
        qualifierOfCurrentIllnessInjuryAccident:
          invoices.qualifierOfCurrentIllnessInjuryAccident || '',
        otherDateConditionOfIllnessOrTreatment:
          invoices.otherDateConditionOfIllnessOrTreatment || '',
        qualifierOfOtherConditionOfIllnessOrTreatment:
          invoices.qualifierOfOtherConditionOfIllnessOrTreatment || '',
        unableToWorkFromDate: invoices.unableToWorkFromDate || undefined,
        unableToWorkToDate: invoices.unableToWorkToDate || undefined,
        referringProviderName: invoices.referringProviderName || '',
        referringProviderQualifier: invoices.referringProviderQualifier || '',
        referringProviderOtherId: invoices.referringProviderOtherId || '',
        referringProviderOtherIdQualifier: invoices.referringProviderOtherIdQualifier || '',
        referringProviderNpi: invoices.referringProviderNpi || '',
        hospitalizationFromDate: invoices.hospitalizationFromDate || undefined,
        hospitalizationToDate: invoices.hospitalizationToDate || undefined,
        physicianSignatureDate: invoices.physicianSignatureDate || undefined,
        patientOrAuthorizedSignatureDate: invoices.patientOrAuthorizedSignatureDate || undefined,
        isOutsideLab: toYesOrNot(invoices.isOutsideLab),
        outsideLabCharges: invoices.outsideLabCharges || undefined,
        resubmissionCode: invoices.resubmissionCode || undefined,
        icd10DiagnosisCodesForDiseasesOrInjuries:
          invoices.icd10DiagnosisCodesForDiseasesOrInjuries || [],
        originalReferenceNumber: invoices.originalReferenceNumber || '',
        priorAuthorizationNumber: invoices.priorAuthorizationNumber || '',
        supplementalInformation: invoices.supplementalInformation || [],
        serviceFacilityLocation: invoices.serviceFacilityLocation || '',
        billingProvider: invoices.billingProvider || '',
        totalCharge: invoices.totalCharge || undefined,
        amountPaid: invoices.amountPaid || undefined,
        federalTaxIdNumber: invoices.federalTaxIdNumber || '',
        federalTaxIdNumberType: invoices.federalTaxIdNumberType || undefined,
        patientAccountNumber: invoices.patientAccountNumber || '',
        acceptAssignment: toYesOrNot(invoices.acceptAssignment),
        invoiceAdjustments: invoices.invoiceAdjustments || undefined,
      }
    : initialValues;

  useEffect(() => {
    // Get the current ref based on the selected invoice type
    const currentRef =
      selectedInvoice === InvoiceType.TypeUB04 ? formikRefUB04.current : formikRefTypeA.current;

    if (currentRef && invoiceId) {
      const missingFields = Object.entries(currentRef.values)
        .filter(([key]) => isRequiredForSubmission(key))
        .filter(([_, value]) => !value)
        .map(([key]) => key);

      const isComplete = missingFields.length === 0;
      setIsFormComplete(isComplete);
    }
  }, [formikRefTypeA.current?.values, formikRefUB04.current?.values, invoiceId, selectedInvoice]);

  // Función para renderizar el formulario correcto
  const renderForm = () => {
    if (selectedInvoice === InvoiceType.TypeUB04) {
      return (
        <FormInvoiceUB04
          onSubmit={handleUpdateInvoice}
          isLoading={isUpdating}
          innerRef={formikRefUB04}
          initialValues={initialFormValuesUB04}
          invoiceId={invoiceId || ''}
        />
      );
    }

    // Por defecto, renderizar FormInvoiceTypeA
    return (
      <FormInvoiceTypeA
        onSubmit={handleUpdateInvoice}
        isLoading={isUpdating}
        innerRef={formikRefTypeA}
        initialValues={initialFormValues}
        invoiceId={invoiceId || ''}
      />
    );
  };

  return (
    <main>
      <div className="sticky top-0 bg-white z-10 p-4 border-b-2">
        <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <PatientsSearch
            onSelectPatient={recordId => setSelectedPatient(recordId)}
            initialValue={invoices?.patientRecordNumber || ''}
          />

          <InvoicesSearch
            onSelectInvoice={invoiceType => setSelectedInvoice(invoiceType)}
            initialValue={invoices?.type || ''}
          />

          {!invoiceId ? (
            <Button
              primary
              className="mt-7 w-full md:max-w-56 !bg-primary-500"
              size="large"
              onPress={handleStartBilling}
            >
              Iniciar Facturación
            </Button>
          ) : (
            <div className="flex items-center justify-center">
              <Button
                type="button"
                buttonType="secondary"
                className="mt-7 w-full md:max-w-56"
                size="large"
                onPress={handleExternalSubmit}
              >
                Guardar Borrador
              </Button>
              {isFormComplete && (
                <div className="w-full flex items-center justify-center flex-col">
                  <ModalConfirmation
                    invoiceId={invoiceId || ''}
                    invoiceData={
                      (selectedInvoice === InvoiceType.TypeUB04
                        ? formikRefUB04.current?.values
                        : formikRefTypeA.current?.values) ||
                      (selectedInvoice === InvoiceType.TypeUB04
                        ? initialFormValuesUB04
                        : initialFormValues)
                    }
                  />
                </div>
              )}
            </div>
          )}
        </section>
      </div>

      {isCreating || isLoading ? (
        <LoadingFallback
          description={isCreating ? 'Generando factura...' : 'Cargando factura...'}
        />
      ) : (
        <section className="mt-6 p-4">
          <h2 className="text-xl font-bold">
            {invoiceId ? 'Detalles de la Factura' : 'Facturas Recientes'}
          </h2>
          {(isSuccess || invoiceId) && <div className="mt-4">{renderForm()}</div>}
        </section>
      )}
    </main>
  );
}
