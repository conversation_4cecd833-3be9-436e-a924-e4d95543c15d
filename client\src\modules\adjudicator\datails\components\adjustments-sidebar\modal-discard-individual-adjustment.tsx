import { Button } from '@digheontech/digh.ui';
import { useRemoveServiceAdjustment } from '../../hooks/use-remove-service-adjustment';
import { Dialog, DialogTrigger, Modal, ModalOverlay } from 'react-aria-components';
import { getDenialReasonLabel } from '@/tools/getDenialReasonLabel';
import { DenialReason } from '@/enums';
import { RiDeleteBin2Line } from 'react-icons/ri';

interface IModalDiscardIndividualAdjustment {
  invoiceId: string;
  serviceId: string;
  fieldLabel: string;
  fieldValue: any;
  disabled?: boolean;
}

const renderFieldValue = (value: any): string => {
  if (value === null || value === undefined) return 'N/A';
  if (typeof value === 'boolean') return value ? 'Sí' : 'No';

  if (Array.isArray(value)) {
    if (value.length === 0) return 'N/A';

    if (typeof value[0] === 'object' && value[0] !== null) {
      return value
        .map((item, index) => {
          const summary = [];

          if (item._id) summary.push(`ID: ${item._id}`);
          if (item.charges) summary.push(`Charges: ${item.charges}`);
          if (item.fromDateOfService) {
            const date = new Date(item.fromDateOfService);
            if (!isNaN(date.getTime())) {
              summary.push(`From: ${date.toISOString().split('T')[0]}`);
            }
          }
          if (item.toDateOfService) {
            const date = new Date(item.toDateOfService);
            if (!isNaN(date.getTime())) {
              summary.push(`To: ${date.toISOString().split('T')[0]}`);
            }
          }
          if (item.placeOfService) summary.push(`Place: ${item.placeOfService}`);
          if (item.denialReason) {
            const translatedReason = getDenialReasonLabel(item.denialReason as DenialReason);
            summary.push(`Denial: ${translatedReason}`);
          }
          if (item.epsdtFamilyPlan) summary.push(`EPSDT: ${item.epsdtFamilyPlan}`);

          return `Item ${index + 1}: ${summary.join(', ')}`;
        })
        .join('\n\n');
    }

    return value.join(', ');
  }

  if (typeof value === 'object') {
    const entries = Object.entries(value)
      .filter(([key, val]) => val !== null && val !== undefined && val !== '')
      .map(([key, val]) => {
        let displayValue = val;

        if (key === 'denialReason' && typeof val === 'string') {
          displayValue = getDenialReasonLabel(val as DenialReason);
        }
        return `${key}: ${displayValue}`;
      })
      .slice(0, 5);

    return entries.length > 0 ? entries.join('\n') : 'Objeto vacío';
  }

  return String(value);
};

export const ModalDiscardIndividualAdjustment = ({
  invoiceId,
  serviceId,
  fieldLabel,
  fieldValue,
  disabled = false,
}: IModalDiscardIndividualAdjustment) => {
  const { removeServiceAdjustment, isRemoving } = useRemoveServiceAdjustment();

  const handleConfirm = (close: () => void) => {
    removeServiceAdjustment({ invoiceId, serviceId }, { onSuccess: () => close() });
  };

  return (
    <DialogTrigger>
      <Button
        buttonType="dangerOutlined"
        disabled={disabled || isRemoving}
        className="disabled:text-gray-400 disabled:cursor-not-allowed transition-colors duration-200 text-xl font-bold mt-1"
      >
        <RiDeleteBin2Line />
      </Button>
      <ModalOverlay
        className={({ isEntering, isExiting }) =>
          `fixed inset-0 z-10 overflow-y-auto bg-black/25 flex min-h-full items-center justify-center p-4 text-center backdrop-blur ${
            isEntering ? 'animate-in fade-in duration-300 ease-out' : ''
          } ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}`
        }
      >
        <Modal
          className={({ isEntering, isExiting }) =>
            `w-full max-w-md overflow-hidden rounded-2xl bg-white pt-7 pb-6 px-6 text-left align-middle shadow-xl relative ${
              isEntering ? 'animate-in zoom-in-95 ease-out duration-300' : ''
            } ${isExiting ? 'animate-out zoom-out-95 ease-in duration-200' : ''}`
          }
        >
          <Dialog role="dialog" className="outline-none">
            {({ close }) => (
              <>
                <div className="text-center">
                  <div className="text-6xl mb-4">🗑️</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Confirmar descarte de ajuste
                  </h3>
                  <p className="text-sm text-gray-600 mb-4">
                    ¿Estás seguro de que deseas descartar el siguiente ajuste?
                  </p>

                  <div className="bg-yellow-50 border border-yellow-200 rounded p-4 mb-4 text-left">
                    <div className="mb-2">
                      <span className="font-semibold text-sm text-gray-800">Campo:</span>
                      <p className="text-sm text-gray-700 mt-1">{fieldLabel}</p>
                    </div>
                    <div>
                      <span className="font-semibold text-sm text-gray-800">Valor actual:</span>
                      <div className="text-sm text-gray-700 mt-1 bg-white p-3 rounded border border-gray-200 max-h-40 overflow-y-auto">
                        <pre className="whitespace-pre-wrap font-sans text-xs leading-relaxed">
                          {renderFieldValue(fieldValue)}
                        </pre>
                      </div>
                    </div>
                  </div>

                  <p className="text-xs text-gray-500 mb-6">Esta acción no se puede deshacer.</p>
                </div>

                <div className="flex justify-center gap-3">
                  <Button
                    disabled={isRemoving}
                    type="button"
                    className="px-4 py-2"
                    buttonType="normal"
                    onPress={() => close()}
                  >
                    Cancelar
                  </Button>
                  <Button
                    disabled={isRemoving}
                    type="button"
                    className="px-4 py-2"
                    buttonType="dangerOutlined"
                    onPress={() => handleConfirm(close)}
                  >
                    {isRemoving ? 'Descartando...' : 'Sí, Descartar'}
                  </Button>
                </div>
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
};
