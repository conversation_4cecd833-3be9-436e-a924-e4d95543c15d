import { SetStateAction, useState, useEffect } from 'react';
import { SearchField, Label } from 'react-aria-components';
import { Input } from '@digheontech/digh.ui';
import { useGetPatients } from '../../../hooks/use-get-patients';

interface IPatient {
  recordId: string;
  name: string;
}

interface IPatientsSearch {
  onSelectPatient: (recordId: string) => void;
  initialValue: string;
}

export function PatientsSearch({ onSelectPatient, initialValue }: IPatientsSearch) {
  const [inputValue, setInputValue] = useState(initialValue || '');
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  const { patients, isLoading } = useGetPatients(searchTerm);

  useEffect(() => {
    setInputValue(initialValue || '');
    setSearchTerm(initialValue || '');
  }, [initialValue]);

  useEffect(() => {
    if (initialValue) {
      onSelectPatient(initialValue);
    }
  }, [initialValue, onSelectPatient]);

  const handleSearch = (value: SetStateAction<string>) => {
    setSearchTerm(value);
    setShowResults(!!value);
  };

  const handleSelectPatient = (patient: IPatient) => {
    setSearchTerm(patient.name);
    setInputValue(patient.name);
    setShowResults(false);
    onSelectPatient(patient.recordId);
  };

  return (
    <div className="w-full max-w-md relative">
      <Label className="font-bold">Buscar paciente</Label>
      <SearchField
        value={inputValue}
        onChange={handleSearch}
        className="relative flex items-center w-full mt-2"
        aria-label="Paciente"
      >
        <Input
          placeholder="Nombre o número de expediente"
          className="w-full outline-none text-gray-700 placeholder-gray-400 p-4 cursor-pointer"
          aria-label="Paciente"
        />
      </SearchField>

      {showResults && patients?.length > 0 && (
        <ul className="absolute z-10 min-w-max max-h-60 overflow-auto mt-1 bg-white border border-gray-200 rounded-lg divide-y divide-gray-200 shadow-lg">
          {patients.map((patient: IPatient) => (
            <li
              key={patient.recordId}
              className="px-4 py-2 text-gray-700 cursor-pointer hover:bg-gray-100"
              onClick={() => handleSelectPatient(patient)}
            >
              <div className="flex justify-start gap-4">
                <small>
                  <strong>Nombre:</strong> {patient.name}
                </small>
                {' - '}
                <small>
                  <strong>ID:</strong> {patient.recordId}
                </small>
              </div>
            </li>
          ))}
        </ul>
      )}

      {isLoading && <p className="mt-2 text-gray-500">Cargando...</p>}

      {searchTerm && !isLoading && patients.length === 0 && (
        <p className="mt-2 text-gray-500">No se encontraron pacientes.</p>
      )}
    </div>
  );
}
