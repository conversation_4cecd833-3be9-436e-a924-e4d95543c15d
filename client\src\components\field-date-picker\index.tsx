'use client';

import { Field } from 'formik';
import DatePicker from 'react-datepicker';
import { Label } from 'react-aria-components';
import { RxCalendar } from 'react-icons/rx';

interface IFieldDatePicker {
  disabled: boolean;
  label?: string;
  name: string;
  className?: HTMLElement['className'];
  value?: string | Date;
  onChange?: (date: Date) => void;
  labelClassName?: HTMLElement['className'];
}

export function FieldDatePicker({
  className,
  label,
  name,
  disabled,
  value,
  onChange,
  labelClassName,
}: IFieldDatePicker) {
  const Component = ({ field, form }: { field: any; form: any }) => {
    const dateValue = value !== undefined ? value : field.value;

    return (
      <DatePicker
        name={field.name}
        selected={dateValue ? new Date(dateValue) : null}
        disabled={disabled}
        onChange={date => {
          form.setFieldValue(field.name, date);
          if (onChange && date) onChange(date);
        }}
        onBlur={field.onBlur}
        dateFormat="MM/dd/yyyy"
        className={`m-auto text-center w-full bg-transparent h-4 p-4 ${disabled ? 'cursor-not-allowed hover:bg-gray-100' : 'cursor-pointer '}`}
        showYearDropdown
        aria-label={label}
      />
    );
  };

  return (
    <div>
      {label && (
        <div>
          <Label className={`block text-sm mb-1 font-medium ${labelClassName}`}>{label}</Label>
        </div>
      )}

      <div
        className={`field-shadow text-sm !h-11 gap-0 ${disabled ? 'cursor-not-allowed hover:bg-gray-100' : 'cursor-pointer '} ${className}`}
      >
        <Field name={name} disabled={disabled} component={Component} />
        <RxCalendar className="text-gray-500 flex-shrink-0 p-0" />
      </div>
    </div>
  );
}
