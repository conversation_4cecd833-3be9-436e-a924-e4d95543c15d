import { Is<PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON>ption<PERSON>, IsString } from 'class-validator';
import { UserRole } from 'src/shared/enums';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;

  @IsString()
  @IsOptional()
  role?: UserRole;
}
