<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="200" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

<p align="center">PR Ready API</p>

## Description

In development...

## Installation

```bash
$ yarn
```

## Running the app

```bash
# development
$ yarn start

# debug mode
$ yarn start:debug

# production mode
$ yarn start:prod
```

## Testing

<p>Mail templates may be previewed with live changes in a browser. A local mail server is also available for sending and receiving of emails.</p>

```bash
# Mail template live preview
$ yarn mail:dev

# Local mail server
$ yarn mail:up
```
