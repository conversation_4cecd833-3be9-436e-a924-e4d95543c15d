import { FormField, FieldDatePicker } from '@/components';
import { AdmissionSourceSelect, AdmissionTypeSelect, DischargeStatusSelect } from '../selects';

export function UB04Items09_17({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 p-4">
      <h3 className="text-lg font-bold mb-4">Patient ID and Admission Information (Fields 9-17)</h3>

      <div className="grid grid-cols-3 gap-4 mb-4">
        {/* Campo 9: Patient ID */}
        <FormField name="patientId" label="9. Patient ID" disabled={isLoading} />

        {/* Campo 10: Federal Tax Number */}
        <FormField name="federalTaxNumber" label="10. Federal Tax Number" disabled={isLoading} />

        {/* Campo 11: Point of Origin */}
        <FormField name="pointOfOrigin" label="11. Point of Origin" disabled={isLoading} />
      </div>

      <div className="grid grid-cols-3 gap-4 mb-4">
        {/* Campo 12: Admission Source */}
        <AdmissionSourceSelect
          name="admissionSource"
          label="12. Admission Source"
          disabled={isLoading}
        />

        {/* Campo 13: Admission Type */}
        <AdmissionTypeSelect name="admissionType" label="13. Admission Type" disabled={isLoading} />

        {/* Campo 14: Discharge Status */}
        <DischargeStatusSelect
          name="dischargeStatus"
          label="14. Discharge Status"
          disabled={isLoading}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        {/* Campo 15: Discharge Hour */}
        <FormField
          name="dischargeHour"
          label="15. Discharge Hour"
          type="time"
          disabled={isLoading}
        />

        {/* Campo 16-17: Reserved for future use */}
        <div className="text-gray-500 italic">Fields 16-17: Reserved for future use</div>
      </div>
    </section>
  );
}
