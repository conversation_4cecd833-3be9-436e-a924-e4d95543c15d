import { useField } from 'formik';
import { <PERSON><PERSON>, Key, ListBox, Popover, Select, SelectValue } from 'react-aria-components';
import { LuChevronsUpDown } from 'react-icons/lu';

import { Item17Role } from '@/enums';
import { StatusItem } from '@/modules/providers/create/components/provider-select-type/status-item';
import { getItem17RoleLabel } from '@/tools/getItem17RoleLabel';

const ROLES = [
  Item17Role.OrderingProvider,
  Item17Role.ReferringProvider,
  Item17Role.SupervisingProvider,
] as const;

interface IRoleOption {
  role: Item17Role;
}

interface IRoleSelectItem17 {
  className?: HTMLElement['className'];
}

const RoleOption = ({ role }: IRoleOption) => (
  <StatusItem key={role} id={role}>
    <div className="flex gap-2 items-baseline">
      <span>{role}</span> <small>{getItem17RoleLabel(role)}</small>
    </div>
  </StatusItem>
);

export function RoleSelectItem17({ className }: IRoleSelectItem17) {
  const [field, meta, helpers] = useField<Item17Role>('referringProviderQualifier');

  const handleSelectionChange = (key: Key) => {
    helpers.setValue(key as Item17Role);
  };

  return (
    <div className={className}>
      <Select
        aria-label="Role"
        className="flex flex-col gap-1 w-full mb-4"
        selectedKey={field.value}
        onSelectionChange={handleSelectionChange}
        defaultSelectedKey={undefined}
        placeholder="select"
      >
        <Button className="field-shadow h-8" aria-label="Role">
          <SelectValue className="flex-1 truncate placeholder-shown:italic">
            {role => getItem17RoleLabel(role as unknown as Item17Role)}
          </SelectValue>
          <LuChevronsUpDown />
        </Button>

        <Popover className="max-h-60 w-[--trigger-width] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out">
          <ListBox className="outline-none p-1">
            {ROLES.map(role => (
              <RoleOption key={role} role={role} />
            ))}
          </ListBox>
        </Popover>
      </Select>

      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
