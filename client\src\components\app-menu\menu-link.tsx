'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';

interface IMenuLink {
  href: string;
  icon?: React.ReactNode;
  label: string;
  collapsed?: boolean;
}

export function MenuLink({ href, icon, label, collapsed = false }: IMenuLink) {
  const pathname = usePathname();

  const isActive = pathname === href;

  return (
    <Link
      href={href}
      className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
        isActive ? 'text-primary-500 border font-bold' : 'hover:text-primary-500'
      }`}
    >
      {icon && (
        <span
          className={
            isActive
              ? 'bg-[#379daa] rounded-full h-8 w-8 p-2 text-slate-100'
              : 'bg-white rounded-full h-8 w-8 p-2 text-[#379daa]'
          }
        >
          {icon}
        </span>
      )}
      {!collapsed && <span>{label}</span>}
    </Link>
  );
}
