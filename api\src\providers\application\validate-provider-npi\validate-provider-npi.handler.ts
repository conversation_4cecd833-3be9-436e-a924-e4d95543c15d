import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { UnauthorizedException } from '@nestjs/common';

import { ProviderRepository } from 'src/providers/infrastructure/repositories/provider.repository';
import { AuthService } from 'src/identity/infrastructure/auth.service';
import { User } from 'src/identity/domain/user.model';

import { ValidateProviderNpiCommand } from './validate-provider-npi.command';
import { UserRepository } from 'src/identity/infrastructure/repositories/user.repository';

@CommandHandler(ValidateProviderNpiCommand)
export class ValidateProviderNpiHandler
  implements ICommandHandler<ValidateProviderNpiCommand>
{
  constructor(
    readonly providerRepository: ProviderRepository,
    readonly userRepository: UserRepository,
    readonly authService: AuthService,
  ) {}
  async execute(command: ValidateProviderNpiCommand) {
    const provider = await this.providerRepository.findById(command.providerId);

    if (!provider) {
      throw new UnauthorizedException();
    }

    if (provider.npi !== command.npi) {
      throw new UnauthorizedException();
    }

    const user = await this.userRepository.findById(command.userId);

    const token = await this.authService.generateToken({
      userId: command.userId,
    });

    return { token, user: User.fromModel(user).getUserInfo() };
  }
}
