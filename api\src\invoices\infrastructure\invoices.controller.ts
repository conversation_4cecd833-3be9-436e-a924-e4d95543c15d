import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Req,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { FileInterceptor } from '@nestjs/platform-express';

import { Roles } from 'src/identity/infrastructure/roles.decorator';
import { UserRole } from 'src/shared/enums';
import { FileUploadValidationPipe } from 'src/file-management/infrastructure/file-upload-validation-pipe';

import { GetInvoiceFilesQuery } from '../application/get-invoice-files/get-invoice-files.query';
import { GetInvoiceDetailQuery } from '../application/get-invoice-detail/get-invoice-detail.query';
import { UpdateInvoiceCommand } from '../application/update-invoice/update-invoice.command';
import { GetInvoicesQuery } from '../application/get-invoices/get-invoices.query';
import { UploadFileCommand } from '../application/upload-file/upload-file.command';
import { CreateInvoiceCommand } from '../application/create-invoice/create-invoice.command';
import { GetFileSignedUrlQuery } from '../application/get-file-signed-url/get-file-signed-url.query';
import { DeleteFileCommand } from '../application/delete-file/delete-file.command';
import { DeleteInvoiceCommand } from '../application/delete-invoice/delete-invoice.command';
import { ConfirmInvoiceCommand } from '../application/confirm-invoice/confirm-invoice.command';
import { UpdateFileCommand } from '../application/update-file/update-file.command';
import { GetCompleteInvoicesQuery } from '../application/get-complete-invoices/get-complete-invoices.query';
import { GetInvoiceAdjustmentsDetailQuery } from '../application/get-invoice-adjustments-detail/get-invoice-adjustments-detail.query';
import { DeleteInvoiceAdjustmentCommand } from '../application/delete-invoice-adjustment/delete-invoice-adjustment.command';
import { UpdateInvoiceAdjustmentCommand } from '../application/update-invoice-adjustment/update-invoice-adjustment.command';
import { RemoveServiceAdjustmentCommand } from '../application/remove-service-adjustment/remove-service-adjustment.command';
import { RemoveItemAdjustmentCommand } from '../application/remove-item-adjustment/remove-item-adjustment.command';

import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { UpdateInvoiceDto } from './dto/update-invoice.dto';
import { ConfirmInvoiceDto } from './dto/confirm-invoice.dto';
import { UploadFileToInvoiceDto } from './dto/upload-file-to-invoice.dto';
import { UpdateInvoiceAdjustmentDto } from './dto/update-invoice-adjustment.dto';

@Controller('invoices')
export class InvoicesController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBuys: QueryBus,
  ) {}

  @Post('/')
  @Roles(UserRole.Provider)
  async createInvoice(@Body() body: CreateInvoiceDto, @Req() req: any) {
    return this.commandBus.execute(
      new CreateInvoiceCommand({ ...body, userId: req.user.id }),
    );
  }

  @Get('/')
  @Roles(UserRole.Provider)
  async getInvoices() {
    return this.queryBuys.execute(new GetInvoicesQuery());
  }

  @Get('/completed')
  @Roles(UserRole.Adjudicator)
  async getCompletedInvoices() {
    return this.queryBuys.execute(new GetCompleteInvoicesQuery());
  }

  @Get('/:invoiceId')
  @Roles(UserRole.Adjudicator, UserRole.Provider)
  async getInvoice(@Param('invoiceId') invoiceId: string) {
    return this.queryBuys.execute(new GetInvoiceDetailQuery(invoiceId));
  }

  @Get('/:invoiceId/adjustments')
  @Roles(UserRole.Adjudicator, UserRole.Provider)
  async getInvoiceAdjustments(@Param('invoiceId') invoiceId: string) {
    return this.queryBuys.execute(
      new GetInvoiceAdjustmentsDetailQuery(invoiceId),
    );
  }

  @Patch('/:invoiceId')
  @Roles(UserRole.Provider)
  async updateInvoice(
    @Param('invoiceId') invoiceId: string,
    @Body() body: UpdateInvoiceDto,
  ) {
    return this.commandBus.execute(new UpdateInvoiceCommand(invoiceId, body));
  }

  @Post('/:invoiceId/files')
  @UseInterceptors(FileInterceptor('file'))
  async addFileInvoice(
    @Param('invoiceId') invoiceId: string,
    @UploadedFile(
      FileUploadValidationPipe.customFileType({
        allowedTypes: /jpeg|jpg|png|pdf/,
      }),
    )
    file: Express.MulterS3.File,
    @Body() body: UploadFileToInvoiceDto,
  ) {
    const { documentType, fileName } = body;
    return this.commandBus.execute(
      new UploadFileCommand(invoiceId, file, documentType, fileName),
    );
  }

  @Patch('/:invoiceId/files/:fileId')
  @Roles(UserRole.Provider)
  async updateFile(
    @Param('invoiceId') invoiceId: string,
    @Param('fileId') fileId: string,
    @Body() body: UploadFileToInvoiceDto,
  ) {
    const { documentType, fileName } = body;
    return this.commandBus.execute(
      new UpdateFileCommand(invoiceId, fileId, documentType, fileName),
    );
  }

  @Get('/:invoiceId/files')
  @Roles(UserRole.Provider, UserRole.Adjudicator)
  async getInvoiceFiles(@Param('invoiceId') invoiceId: string) {
    return this.queryBuys.execute(new GetInvoiceFilesQuery(invoiceId));
  }

  @Delete('/:invoiceId/files/:fileId')
  @Roles(UserRole.Provider)
  async deleteFile(
    @Param('invoiceId') invoiceId: string,
    @Param('fileId') fileId: string,
  ) {
    return this.commandBus.execute(new DeleteFileCommand(invoiceId, fileId));
  }

  @Get('/:invoiceId/files/:fileId')
  @Roles(UserRole.Provider, UserRole.Adjudicator)
  async getFileSignedUrl(
    @Param('invoiceId') invoiceId: string,
    @Param('fileId') fileId: string,
  ) {
    return this.queryBuys.execute(new GetFileSignedUrlQuery(invoiceId, fileId));
  }

  @Delete('/:invoiceId')
  @Roles(UserRole.Provider)
  async deleteInvoice(@Param('invoiceId') invoiceId: string) {
    return this.commandBus.execute(new DeleteInvoiceCommand(invoiceId));
  }

  @Patch('/:invoiceId/confirm')
  @Roles(UserRole.Provider)
  async confirmInvoice(
    @Param('invoiceId') invoiceId: string,
    @Body() body: ConfirmInvoiceDto,
  ) {
    return this.commandBus.execute(new ConfirmInvoiceCommand(invoiceId, body));
  }

  @Patch('/:originalInvoiceId/adjustment')
  @Roles(UserRole.Adjudicator)
  async updateInvoiceAdjustments(
    @Param('originalInvoiceId') originalInvoiceId: string,
    @Body() body: UpdateInvoiceAdjustmentDto,
  ) {
    return this.commandBus.execute(
      new UpdateInvoiceAdjustmentCommand(originalInvoiceId, body),
    );
  }

  @Delete('/:originalInvoiceId/remove-adjustment')
  @Roles(UserRole.Adjudicator)
  async deleteInvoiceAdjustments(
    @Param('originalInvoiceId') originalInvoiceId: string,
  ) {
    return this.commandBus.execute(
      new DeleteInvoiceAdjustmentCommand(originalInvoiceId),
    );
  }

  @Delete('/:originalInvoiceId/service-adjustment/:serviceAdjustmentId')
  @Roles(UserRole.Adjudicator)
  async deleteServiceAdjustment(
    @Param('originalInvoiceId') originalInvoiceId: string,
    @Param('serviceAdjustmentId') serviceAdjustmentId: string,
  ) {
    return this.commandBus.execute(
      new RemoveServiceAdjustmentCommand(
        originalInvoiceId,
        serviceAdjustmentId,
      ),
    );
  }

  @Delete('/:originalInvoiceId/item-adjustment/:itemAdjustmentId')
  @Roles(UserRole.Adjudicator)
  async deleteItemAdjustment(
    @Param('originalInvoiceId') originalInvoiceId: string,
    @Param('itemAdjustmentId') itemAdjustmentId: string,
  ) {
    return this.commandBus.execute(
      new RemoveItemAdjustmentCommand(originalInvoiceId, itemAdjustmentId),
    );
  }
}
