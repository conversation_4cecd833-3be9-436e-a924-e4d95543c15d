import * as Yup from 'yup';

import { IFormValidateProviders } from '@/interfaces';

export const initialValues: IFormValidateProviders = {
  providerId: '',
  npi: '',
  userId: '',
};

export const validationSchema = Yup.object().shape({
  userId: Yup.string().trim().required(),
  providerId: Yup.string().trim().required(),
  npi: Yup.string()
    .trim()
    .required('El NPI es requerido')
    .matches(/^\d{10}$/, 'El NPI debe ser exactamente 10 dígitos sin espacios ni símbolos'),
});
