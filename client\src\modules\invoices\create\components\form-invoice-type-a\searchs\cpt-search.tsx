import { useField } from 'formik';
import { useState, useEffect } from 'react';
import { SearchField } from 'react-aria-components';
import { Input } from '@digheontech/digh.ui';

import { ICPT } from '@/interfaces';
import { useGetCPT } from '../../../hooks/use-get-cpt-codes';

interface ICPTSearch {
  onSelectCode?: (code: string) => void;
  initialValue?: string;
  language?: 'es' | 'en';
  disabled?: boolean;
  name: string;
  className?: string;
}

export function CPTSearch({
  onSelectCode,
  initialValue = '',
  language = 'es',
  disabled,
  name,
  className,
}: ICPTSearch) {
  const [field, meta, helpers] = useField(name);

  const [inputValue, setInputValue] = useState(initialValue || field.value || '');
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  const { CPT = [], isLoading } = useGetCPT(searchTerm, language);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [selectedCPT, setSelectedCPT] = useState<ICPT | null>(null);

  useEffect(() => {
    const valueToUse = field.value || initialValue;

    if (valueToUse) {
      setInputValue(valueToUse);
      setSearchTerm(valueToUse);
    } else {
      setInputValue('');
      setSearchTerm('');
    }
  }, [initialValue, field.value]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    setShowResults(!!newValue);

    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (newValue) {
      const timeout = setTimeout(() => {
        setSearchTerm(newValue);
      }, 500);
      setSearchTimeout(timeout);
    } else {
      setSearchTerm('');
      setSelectedCPT(null);
      helpers?.setValue('');
    }
  };

  const handleSelectCPT = (codes: ICPT) => {
    setSelectedCPT(codes);
    setInputValue(codes.code);
    setShowResults(false);
    setSearchTerm('');

    helpers?.setValue(codes.code);
    helpers?.setTouched(true);

    if (onSelectCode) {
      onSelectCode(codes.code);
    }
  };

  const clearSelection = () => {
    setInputValue('');
    setSearchTerm('');
    setSelectedCPT(null);
    setShowResults(false);
    helpers?.setValue('');
    helpers?.setTouched(true);
  };

  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  return (
    <div className={`w-full max-w-44 relative ${className || ''}`}>
      <SearchField
        aria-label="Procedure Code"
        className="relative flex items-center w-full mt-1"
        {...field}
        value={inputValue}
        onChange={setInputValue}
        onClear={clearSelection}
      >
        <Input
          value={inputValue}
          onChange={e => handleSearch(e)}
          onFocus={() => setShowResults(!!inputValue)}
          onBlur={() => {
            setTimeout(() => {
              setShowResults(false);
              if (selectedCPT) {
                setInputValue(selectedCPT.code);
              }
            }, 200);
          }}
          placeholder="Buscar..."
          disabled={disabled}
          className="field-shadow !h-10 !w-full"
        />
      </SearchField>

      {showResults && CPT?.length > 0 && (
        <ul className="absolute z-10 min-w-max max-h-60 overflow-auto mt-1 bg-white border border-gray-200 rounded-lg divide-y divide-gray-200 shadow-lg">
          {CPT.map((item: ICPT) => (
            <li
              key={item.code}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSelectCPT(item)}
            >
              <div className="flex flex-col">
                <small>
                  <strong>Code:</strong> {item.code}
                </small>
                <small>
                  <strong>Name:</strong>{' '}
                  {language === 'es' ? item.descriptionEs : item.descriptionEn}
                </small>
              </div>
            </li>
          ))}
        </ul>
      )}

      {isLoading && <p className="mt-2 text-gray-500 text-sm">Cargando...</p>}

      {searchTerm && !isLoading && CPT?.length === 0 && showResults && (
        <p className="mt-2 text-red-400 text-sm text-pretty w-96">No se encontraron códigos.</p>
      )}

      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
