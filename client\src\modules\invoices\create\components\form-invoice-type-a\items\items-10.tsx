import { Label } from 'react-aria-components';
import { FormField } from '@/components';
import { YesOrNot } from '@/enums';

const { Yes, No } = YesOrNot;

export function Items10({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 border-t-transparent border-r-transparent flex flex-col gap-4 hover-card">
      <div className="flex gap-4 p-4 ">
        <strong>10.</strong>
        <Label className="block text-sm font-medium">IS PATIENT&apos;S CONDITION RELATED TO:</Label>
      </div>
      <div className="flex flex-col gap-4 ml-6">
        <div className="flex gap-4 mb-2">
          <strong>a.</strong>
          <FormField
            label="EMPLOYMENT?"
            name="conditionPatientRelatedToEmployment"
            disabled={isLoading}
            type="radio"
            options={[
              { value: Yes, label: Yes },
              { value: No, label: No },
            ]}
          />
          <small>(Current or Previous)</small>
        </div>
        <div className="flex gap-16">
          <div className="flex gap-4 my-2">
            <strong>b.</strong>
            <FormField
              label="AUTO ACCIDENT?"
              name="conditionPatientRelatedToAutoAccident"
              disabled={isLoading}
              type="radio"
              options={[
                { value: Yes, label: Yes },
                { value: No, label: No },
              ]}
            />
          </div>
          <FormField
            label="Place (State)"
            name="conditionPatientRelatedToAutoAccidentPlace"
            type="text"
            disabled={isLoading}
          />
        </div>
        <div className="flex gap-4 my-2">
          <strong>c.</strong>
          <FormField
            label="OTHER ACCIDENT?"
            name="conditionPatientRelatedToOtherAccident"
            type="radio"
            options={[
              { value: Yes, label: Yes },
              { value: No, label: No },
            ]}
            disabled={isLoading}
          />
        </div>
        <div className="flex gap-4 my-2">
          <strong>d.</strong>
          <FormField
            label="CLAIM CODES (Designated by NUCC)"
            name=" "
            type="text"
            className="border-none bg-transparent"
            disabled
          />
        </div>
      </div>
    </section>
  );
}
