import { FormField } from '@/components';
import { CityZipCodeSearch } from '../searchs/city-zip-code-search';

export function Items07({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="p-4 hover-card">
      <div className="grid grid-cols-2 gap-4">
        <div className="flex gap-4 justify-start">
          <strong className="w-4">7.</strong>
          <div className="w-full">
            <FormField
              disabled={isLoading}
              name="insured.address.street"
              label="INSURED’S ADDRESS (No., Street)"
            />
          </div>
        </div>
        <FormField disabled={isLoading} name="insured.phoneNumber" label="TELEPHONE" />
      </div>
      <div className="mt-4 ml-8">
        <CityZipCodeSearch
          className="gap-4"
          state="insured.address.state"
          city="insured.address.city"
          zipCode="insured.address.zipCode"
          disabled={isLoading}
        />
      </div>
    </section>
  );
}
