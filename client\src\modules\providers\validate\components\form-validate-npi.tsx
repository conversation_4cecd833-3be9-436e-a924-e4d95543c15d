'use client';

import { Form, Formik } from 'formik';
import { Button } from '@digheontech/digh.ui';
import { IFormValidateProviders } from '@/interfaces';
import { FormField } from '@/components';

import { useValidateProvider } from '../hooks/use-validate-provider';
import { validationSchema } from './validations-form';

export function FormValidateNpi({ initialValues }: { initialValues: IFormValidateProviders }) {
  const { isValidating, validate } = useValidateProvider();

  const onSubmit = (payload: IFormValidateProviders) => {
    validate({ ...initialValues, ...payload });
  };

  return (
    <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={onSubmit}>
      {() => (
        <Form className="w-full bg-transparent flex flex-col gap-4">
          <FormField
            className="!h-24 !text-2xl text-center"
            name="npi"
            label="Id único (NPI)"
            placeholder="Ingresar NPI"
            type="text"
            disabled={isValidating}
          />

          <Button
            type="submit"
            primary
            className="mt-4"
            fullWidth
            disabled={isValidating}
            size="large"
          >
            Enviar
          </Button>
        </Form>
      )}
    </Formik>
  );
}
