import { useQuery } from '@tanstack/react-query';
import { Http } from '@/config/http';

const getSignedUrl = async (invoiceId: string, fileId: string) => {
  const {
    data: { fileUrl },
  } = await Http.get<{ fileUrl: string }>(`invoices/${invoiceId}/files/${fileId}`);

  return fileUrl;
};

export const useGetSignedDocument = (invoiceId: string, fileId: string) => {
  return useQuery({
    queryKey: ['signedUrl', invoiceId, fileId],
    queryFn: () => getSignedUrl(invoiceId, fileId),
    enabled: !!invoiceId && !!fileId,
  });
};
