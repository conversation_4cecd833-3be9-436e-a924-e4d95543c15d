'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const LanguageSelector = ({ className }: { className: string }) => {
  const { i18n } = useTranslation();
  const [selectedLang, setSelectedLang] = useState('es');

  useEffect(() => {
    const savedLang = localStorage.getItem('lang') || 'es';
    setSelectedLang(savedLang);
  }, []);

  const changeLanguage = (lang: string) => {
    setSelectedLang(lang);
    i18n.changeLanguage(lang);
    localStorage.setItem('lang', lang);
  };

  return (
    <select
      value={selectedLang}
      onChange={e => changeLanguage(e.target.value)}
      className={className}
    >
      <option value="en">English</option>
      <option value="es">Español</option>
    </select>
  );
};

export default LanguageSelector;
