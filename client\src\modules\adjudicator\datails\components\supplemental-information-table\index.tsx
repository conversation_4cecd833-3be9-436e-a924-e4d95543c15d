import { AuditableSection } from '../auditable-section';
import { Label } from 'react-aria-components';
import { ISupplementalInformation } from '@/interfaces';

interface ISupplementalInformationTable {
  supplementalInfo: ISupplementalInformation[];
  isLoading?: boolean;
  isFieldInAdjustments?: (fieldName: string) => boolean;
}

export const SupplementalInformationTable = ({
  supplementalInfo,
  isLoading,
  isFieldInAdjustments,
}: ISupplementalInformationTable) => {
  return (
    <div className="mt-2">
      <div
        className={`w-full border border-gray-300 p-2 ${
          isFieldInAdjustments && isFieldInAdjustments('supplementalInformation')
            ? 'border-yellow-600 bg-yellow-200'
            : 'bg-gray-100'
        }`}
      >
        <div className="flex justify-between items-baseline">
          <strong className="text-sm text-primary-500 font-bold">
            <small className="text-red-500">*</small> 24. SERVICE LINE
          </strong>
        </div>
      </div>

      <div className="relative border border-slate-200">
        <div
          className="grid grid-cols-11 bg-white text-xs border-b border-sky-200"
          style={{
            gridTemplateColumns: '280px 136px 1fr 1fr 320px repeat(6, 1fr)',
          }}
        >
          <div className="p-4 border border-slate-200 text-center">
            A. DATE(S) OF SERVICE
            <div className="flex justify-around items-center mt-1">
              <span>From</span>
              <span>To</span>
            </div>
          </div>
          <div className="p-4 border border-slate-200 text-center">B. PLACE OF SERVICE</div>
          <div className="p-4 border border-slate-200 text-center">C. EMG</div>
          <div
            className="p-4 border border-slate-200 text-center"
            style={{ gridColumn: '4 / span 2' }}
          >
            D. PROCEDURES, SERVICES, OR SUPPLIES
            <div className="flex justify-around items-center mt-1">
              <span>CPT / HCPCS</span>
              <span>Modifier</span>
            </div>
          </div>
          <div className="p-4 border border-slate-200 text-center">E. DIAGNOSIS POINTER</div>
          <div className="p-4 border border-slate-200 text-center">F. $ CHARGES</div>
          <div className="p-4 border border-slate-200 text-center">G. DAYS OR UNITS</div>
          <div className="p-4 border border-slate-200 text-center">H. EPSDT Family Plan</div>
          <div className="p-4 border border-slate-200 text-center">I. ID. QUAL.</div>
          <div className="p-2 text-center">J. RENDERING PROVIDER ID. #</div>
        </div>

        {supplementalInfo.map((info, index) => (
          <AuditableSection
            initialValue={info}
            key={index}
            title={
              <div
                className="grid grid-cols-12 bg-gray-50 border-x border-gray-300 text-xs font-semibold"
                style={{ gridTemplateColumns: '136px 136px 84px 80px 320px repeat(7, 1fr)' }}
              >
                <div className="col-span-2 p-2 border-r border-gray-300 text-center">
                  A. DATE(S) OF SERVICE
                  <div className="grid grid-cols-2 gap-2 mt-1">
                    <Label className="text-xs">From</Label>
                    <Label className="text-xs">To</Label>
                  </div>
                </div>
                <div className="!min-w-24 w-fit p-2 border-r border-gray-300 text-center">
                  B. PLACE OF SERVICE
                </div>
                <div className="col-span-1 p-2 border-r border-gray-300 text-center">C. EMG</div>
                <div className="col-span-2 p-2 border-r border-gray-300 text-center">
                  D. PROCEDURES, SERVICES, OR SUPPLIES
                  <div className="grid grid-cols-2 gap-2 mt-1">
                    <Label className="text-xs">CPT / HCPCS</Label>
                    <Label className="text-xs">Modifier</Label>
                  </div>
                </div>
                <div className="col-span-1 p-2 border-r border-gray-300 text-center">
                  E. DIAGNOSIS POINTER
                </div>
                <div className="col-span-1 p-2 border-r border-gray-300 text-center">
                  F. $ CHARGES
                </div>
                <div className="col-span-1 p-2 border-r border-gray-300 text-center">
                  G. DAYS OR UNITS
                </div>
                <div className="col-span-1 p-2 border-r border-gray-300 text-center">
                  H. EPSDT Family Plan
                </div>
                <div className="col-span-1 p-2 border-r border-gray-300 text-center">
                  I. ID. QUAL.
                </div>
                <div className="col-span-1 p-2">J. RENDERING PROVIDER ID. #</div>
              </div>
            }
            fieldName={`supplementalInformation[${index}]`}
            isLoading={isLoading}
          >
            <div
              className={`grid grid-cols-11 border-x border-slate-200 text-xs font-normal ${
                isFieldInAdjustments && isFieldInAdjustments(`supplementalInformation[${index}]`)
                  ? 'border-yellow-600 bg-yellow-50'
                  : 'bg-white'
              }`}
              style={{
                gridTemplateColumns: '280px 136px 1fr 1fr 320px repeat(6, 1fr)',
              }}
            >
              <div className="grid grid-cols-2 border-slate-200">
                <div className="p-4 border border-slate-200 text-center">
                  {info.fromDateOfService
                    ? new Date(info.fromDateOfService).toLocaleDateString()
                    : '-'}
                </div>
                <div className="p-4 border border-slate-200 text-center">
                  {info.toDateOfService ? new Date(info.toDateOfService).toLocaleDateString() : '-'}
                </div>
              </div>

              <div className="p-4 border border-slate-200 text-center">
                {info.placeOfService || '-'}
              </div>

              <div className="p-4 border border-slate-200 text-center">
                {info.emergencyIndicator}
              </div>

              <div className="grid grid-cols-2" style={{ gridColumn: '4 / span 2' }}>
                <div className="p-4 border border-slate-200 text-center">
                  {info.proceduresCode || '-'}
                </div>
                <div className="p-4 border border-slate-200 text-center">
                  {info.proceduresModifier?.filter(Boolean).join(', ') || '-'}
                </div>
              </div>

              <div className="p-4 border border-slate-200 text-center">
                {info.diagnosisPointer || '-'}
              </div>
              <div className="p-4 border border-slate-200 text-center">{info.charges || '-'}</div>
              <div className="p-4 border border-slate-200 text-center">
                {info.daysOrUnits || '-'}
              </div>
              <div className="p-4 border border-slate-200 text-center">
                {info.epsdtFamilyPlan || '-'}
              </div>
              <div className="p-4 border border-slate-200 text-center">
                {info.idQualifier || '-'}
              </div>
              <div className="p-4 border border-slate-200 text-center">
                {info.renderingProviderId || '-'}
              </div>
            </div>
          </AuditableSection>
        ))}
      </div>
    </div>
  );
};
