'use client';

import { MdOutlineDashboard } from 'react-icons/md';

import { MenuLink } from './menu-link';
import { UserRole } from '@/enums';
import useLoggedUser from '@/hooks/use-logged-user';
import { LiaFileInvoiceSolid } from 'react-icons/lia';

export function ItemsProviders({ collapsed }: { collapsed: boolean }) {
  const { Provider } = UserRole;
  const { loggedUser } = useLoggedUser();

  if (loggedUser?.role !== Provider) {
    return null;
  }

  return (
    <nav className="flex flex-col h-full py-6 px-4 gap-6 items-center w-full">
      <ul className="flex flex-col gap-4">
        <li className="mt-auto w-full">
          <MenuLink
            href="/providers/dashboard"
            label="Dashboard"
            icon={<MdOutlineDashboard />}
            collapsed={collapsed}
          />
        </li>
        <li>
          <MenuLink
            href="/invoices"
            label="Facturas"
            icon={<LiaFileInvoiceSolid />}
            collapsed={collapsed}
          />
        </li>
      </ul>
    </nav>
  );
}
