'use client';

import { useField } from 'formik';
import { MdCheck } from 'react-icons/md';

import { WrapFileUpload } from './wrap-file-upload';
import clsx from 'clsx';
import { useMemo } from 'react';

interface IFileUploadField {
  children: React.ReactNode;
  label?: string;
  name: string;
  noStyle?: boolean;
  className?: HTMLElement['className'];
}

export function FileUploadField({ name, children, label, noStyle, className }: IFileUploadField) {
  const [field, meta] = useField(name);

  const file = field.value as File | null;
  const hasError = meta.touched && meta.error;
  const showSuccessTag = !hasError && file;

  const inputLabelClasses = useMemo(() => clsx('text-sm', hasError && 'text-red-500'), [hasError]);

  return (
    <section className="flex flex-col gap-2">
      {label && <label className={inputLabelClasses}>{label}</label>}
      <WrapFileUpload noStyle={noStyle} className={className}>
        {children}
      </WrapFileUpload>
      {hasError && <span className="text-sm text-red-600">{meta.error}</span>}
      {showSuccessTag && (
        <div className="text-sm text-green-600 flex gap-4 items-center">
          <MdCheck />
          <span>File Success</span>
        </div>
      )}
    </section>
  );
}
