import { useQuery } from '@tanstack/react-query';
import { Http } from '@/config/http';

const EP = 'https://test.ehr.digheontech.com/api/diagnoses';

interface IDiagnosis {
  code: string;
  description: {
    en: string;
    es: string;
  };
}

export function useGetDiagnoses(term = '', language: 'es' | 'en' = 'es') {
  const { data: diagnoses, ...rest } = useQuery<IDiagnosis[]>({
    queryKey: ['diagnoses', term, language],
    queryFn: () =>
      Http.get(`${EP}?language=${language}&term=${encodeURIComponent(term)}`).then(
        ({ data }) => data,
      ),
    enabled: !!term,
  });

  return { diagnoses, ...rest };
}
