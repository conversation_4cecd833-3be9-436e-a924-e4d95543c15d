import { IFormInvoiceTypeAPayload } from '@/interfaces';

export const requiredFields = [
  'acceptAssignment',
  'billingProvider',
  'billingProvider.address',
  'billingProvider.city',
  'billingProvider.name',
  'billingProvider.npi',
  'billingProvider.phoneNumber',
  'billingProvider.state',
  'billingProvider.zipCode',
  'conditionPatientRelatedToAutoAccident',
  'conditionPatientRelatedToEmployment',
  'conditionPatientRelatedToOtherAccident',
  'federalTaxIdNumber',
  'federalTaxIdNumberType',
  'icd10DiagnosisCodesForDiseasesOrInjuries',
  'insuranceIdNumber',
  'insured.birthDate',
  'insured.birthDate',
  'insured.fullName',
  'insured.gender',
  'isOutsideLab',
  'patient.address.city',
  'patient.address.state',
  'patient.address.street',
  'patient.address.zipCode',
  'patient.birthDate',
  'patient.fullName',
  'patientAccountNumber',
  'patientRelationshipToInsured',
  'physicianSignature',
  'physicianSignatureDate',
  'priorAuthorizationNumber',
  'serviceFacilityLocation',
  'serviceFacilityLocation.address',
  'serviceFacilityLocation.city',
  'serviceFacilityLocation.name',
  'serviceFacilityLocation.npi',
  'serviceFacilityLocation.state',
  'serviceFacilityLocation.zipCode',
  'supplementalInformation',
  'supplementalInformation',
  'supplementalInformation[0].charges',
  'supplementalInformation[0].daysOrUnits',
  'supplementalInformation[0].diagnosisPointer',
  'supplementalInformation[0].emergencyIndicator',
  'supplementalInformation[0].epsdtFamilyPlan',
  'supplementalInformation[0].fromDateOfService',
  'supplementalInformation[0].idQualifier',
  'supplementalInformation[0].placeOfService',
  'supplementalInformation[0].proceduresCode',
  'supplementalInformation[0].renderingProviderId',
  'supplementalInformation[0].toDateOfService',
];

export const isRequiredForSubmission = (fieldName: string): boolean => {
  if (requiredFields.includes(fieldName)) return true;

  const normalizedFieldName = fieldName.replace(/\[([^\]]+)\]/g, '.$1');
  if (requiredFields.includes(normalizedFieldName)) return true;

  return false;
};

export function areRequiredFieldsComplete(values: IFormInvoiceTypeAPayload): boolean {
  const missingFields: string[] = [];

  if (!values.insuranceIdNumber) missingFields.push('insuranceIdNumber');
  if (!values.federalTaxIdNumber) missingFields.push('federalTaxIdNumber');
  if (!values.federalTaxIdNumberType) missingFields.push('federalTaxIdNumberType');
  if (!values.patientAccountNumber) missingFields.push('patientAccountNumber');
  if (!values.physicianSignature) missingFields.push('physicianSignature');
  if (!values.physicianSignatureDate) missingFields.push('physicianSignatureDate');
  if (!values.priorAuthorizationNumber) missingFields.push('priorAuthorizationNumber');
  if (
    !Array.isArray(values.icd10DiagnosisCodesForDiseasesOrInjuries) ||
    values.icd10DiagnosisCodesForDiseasesOrInjuries.length === 0
  ) {
    missingFields.push('icd10DiagnosisCodesForDiseasesOrInjuries');
  }
  if (values.conditionPatientRelatedToEmployment === undefined)
    missingFields.push('conditionPatientRelatedToEmployment');
  if (values.conditionPatientRelatedToAutoAccident === undefined)
    missingFields.push('conditionPatientRelatedToAutoAccident');
  if (values.conditionPatientRelatedToOtherAccident === undefined)
    missingFields.push('conditionPatientRelatedToOtherAccident');
  if (values.isOutsideLab === undefined) missingFields.push('isOutsideLab');
  if (values.acceptAssignment === undefined) missingFields.push('acceptAssignment');
  if (!values.patientRelationshipToInsured) missingFields.push('patientRelationshipToInsured');

  if (!values.patient?.fullName) missingFields.push('patient.fullName');
  if (!values.patient?.birthDate) missingFields.push('patient.birthDate');
  if (!values.patient?.address?.street) missingFields.push('patient.address.street');
  if (!values.patient?.address?.city) missingFields.push('patient.address.city');
  if (!values.patient?.address?.state) missingFields.push('patient.address.state');
  if (!values.patient?.address?.zipCode) missingFields.push('patient.address.zipCode');

  if (!values.insured?.fullName) missingFields.push('insured.fullName');
  if (!values.insured?.birthDate) missingFields.push('insured.birthDate');
  if (!values.insured?.gender) missingFields.push('insured.gender');

  if (!values.billingProvider?.name) missingFields.push('billingProvider.name');
  if (!values.billingProvider?.phoneNumber) missingFields.push('billingProvider.phoneNumber');
  if (!values.billingProvider?.address) missingFields.push('billingProvider.address');
  if (!values.billingProvider?.city) missingFields.push('billingProvider.city');
  if (!values.billingProvider?.state) missingFields.push('billingProvider.state');
  if (!values.billingProvider?.zipCode) missingFields.push('billingProvider.zipCode');
  if (!values.billingProvider?.npi) missingFields.push('billingProvider.npi');

  if (!values.serviceFacilityLocation?.name) missingFields.push('serviceFacilityLocation.name');
  if (!values.serviceFacilityLocation?.address)
    missingFields.push('serviceFacilityLocation.address');
  if (!values.serviceFacilityLocation?.city) missingFields.push('serviceFacilityLocation.city');
  if (!values.serviceFacilityLocation?.state) missingFields.push('serviceFacilityLocation.state');
  if (!values.serviceFacilityLocation?.zipCode)
    missingFields.push('serviceFacilityLocation.zipCode');
  if (!values.serviceFacilityLocation?.npi) missingFields.push('serviceFacilityLocation.npi');

  if (
    !Array.isArray(values.supplementalInformation) ||
    values.supplementalInformation.length === 0
  ) {
    missingFields.push('supplementalInformation');
  } else {
    values.supplementalInformation.forEach((item, index) => {
      if (!item.fromDateOfService)
        missingFields.push(`supplementalInformation[${index}].fromDateOfService`);
      if (!item.toDateOfService)
        missingFields.push(`supplementalInformation[${index}].toDateOfService`);
      if (!item.placeOfService)
        missingFields.push(`supplementalInformation[${index}].placeOfService`);
      if (!item.emergencyIndicator)
        missingFields.push(`supplementalInformation[${index}].emergencyIndicator`);
      if (!item.proceduresCode)
        missingFields.push(`supplementalInformation[${index}].proceduresCode`);
      if (!item.diagnosisPointer)
        missingFields.push(`supplementalInformation[${index}].diagnosisPointer`);
      if (!item.charges) missingFields.push(`supplementalInformation[${index}].charges`);
      if (!item.daysOrUnits) missingFields.push(`supplementalInformation[${index}].daysOrUnits`);
      if (!item.epsdtFamilyPlan)
        missingFields.push(`supplementalInformation[${index}].epsdtFamilyPlan`);
      if (!item.idQualifier) missingFields.push(`supplementalInformation[${index}].idQualifier`);
      if (!item.renderingProviderId)
        missingFields.push(`supplementalInformation[${index}].renderingProviderId`);
    });
  }

  return missingFields.length === 0 ? true : false;
}
