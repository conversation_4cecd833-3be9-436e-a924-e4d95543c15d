'use client';

import { FieldDatePicker } from '@/components';

interface Items12 {
  disabled: boolean;
}

export function Items12({ disabled }: Items12) {
  return (
    <section className="border border-gray-300 p-4 border-t-transparent hover-card ">
      <div className="flex gap-4 my-2 ml-4">
        <strong>12.</strong>
        <p>
          PATIENT&apos;S OR AUTHORIZED PERSON&apos;S SIGNATURE I authorize the release of any
          medical or other information necessary to process this claim. I also request payment of
          government benefits either to myself or to the party who accepts assignment below.
        </p>
      </div>
      <div className="w-full flex items-center gap-11">
        <div className="text-3xl w-full flex justify-center items-center h-12">
          Signature on File
        </div>
        <div className="w-1/2">
          <FieldDatePicker
            label="DATE"
            name="patientOrAuthorizedSignatureDate"
            disabled={disabled}
          />
        </div>
      </div>
    </section>
  );
}
