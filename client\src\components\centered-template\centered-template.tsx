import clsx from 'clsx';
import { GuestImage } from './guest-img';

interface IGuestLayout {
  children: React.ReactNode;
  hideLogo?: boolean;
}

export function CenteredTemplate({ children, hideLogo }: IGuestLayout) {
  const templateClasses = clsx(
    'bg-slate-100 p-6 flex min-h-screen ',
    !hideLogo && 'lg:bg-guest bg-no-repeat bg-cover',
  );

  return (
    <div className={templateClasses}>
      {!hideLogo && (
        <div className="hidden lg:flex items-center justify-center flex-1 w">
          <GuestImage />
        </div>
      )}
      <div className="flex-1 flex justify-center items-center">{children}</div>
    </div>
  );
}
