# API
PORT=4000
NODE_ENV=development
WEB_BASE_URL=
PASSWORD_RECOVERY_EXPIRATION=

# JSON Web Token
JWT_SECRET=

# Google reCaptcha
RECAPTCHA_ENABLED=true
RECAPTCHA_SECRET=

# CORS
CORS_ORIGINS=*

# Database
MONGODB_URI=mongodb://localhost:27017/somename?gssapiServiceName=mongodb

# Logging
LOKI_HOST=
LOKI_ENABLED=false
LOKI_SUFFIX_APP=

# E-mail
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_FROM=<EMAIL>
MAIL_USER=someUser
MAIL_PASSWORD=someP
MAIL_API_URL=
MAIL_API_PASSWORD=

# AWS
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_BUCKET=
AWS_REGION=
