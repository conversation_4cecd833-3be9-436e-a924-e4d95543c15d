import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import {
  InvoiceFile,
  InvoiceFileDocument,
} from '../schema/invoice-file.schema';

@Injectable()
export class InvoiceFileRepository {
  constructor(
    @InjectModel('InvoiceFile')
    private invoiceFileModel: Model<InvoiceFileDocument>,
  ) {}

  async create(data: Partial<InvoiceFile>) {
    return this.invoiceFileModel.create(data);
  }

  async update(id: string, data: Partial<InvoiceFile>) {
    return this.invoiceFileModel
      .findByIdAndUpdate(id, data, { new: true })
      .exec();
  }

  async getByInvoiceId(invoiceId: string) {
    return this.invoiceFileModel.find({ invoiceId }).exec();
  }

  async findByIdAndInvoiceId(
    id: string,
    invoiceId: string,
    failIfNotFound = false,
  ) {
    const file = await this.invoiceFileModel
      .findOne({ _id: id, invoiceId })
      .exec();
    if (!file && failIfNotFound) {
      throw new NotFoundException(`File '${id}' not found`);
    }
    return file;
  }

  async deleteById(id: string) {
    return this.invoiceFileModel.findByIdAndDelete(id).exec();
  }

  async deleteByInvoiceId(invoiceId: string) {
    return this.invoiceFileModel.deleteMany({ invoiceId }).exec();
  }

  async getByInvoiceIds(invoiceIds: string[]) {
    return this.invoiceFileModel
      .find({ invoiceId: { $in: invoiceIds } })
      .exec();
  }
}
