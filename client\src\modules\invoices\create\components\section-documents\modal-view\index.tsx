/* eslint-disable @next/next/no-img-element */
import {
  Dialog,
  DialogTrigger,
  Form,
  Heading,
  Label,
  Modal,
  ModalOverlay,
} from 'react-aria-components';
import { RiEyeLine } from 'react-icons/ri';
import { Button } from '@digheontech/digh.ui';

import { IDocument, useGetSignedDocument } from '../hooks';
import { Formik } from 'formik';
import { DocumentType } from '@/enums';
import { uploadFormSchemaEdit } from '../upload-document/upload-form.schema';
import { FormField } from '@/components';
import { DocumentTypeSelect } from '../../form-invoice-type-a/selects/document-type-select';
import { MdEdit } from 'react-icons/md';
import { useEditDocument } from '../hooks/use-edit-document';

interface IModalView {
  doc: IDocument;
}

export function ModalView({ doc }: IModalView) {
  const isPdf = doc.fileType === 'application/pdf';
  const { data: signedUrl, isLoading } = useGetSignedDocument(doc.invoiceId, doc._id);
  const { onSubmit, isEditing } = useEditDocument(doc.invoiceId, doc._id);

  const INITIAL_VALUES = {
    fileName: doc.originalName,
    documentType: doc.documentType || DocumentType.notaDeProgreso,
  };

  return (
    <DialogTrigger>
      <Button
        leftIcon={<RiEyeLine size={20} />}
        buttonType="normal"
        className="rounded-full w-10 h-10 p-2 border-none hover:bg-blue-200"
      />
      <ModalOverlay
        className={({
          isEntering,
          isExiting,
        }) => `fixed inset-0 z-10 overflow-y-auto bg-black/25 flex min-h-full items-center justify-center p-4 text-center backdrop-blur
          ${isEntering ? 'animate-in fade-in duration-300 ease-out' : ''}
          ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}`}
      >
        <Modal
          className={({
            isEntering,
            isExiting,
          }) => `w-full max-w-4xl overflow-hidden rounded-2xl bg-white pt-7 pb-10 px-10 text-left align-middle shadow-xl relative
                ${isEntering ? 'animate-in zoom-in-95 ease-out duration-300' : ''}
                ${isExiting ? 'animate-out zoom-out-95 ease-in duration-200' : ''}`}
        >
          <Dialog role="dialog" className="outline-none">
            {({ close }) => (
              <>
                {isLoading ? (
                  <p className="text-center">Cargando...</p>
                ) : isPdf ? (
                  <iframe
                    src={signedUrl}
                    title={doc.originalName}
                    className="w-full h-[80vh] border-none"
                  />
                ) : (
                  <img
                    alt={doc.originalName}
                    src={signedUrl}
                    loading="lazy"
                    className="w-full h-[60vh] border-none"
                  />
                )}
                <Formik
                  initialValues={INITIAL_VALUES}
                  validationSchema={uploadFormSchemaEdit}
                  onSubmit={(values, { resetForm }) => {
                    onSubmit(
                      {
                        fileName: values.fileName,
                        documentType: values.documentType as DocumentType,
                      },
                      () => {
                        resetForm();
                        close();
                      },
                    );
                  }}
                >
                  {({ resetForm, isSubmitting, submitForm }) => (
                    <Form className="px-8 py-4">
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <FormField
                          disabled={isSubmitting || isEditing}
                          name="fileName"
                          label="Nombre del archivo"
                          placeholder="Nombre del archivo"
                        />
                        <div className="min-w-60">
                          <Label className="text-slate-700">Tipo de documento</Label>
                          <DocumentTypeSelect
                            className="w-full mt-1"
                            disabled={isSubmitting || isEditing}
                            name="documentType"
                          />
                        </div>
                      </div>

                      <div className="flex justify-center gap-2 pt-4">
                        <Button
                          disabled={isSubmitting || isEditing}
                          fullWidth
                          type="button"
                          className="h-10 w-52"
                          buttonType="normal"
                          onPress={() => {
                            resetForm();
                            close();
                          }}
                        >
                          Cerrar
                        </Button>
                        <Button
                          leftIcon={<MdEdit />}
                          disabled={isSubmitting || isEditing}
                          fullWidth
                          type="button"
                          className="h-10 w-52"
                          primary
                          onPress={() => {
                            submitForm();
                          }}
                        >
                          {isEditing ? 'Guardando...' : 'Editar'}
                        </Button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
}
