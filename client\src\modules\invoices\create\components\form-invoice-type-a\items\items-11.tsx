import { FieldDatePicker, FormField } from '@/components';
import { Gender, YesOrNot } from '@/enums';

const { Yes, No } = YesOrNot;
const { Female, Male } = Gender;

export function Items11({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 py-4 border-t-transparent hover-card">
      <div className="flex gap-4 my-2 ml-2">
        <strong>11</strong>
        <FormField
          label="INSURED'S POLICY GROUP OR FECA NUMBER"
          name="otherInsuredPolicyOrGroupNumber"
          disabled={isLoading}
        />
      </div>
      <div className="flex justify-evenly p-4 my-2">
        <div className="w-full">
          <div className="flex gap-4 w-full">
            <strong>a.</strong>
            <FieldDatePicker
              label="INSURED'S DATE OF BIRTH"
              name="insured.birthDate"
              disabled={isLoading}
              labelClassName="text-primary-500 font-bold block text-sm mb-1"
            />
          </div>
        </div>
        <div className="w-1/2">
          <FormField
            label="SEX"
            name="insured.gender"
            type="radio"
            options={[
              { value: Female, label: 'F' },
              { value: Male, label: 'M' },
            ]}
            disabled={isLoading}
          />
        </div>
      </div>
      <div className="flex gap-4 my-2 ml-4">
        <strong>b.</strong>
        <FormField
          label="OTHER CLAIM ID (Designated by NUCC)"
          name=" "
          className="border-none bg-transparent"
          disabled
        />
      </div>
      <div className="flex gap-4 pd-4 my-2 ml-4">
        <strong>c.</strong>
        <FormField
          label="INSURANCE PLAN NAME OR PROGRAM NAME"
          name="insured.planOrProgramName"
          disabled={isLoading}
        />
      </div>
      <div className="flex gap-4 my-2 ml-4">
        <div className="flex gap-4 mb-2 w-full">
          <strong>d.</strong>
          <FormField
            label="IS THERE ANOTHER HEALTH BENEFIT PLAN?"
            name="insured.anotherHealthBenefitPlan"
            disabled={isLoading}
            type="radio"
            options={[
              { value: Yes, label: Yes },
              { value: No, label: No },
            ]}
          />
        </div>
        <small className="w-1/2">
          <strong>If yes,</strong> return to and complete item 9, 9a and 9d.
        </small>
      </div>
    </section>
  );
}
