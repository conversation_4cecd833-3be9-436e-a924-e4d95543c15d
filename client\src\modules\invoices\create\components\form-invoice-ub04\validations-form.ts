import * as Yup from 'yup';
import { IFormInvoiceUB04Payload } from '@/interfaces';

// Agregar valores iniciales para UB04
export const initialValues: IFormInvoiceUB04Payload = {
  // Información del paciente (campos 1-8)
  providerName: '',
  providerNumber: '',
  patientName: '',
  patientAddress: '',
  patientBirthDate: '',
  patientSex: '',
  admissionDate: '',
  admissionHour: '',

  // Información del proveedor (campos 9-17)
  patientId: '',
  federalTaxNumber: '',
  admissionSource: '',
  admissionType: '',
  dischargeHour: '',
  pointOfOrigin: '',
  dischargeStatus: '',

  // Información de servicios (campos 18-28)
  conditionCodes: [],
  occurrenceCodes: [],
  valueAmountCodes: [],

  // Códigos de procedimiento (campos 29-41)
  procedureCodes: [],

  // Agregar campos faltantes:
  totalPriorPayments: 0,
  balanceDue: 0,
  certificationNumber: '',
  patientReasonForVisit: '',
  prospectivePaymentSystemCode: '',
  externalCauseOfInjury: '',
  principalProcedureCode: '',

  // Actualizar estructura del médico:
  attendingPhysician: {
    name: '',
    npi: '',
    upin: '',
    qualifier: '',
  },
  operatingPhysician: {
    name: '',
    npi: '',
    upin: '',
    qualifier: '',
  },
  otherPhysician: {
    name: '',
    npi: '',
    upin: '',
    qualifier: '',
  },
  occurrenceSpanCodes: [],
  serviceLines: [],
  totalCharges: 0,
  nonCoveredCharges: 0,
  payerInformation: [],
  diagnosisCodes: [],
  remarks: '',
};

export const validationSchema = Yup.object().shape({
  providerName: Yup.string().required('Provider name is required'),
  providerNumber: Yup.string().required('Provider number is required'),
  patientName: Yup.string().required('Patient name is required'),
  patientBirthDate: Yup.date().required('Patient birth date is required'),
  patientSex: Yup.string().oneOf(['M', 'F']).required('Patient sex is required'),
  admissionDate: Yup.date().required('Admission date is required'),

  serviceLines: Yup.array()
    .of(
      Yup.object().shape({
        revenueCode: Yup.string().required('Revenue code is required'),
        totalCharges: Yup.number()
          .min(0, 'Charges must be positive')
          .required('Total charges required'),
        serviceUnits: Yup.number()
          .min(1, 'Units must be at least 1')
          .required('Service units required'),
      }),
    )
    .min(1, 'At least one service line is required'),

  diagnosisCodes: Yup.array()
    .of(
      Yup.object().shape({
        code: Yup.string().required('Diagnosis code is required'),
        type: Yup.string().oneOf(['principal', 'secondary', 'admitting', 'external']).required(),
      }),
    )
    .min(1, 'At least one diagnosis code is required'),

  attendingPhysician: Yup.object().shape({
    name: Yup.string().required('Attending physician name is required'),
    npi: Yup.string().required('Attending physician NPI is required'),
  }),

  // Agregar campos faltantes:
  totalCharges: Yup.number()
    .min(0, 'Total Charges must be non-negative')
    .required('Total Charges is required'),
  nonCoveredCharges: Yup.number()
    .min(0, 'Non-Covered Charges must be non-negative')
    .required('Non-Covered Charges is required'),
  totalPriorPayments: Yup.number().min(0, 'Total Prior Payments must be non-negative'),
  balanceDue: Yup.number(),

  // Campos de información del paciente
  patientAddress: Yup.string().required('Patient address is required'),
  patientId: Yup.string().required('Patient ID is required'),
  federalTaxNumber: Yup.string(),
  admissionSource: Yup.string(),
  admissionType: Yup.string(),
  dischargeStatus: Yup.string(),
  dischargeHour: Yup.string(),
  pointOfOrigin: Yup.string(),

  // Campos de códigos
  // Agregar validaciones para los campos de códigos:
  conditionCodes: Yup.array().of(Yup.string()),

  occurrenceCodes: Yup.array().of(
    Yup.object().shape({
      code: Yup.string().required('Occurrence code is required'),
      date: Yup.string().required('Occurrence date is required'),
    }),
  ),

  valueAmountCodes: Yup.array().of(
    Yup.object().shape({
      code: Yup.string().required('Value amount code is required'),
      amount: Yup.number().min(0, 'Amount must be non-negative').required('Amount is required'),
    }),
  ),

  procedureCodes: Yup.array().of(
    Yup.object().shape({
      code: Yup.string().required('Procedure code is required'),
      date: Yup.string().required('Procedure date is required'),
    }),
  ),

  // Información del pagador
  payerInformation: Yup.array().of(
    Yup.object().shape({
      payerName: Yup.string().required(),
      payerId: Yup.string().required(),
      releaseOfInformation: Yup.string(),
      assignmentOfBenefits: Yup.string(),
      priorPayments: Yup.number().min(0),
      estimatedAmountDue: Yup.number().min(0),
    }),
  ),

  // Información adicional del médico
  operatingPhysician: Yup.object().shape({
    name: Yup.string(),
    npi: Yup.string(),
    upin: Yup.string(),
    qualifier: Yup.string(),
  }),

  referringPhysician: Yup.object().shape({
    name: Yup.string(),
    npi: Yup.string(),
    upin: Yup.string(),
    qualifier: Yup.string(),
  }),

  // Campos adicionales
  remarks: Yup.string(),
  certificationNumber: Yup.string(),
  patientReasonForVisit: Yup.string(),
  prospectivePaymentSystemCode: Yup.string(),
  externalCauseOfInjury: Yup.string(),
  principalProcedureCode: Yup.string(),
});
