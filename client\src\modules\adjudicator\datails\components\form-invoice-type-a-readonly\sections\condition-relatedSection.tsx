import React from 'react';
import { AuditableSection } from '../../auditable-section';
import { FieldCMS, CMSCheckbox } from '../ui/cms-field';
import { isFieldInAdjustments, getOriginalFieldValue } from '../utils';
import { SectionProps } from '../interfaces';

export const ConditionRelatedSection = ({ invoice }: SectionProps) => {
  return (
    <div className="mb-1">
      <div className="grid grid-cols-3 gap-1">
        <AuditableSection title="Auditoria" fieldName="otherInsuredName">
          <FieldCMS label="" isVerified={isFieldInAdjustments(invoice, 'otherInsuredName')}>
            <div className="flex gap-2 items-center mb-1">
              <strong className="text-[14px] text-primary-500">
                9.
                <span className="ml-2 capitalize">
                  Other insured&apos;s name (last name, first name, middle initial)
                </span>
              </strong>
            </div>
            <div className="flex gap-2 ml-6 mb-1">
              <span className="ml-2 text-xs">
                {getOriginalFieldValue(invoice, 'otherInsuredName', invoice.otherInsuredName) || (
                  <span className="text-gray-400">-</span>
                )}
              </span>
            </div>
            <div className="flex gap-2 ml-6 mb-1">
              <strong>a.</strong>
              <span className="text-[12px] text-primary-500">
                Other insured&apos;s policy or group number
              </span>
              <span className="ml-2 text-xs">
                {getOriginalFieldValue(
                  invoice,
                  'insured.policyGroupNumber',
                  invoice.insured?.policyGroupNumber,
                ) || <span className="text-gray-400">-</span>}
              </span>
            </div>
            <div className="flex gap-2 ml-6 mb-1">
              <strong>b.</strong>
              <span className="text-[12px] text-primary-500">Reserved for nucc use</span>
              <span className="ml-2 text-xs text-gray-400">-</span>
            </div>
            <div className="flex gap-2 ml-6 mb-1">
              <strong>c.</strong>
              <span className="text-[12px] text-primary-500">Reserved for nucc use</span>
              <span className="ml-2 text-xs text-gray-400">-</span>
            </div>
            <div className="flex gap-2 ml-6">
              <strong>d.</strong>
              <span className="text-[12px] text-primary-500">
                Insurance plan name or program name
              </span>
              <span className="ml-2 text-xs">
                {getOriginalFieldValue(
                  invoice,
                  'otherInsuredPlanNameOrProgramName',
                  invoice.otherInsuredPlanNameOrProgramName,
                ) || <span className="text-gray-400">-</span>}
              </span>
            </div>
          </FieldCMS>
        </AuditableSection>

        <AuditableSection title="Auditoria" fieldName="conditionPatientRelatedToEmployment">
          <FieldCMS
            label=""
            isVerified={isFieldInAdjustments(invoice, 'conditionPatientRelatedToEmployment')}
          >
            <strong className="text-[14px] text-primary-500 capitalize mb-2">
              10.
              <span className="ml-2">Is patient&apos;s condition related to</span>
            </strong>
            <div className="flex flex-col gap-1 mt-2 ml-6">
              <div className="flex gap-2 items-center">
                <strong>a.</strong>
                <span className="text-[12px] text-primary-500">Employment?</span>
                <span className="flex items-center ml-2 text-xs">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'conditionPatientRelatedToEmployment',
                        invoice.conditionPatientRelatedToEmployment,
                      ) === 'YES'
                    }
                  />{' '}
                  Yes
                </span>
                <span className="flex items-center ml-2 text-xs">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'conditionPatientRelatedToEmployment',
                        invoice.conditionPatientRelatedToEmployment,
                      ) === 'NO'
                    }
                  />{' '}
                  No
                </span>
                <small className="ml-2 text-gray-400">(Current or Previous)</small>
              </div>
              <div className="flex gap-2 items-center">
                <div className="flex gap-2 items-center">
                  <strong>b.</strong>
                  <span className="text-[12px] text-primary-500">Auto accident?</span>
                  <span className="flex items-center ml-2 text-xs">
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'conditionPatientRelatedToAutoAccident',
                          invoice.conditionPatientRelatedToAutoAccident,
                        ) === 'YES'
                      }
                    />{' '}
                    Yes
                  </span>
                  <span className="flex items-center ml-2 text-xs">
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'conditionPatientRelatedToAutoAccident',
                          invoice.conditionPatientRelatedToAutoAccident,
                        ) === 'NO'
                      }
                    />{' '}
                    No
                  </span>
                </div>
                <span className="text-[12px] text-primary-500">Place (State):</span>
                <span className="ml-1 text-xs">
                  {getOriginalFieldValue(
                    invoice,
                    'conditionPatientRelatedToAutoAccidentPlace',
                    invoice.conditionPatientRelatedToAutoAccidentPlace,
                  ) || <span className="text-gray-400">-</span>}
                </span>
              </div>
              <div className="flex gap-2 items-center">
                <strong>c.</strong>
                <span className="text-[12px] text-primary-500">Other accident?</span>
                <span className="flex items-center ml-2 text-xs">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'conditionPatientRelatedToOtherAccident',
                        invoice.conditionPatientRelatedToOtherAccident,
                      ) === 'YES'
                    }
                  />{' '}
                  Yes
                </span>
                <span className="flex items-center ml-2 text-xs">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'conditionPatientRelatedToOtherAccident',
                        invoice.conditionPatientRelatedToOtherAccident,
                      ) === 'NO'
                    }
                  />{' '}
                  No
                </span>
              </div>
              <div className="flex gap-2 items-center">
                <strong>d.</strong>
                <span className="text-[12px] text-primary-500">
                  Claim Codes (Designated by NUCC)
                </span>
                <span className="ml-2 text-xs text-gray-400">-</span>
              </div>
            </div>
          </FieldCMS>
        </AuditableSection>

        <AuditableSection title="Auditoria" fieldName="otherInsuredPolicyOrGroupNumber">
          <FieldCMS
            label=""
            isVerified={isFieldInAdjustments(invoice, 'otherInsuredPolicyOrGroupNumber')}
          >
            <div className="flex gap-2 items-center">
              <strong className="text-[14px] text-primary-500 capitalize mb-2">
                11.
                <span className="ml-2">Insured&apos;s policy group or feca number</span>
              </strong>
              <span className="ml-2 text-xs">
                {getOriginalFieldValue(
                  invoice,
                  'insured.policyGroupNumber',
                  invoice.insured?.policyGroupNumber,
                ) || <span className="text-gray-400">-</span>}
              </span>
            </div>
            <div className="flex gap-2 ml-6 items-center">
              <strong>a.</strong>
              <span className="text-[12px] text-primary-500 mb-1">
                Insured&apos;s date of birth
              </span>
              <span className="ml-2 text-xs">
                {getOriginalFieldValue(invoice, 'insured.birthDate', invoice.insured?.birthDate) ? (
                  new Date(
                    getOriginalFieldValue(invoice, 'insured.birthDate', invoice.insured?.birthDate),
                  ).toLocaleDateString()
                ) : (
                  <span className="text-gray-400">-</span>
                )}
              </span>
              <span className="ml-4 text-[10px] text-primary-700 font-bold">SEX:</span>
              <span className="flex items-center ml-2 text-xs">
                F{' '}
                <CMSCheckbox
                  checked={
                    getOriginalFieldValue(invoice, 'insured.gender', invoice.insured?.gender) ===
                    'F'
                  }
                />
                <span className="ml-2">
                  M{' '}
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(invoice, 'insured.gender', invoice.insured?.gender) ===
                      'M'
                    }
                  />
                </span>
              </span>
            </div>
            <div className="flex gap-2 ml-6">
              <strong>b.</strong>
              <span className="text-[12px] text-primary-500 mb-1">
                Other Claim ID (Designated by NUCC)
              </span>
              <span className="ml-2 text-xs text-gray-400">-</span>
            </div>
            <div className="flex gap-2 lowercase ml-6">
              <strong>c.</strong>
              <span className="text-[12px] text-primary-500 mb-1">
                Insurance plan name or program name
              </span>
              <span className="ml-2 text-xs">
                {getOriginalFieldValue(
                  invoice,
                  'insured.planOrProgramName',
                  invoice.insured?.planOrProgramName,
                ) || <span className="text-gray-400">-</span>}
              </span>
            </div>
            <div className="flex gap-2 lowercase ml-6 items-center">
              <strong>d.</strong>
              <span className="text-[12px] text-primary-500 mb-1">
                IS THERE ANOTHER HEALTH BENEFIT PLAN?
              </span>
              <span className="flex items-center ml-2 text-xs">
                <CMSCheckbox
                  checked={
                    getOriginalFieldValue(
                      invoice,
                      'insured.anotherHealthBenefitPlan',
                      invoice.insured?.anotherHealthBenefitPlan,
                    ) === 'YES'
                  }
                />{' '}
                Yes
                <span className="ml-2">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'insured.anotherHealthBenefitPlan',
                        invoice.insured?.anotherHealthBenefitPlan,
                      ) === 'NO'
                    }
                  />{' '}
                  No
                </span>
              </span>
            </div>
          </FieldCMS>
        </AuditableSection>
      </div>
    </div>
  );
};
