import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// imports EN
import enTranslation from './locales/en/translation.json';
import enResponses from './locales/en/responses.json';

// imports ES
import esResponses from './locales/es/responses.json';
import esTranslation from './locales/es/translation.json';

import { Language } from './enums';

const resources = {
  en: {
    translation: enTranslation,
    responses: enResponses,
  },
  es: {
    translation: esTranslation,
    responses: esResponses,
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: Language.Spanish,
  fallbackLng: Language.Spanish,
  ns: ['translation', 'responses'],
  defaultNS: 'translation',
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
