import { ParseFilePipe, ParseFilePipeBuilder } from '@nestjs/common/pipes';
import {
  HttpStatus,
  Inject,
  Injectable,
  PipeTransform,
  UnprocessableEntityException,
} from '@nestjs/common';

type FileUploadValidationPipeParams = {
  allowedTypes: RegExp;
  maxSizeInBytes: number;
  isOptional?: boolean;
};

@Injectable()
export class FileUploadValidationPipe implements PipeTransform {
  private static DEFAULT_IMAGE_TYPES = /jpeg|jpg|png/;
  private static DEFAULT_IMAGE_SIZE_IN_BYTES = 5 * 1024 * 1024;
  private static DEFAULT_MULTIMEDIA_TYPES =
    /jpeg|jpg|png|mp4|mov|avi|wmv|flv|mp3|wav|ogg/;
  private static DEFAULT_MULTIMEDIA_SIZE_IN_BYTES = 10 * 1024 * 1024;

  private readonly filePipe: ParseFilePipe;
  private readonly isOptional: boolean;

  constructor(
    @Inject('FILE_MANAGEMENT_CONFIG')
    config: FileUploadValidationPipeParams = {
      allowedTypes: FileUploadValidationPipe.DEFAULT_IMAGE_TYPES,
      maxSizeInBytes: FileUploadValidationPipe.DEFAULT_IMAGE_SIZE_IN_BYTES,
    },
  ) {
    this.isOptional = config.isOptional || false;

    this.filePipe = new ParseFilePipeBuilder()
      .addFileTypeValidator({ fileType: config.allowedTypes })
      .addMaxSizeValidator({
        maxSize:
          config.maxSizeInBytes ||
          FileUploadValidationPipe.DEFAULT_IMAGE_SIZE_IN_BYTES,
      })

      .build({ errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY });
  }

  static image({}: Partial<FileUploadValidationPipeParams> = {}) {
    return new FileUploadValidationPipe({
      allowedTypes: this.DEFAULT_IMAGE_TYPES,
      maxSizeInBytes: this.DEFAULT_IMAGE_SIZE_IN_BYTES,
    });
  }

  static multimedia({}: Partial<FileUploadValidationPipeParams> = {}) {
    return new FileUploadValidationPipe({
      allowedTypes: this.DEFAULT_MULTIMEDIA_TYPES,
      maxSizeInBytes: this.DEFAULT_MULTIMEDIA_SIZE_IN_BYTES,
    });
  }

  static customFileType(options: Partial<FileUploadValidationPipeParams> = {}) {
    return new FileUploadValidationPipe({
      allowedTypes: options.allowedTypes || this.DEFAULT_IMAGE_TYPES,
      maxSizeInBytes:
        options.maxSizeInBytes || this.DEFAULT_IMAGE_SIZE_IN_BYTES,
    });
  }

  transform(file?: Express.Multer.File) {
    if (!file) {
      if (this.isOptional) {
        return null;
      }
      throw new UnprocessableEntityException('The file is required.');
    }

    return this.filePipe.transform(file);
  }
}
