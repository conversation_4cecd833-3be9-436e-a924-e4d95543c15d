import { useField, useFormikContext } from 'formik';
import { Label } from 'react-aria-components';
import { DiagnosesSearch } from './diagnoses-search';

interface IDiagnosis {
  line: string;
  code: string;
}

interface IMultipleDiagnosesProps {
  name: string;
  disabled?: boolean;
}

export function MultipleDiagnoses({ name, disabled = false }: IMultipleDiagnosesProps) {
  const { setFieldValue } = useFormikContext();
  const [field] = useField(name);

  const lines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L'];

  const transformToInternalFormat = (codes: string[]): IDiagnosis[] => {
    return codes.map((code, index) => ({
      line: lines[index],
      code,
    }));
  };

  const transformToExternalFormat = (diagnoses: IDiagnosis[]): string[] => {
    return diagnoses.map(d => d.code);
  };

  const handleDiagnosisSelect = (line: string, code: string) => {
    const currentDiagnoses = Array.isArray(field.value)
      ? transformToInternalFormat(field.value)
      : [];

    const newDiagnoses = [...currentDiagnoses];
    const diagnosisIndex = newDiagnoses.findIndex(d => d.line === line);

    if (code) {
      if (diagnosisIndex >= 0) {
        newDiagnoses[diagnosisIndex] = { line, code };
      } else {
        newDiagnoses.push({ line, code });
      }
    } else {
      if (diagnosisIndex >= 0) {
        newDiagnoses.splice(diagnosisIndex, 1);
      }
    }

    setFieldValue(name, transformToExternalFormat(newDiagnoses));
  };

  const getDiagnosisForLine = (line: string): string => {
    const internalFormat = Array.isArray(field.value) ? transformToInternalFormat(field.value) : [];
    return internalFormat.find((d: IDiagnosis) => d.line === line)?.code || '';
  };

  return (
    <section className="grid border border-gray-300 border-t-transparent p-4 hover-card">
      <div className="w-full">
        <strong className="mr-4">21.</strong>
        <Label className="text-slate-700">
          DIAGNOSIS OR NATURE OF ILLNESS OR INJURY
          <small className="ml-4">Relate A-L to service line below (24E)</small>
        </Label>
        <strong className="ml-12">ICD lnd: 0</strong>
      </div>

      <div className="grid grid-cols-1 mt-4 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {lines.map(line => (
          <div key={line} className="flex items-center space-x-2">
            <span className="font-bold min-w-[25px]">{line}.</span>
            <DiagnosesSearch
              onSelectDiagnosis={code => handleDiagnosisSelect(line, code)}
              initialValue={getDiagnosisForLine(line)}
              language="es"
              disabled={disabled}
            />
          </div>
        ))}
      </div>
    </section>
  );
}
