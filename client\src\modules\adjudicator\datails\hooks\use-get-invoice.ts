import { useSearchParams } from 'next/navigation';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Http } from '@/config/http';

export function useGetInvoice() {
  const searchParams = useSearchParams();
  const invoiceId = searchParams.get('invoiceId');
  const queryClient = useQueryClient();

  const queryKey = ['adjustments', invoiceId];

  const { data: invoices, ...rest } = useQuery({
    queryKey,
    queryFn: () => Http.get(`/invoices/${invoiceId}/adjustments`).then(({ data }) => data),
    enabled: !!invoiceId,
  });

  const invalidateAdjustments = () => queryClient.invalidateQueries({ queryKey });

  return { invoices, invalidateAdjustments, ...rest };
}
