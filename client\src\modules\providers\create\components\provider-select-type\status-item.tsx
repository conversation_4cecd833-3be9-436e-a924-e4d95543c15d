import { ListBoxItem } from 'react-aria-components';
import type { ListBoxItemProps } from 'react-aria-components';
import { MdCheckCircleOutline } from 'react-icons/md';

export function StatusItem(props: ListBoxItemProps & { children: React.ReactNode }) {
  return (
    <ListBoxItem
      {...props}
      className="group flex items-center gap-2 cursor-default select-none py-2 px-4 outline-none rounded text-gray-900 focus:bg-primary-600 focus:text-white"
    >
      {({ isSelected }) => (
        <>
          <span className="flex-1 flex items-center gap-2 truncate font-normal group-selected:font-medium">
            {props.children}
          </span>
          <span className="w-5 flex items-center text-primary-600 group-focus:text-white">
            {isSelected && <MdCheckCircleOutline size="S" />}
          </span>
        </>
      )}
    </ListBoxItem>
  );
}
