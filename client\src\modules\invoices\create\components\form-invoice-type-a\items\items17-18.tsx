'use client';

import { FieldDatePicker, FormField } from '@/components';
import { QualifierSelectItem17a, RoleSelectItem17 } from '../selects';

interface IItems17_18 {
  disabled: boolean;
}

export function Items17_18({ disabled }: IItems17_18) {
  return (
    <>
      <section className="grid grid-cols-2 items-center border border-gray-300 p-4 border-t-transparent  gap-4 hover-card">
        <div className="flex gap-2 flex-col">
          <div className="flex gap-4 my-2 ml-4 ">
            <strong>17.</strong>
            <div>NAME OF REFERRING PROVIDER OR OTHER SOURCE</div>
          </div>
          <div className="flex gap-2 justify-center">
            <div className="w-60 mt-2 ">
              <RoleSelectItem17 />
            </div>
            <div className="w-full">
              <FormField name="referringProviderName" className="!h-12" disabled={disabled} />
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <div className="flex gap-2 items-center">
            <strong>17a. </strong>
            <QualifierSelectItem17a className="min-w-48 mt-6 ml-4" />
            <div className="w-full">
              <FormField
                name="referringProviderOtherId"
                className="!h-12 w-full"
                disabled={disabled}
              />
            </div>
          </div>
          <div className="flex gap-4 items-center">
            <strong>17b. </strong>
            <b>NPI</b>
            <div className="w-full">
              <FormField name="referringProviderNpi" className="!h-12 w-full" disabled={disabled} />
            </div>
          </div>
        </div>
      </section>
      <section className="grid border border-gray-300 border-t-transparent border-l-transparent">
        <div className="flex flex-col gap-4 w-full p-4 hover-card">
          <div className="flex gap-4 w-full">
            <strong>18.</strong>
            <p>HOSPITALIZATION DATES RELATED TO CURRENT SERVICES</p>
          </div>

          <div className="flex gap-4 w-full justify-around">
            <FieldDatePicker label="FROM" name="hospitalizationFromDate" disabled={disabled} />
            <FieldDatePicker label="TO" name="hospitalizationToDate" disabled={disabled} />
          </div>
        </div>
      </section>
    </>
  );
}
