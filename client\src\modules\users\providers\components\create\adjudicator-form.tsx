import { Form, Formik, FormikHelpers } from 'formik';
import { Button } from '@digheontech/digh.ui';

import { FormField } from '@/components';
import { IFormAdjudicatorValues } from '@/interfaces';
import { fieldsUser, initialValues, validationSchema } from './validations-form';
import { useCreateAdjudicator } from '../../hooks/use-create-ajudicator';

interface IAdjudicatorForm {
  onSubmitSuccess: () => void;
}

export function AdjudicatorForm({ onSubmitSuccess }: IAdjudicatorForm) {
  const { create, isCreating } = useCreateAdjudicator();

  const onSubmit = (
    payload: IFormAdjudicatorValues,
    { resetForm }: FormikHelpers<IFormAdjudicatorValues>,
  ) => {
    create(payload);
    resetForm();
    onSubmitSuccess();
  };

  return (
    <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={onSubmit}>
      {({ isValid }) => (
        <Form className="w-full bg-transparent flex flex-col gap-4">
          {fieldsUser.map(({ name, label }) => (
            <FormField key={name} name={name} label={label} disabled={isCreating} />
          ))}
          <Button
            type="submit"
            primary
            className="mt-4"
            fullWidth
            disabled={!isValid || isCreating}
          >
            Crear
          </Button>
        </Form>
      )}
    </Formik>
  );
}
