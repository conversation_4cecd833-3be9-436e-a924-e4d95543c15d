{"name": "phy-corr-claims.api", "version": "0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": ">=22.13.0"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "mail:dev": "parcel serve src/shared/mail/templates/development.html", "mail:up": "maildev --ip localhost", "mongodb:refresh": "node dist/shared/commands/seed-mongo.command --refresh", "new:super-admin": "node dist/phy-corr-claims.js create-super-admin"}, "dependencies": {"@aws-sdk/lib-storage": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@faker-js/faker": "^9.4.0", "@nestjs/axios": "^3.0.3", "@nestjs/cli": "^9.5.0", "@nestjs/common": "^10.4.1", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.1", "@nestjs/cqrs": "^10.2.7", "@nestjs/jwt": "^10.2.0", "@nestjs/mongoose": "^10.0.10", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.1", "@nestlab/google-recaptcha": "^3.8.0", "@types/bcrypt": "^5.0.2", "@types/multer": "^1.4.12", "@types/multer-s3": "^3.0.3", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "mongoose": "^8.6.1", "nest-commander": "^3.15.0", "nest-winston": "^1.9.7", "nestjs-seeder": "^0.3.2", "nodemailer": "^6.9.15", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.1", "uuidv4": "^6.2.13", "winston": "^3.14.2", "winston-loki": "^6.1.2"}, "devDependencies": {"@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^10.4.1", "@types/express": "^4.17.21", "@types/jest": "29.5.12", "@types/lodash": "^4.17.7", "@types/node": "^22.5.4", "@types/nodemailer": "^6.4.15", "@types/react": "^18.3.5", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "29.7.0", "maildev": "^2.1.0", "parcel": "^2.12.0", "prettier": "^3.3.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.6.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}