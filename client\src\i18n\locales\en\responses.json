{"SUCCESS": {"CREATE_USERS": {"title": "Create User", "description": "User created successfully"}}, "ERRORS": {"unknown": "An error has ocurred.", "alreadyExists": "Already exists", "byHttpCode": {"400": "Check the fields, {{field}}", "401": "Not authorized", "403": "Unauthorized to perform this action", "404": "Resource not found", "500": "Internal server error", "502": "Bad Gateway", "503": "Service unavailable", "504": "Gateway timeout"}, "byDomainCode": {"SESSION_TOKEN_EXPIRED": "Session token has expired", "TOKEN_INVALID": "<PERSON><PERSON> is invalid.", "USER_ALREADY_REGISTERED": "User is already registered."}, "upload": {"required": "File required", "unsupported": "Unsupported file type", "fileTooLarge": "File too large"}, "CREATE_USERS": {"title": "Error Create User", "description": "User not created"}}}