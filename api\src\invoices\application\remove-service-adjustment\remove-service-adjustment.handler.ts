import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>CommandHandler } from '@nestjs/cqrs';

import { InvoiceAdjustmentRepository } from 'src/invoices/infrastructure/repositories/invoice-adjustments.repository';
import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';

import { RemoveServiceAdjustmentCommand } from './remove-service-adjustment.command';
import { DomainError } from 'src/shared/domain';

@CommandHandler(RemoveServiceAdjustmentCommand)
export class RemoveServiceAdjustmentHandler
  implements ICommandHandler<RemoveServiceAdjustmentCommand>
{
  constructor(
    private readonly invoiceAdjustmentRepository: InvoiceAdjustmentRepository,
    private readonly invoiceRepository: InvoiceRepository,
  ) {}

  async execute(command: RemoveServiceAdjustmentCommand) {
    const invoiceAdjustment = await this.findInvoiceAdjustment(
      command.originalInvoiceId,
    );

    const existingInvoice = await this.invoiceRepository.findById(
      command.originalInvoiceId,
    );

    const supplementalInformationFiltered =
      invoiceAdjustment.supplementalInformation?.filter(
        sa => sa._id !== command.serviceAdjustmentId,
      );

    return this.updateInvoiceAdjustment(
      command.originalInvoiceId,
      supplementalInformationFiltered,
      existingInvoice,
    );
  }

  private async findInvoiceAdjustment(invoiceId: string) {
    const invoiceAdjustment =
      await this.invoiceAdjustmentRepository.findByInvoiceId(invoiceId);

    if (!invoiceAdjustment) {
      throw new DomainError(
        'INVOICE_ADJUSTMENT_NOT_FOUND',
        'Invoice adjustment not found',
      );
    }

    return invoiceAdjustment;
  }

  private async updateInvoiceAdjustment(
    invoiceId,
    filteredSupplementalInformation,
    existingInvoice,
  ) {
    if (!filteredSupplementalInformation?.length) {
      return await this.invoiceAdjustmentRepository.update(invoiceId, {
        supplementalInformation: null,
        totalCharge: null,
      });
    }

    const totalCharge = this.recalculateTotalCharge(
      existingInvoice?.supplementalInformation,
      filteredSupplementalInformation,
    );

    return await this.invoiceAdjustmentRepository.update(invoiceId, {
      supplementalInformation: filteredSupplementalInformation,
      totalCharge,
    });
  }

  private recalculateTotalCharge(
    originalSupplementalInformation,
    updatedSupplementalInformation,
  ) {
    if (updatedSupplementalInformation.length === 0) {
      return undefined;
    }

    const mergedSupplementalInformation = this.getMergedSupplementalInformation(
      originalSupplementalInformation,
      updatedSupplementalInformation,
    );

    let totalCharge = 0;

    for (const info of mergedSupplementalInformation) {
      if (!info.charges) {
        continue;
      }
      totalCharge += +info.charges;
    }
    return totalCharge;
  }

  private getMergedSupplementalInformation(
    originalSupplementalInformation,
    updatedSupplementalInformation,
  ) {
    const updatedSupplementalInformationMap = new Map(
      updatedSupplementalInformation.map(item => [item._id.toString(), item]),
    );

    return originalSupplementalInformation.map(item => {
      const updatedItem: Record<string, any> =
        updatedSupplementalInformationMap.get(item._id.toString());
      if (updatedItem) {
        return {
          ...item.toObject(),
          ...updatedItem,
          _id: item._id,
        };
      }
      return item;
    });
  }
}
