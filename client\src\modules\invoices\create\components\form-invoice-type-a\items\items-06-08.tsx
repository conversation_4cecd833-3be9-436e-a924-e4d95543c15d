import { FormField } from '@/components';
import { Relationship } from '@/enums';

const { Child, Other, Self, Spouse } = Relationship;

export function Items06_08({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="flex flex-col">
      <div className="p-4 border border-y-transparent h-1/2 hover-card">
        <div className="flex gap-4">
          <strong>6.</strong>
          <div className="w-full">
            <FormField
              label="PATIENT´S RELATIONSHIP TO INSURED"
              type="radio"
              disabled={isLoading}
              name="patientRelationshipToInsured"
              options={[
                { value: Self, label: 'Self' },
                { value: Spouse, label: 'Spouse' },
                { value: Child, label: 'Child' },
                { value: Other, label: 'Other' },
              ]}
            />
          </div>
        </div>
      </div>
      <div className="p-4 border border-b-transparent h-1/2 hover-card">
        <div className="flex gap-4">
          <strong>8.</strong>
          <FormField
            disabled
            name=" "
            label="RESERVED FOR NUCC USE"
            className="border-none bg-transparent"
          />
        </div>
      </div>
    </section>
  );
}
