import { seeder } from 'nestjs-seeder';
import { NestFactory } from '@nestjs/core';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

import { AppModule } from 'src/app.module';
import {
  Patient,
  PatientSchema,
} from 'src/patients/infrastructure/schema/patient.schema';
import { PatientSeeder } from 'src/patients/infrastructure/seeders/patient.seeder';

function runInitialSeeders() {
  seeder({
    imports: [
      AppModule,
      ConfigModule,
      MongooseModule.forFeature([
        { name: Patient.name, schema: PatientSchema },
      ]),
    ],
  }).run([PatientSeeder]);
}

async function main() {
  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: ['error'],
  });
  runInitialSeeders();
  return app.close();
}

main();
