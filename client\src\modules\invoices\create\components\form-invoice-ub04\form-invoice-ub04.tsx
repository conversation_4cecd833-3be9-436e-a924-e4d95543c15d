import { useState } from 'react';
import { Button } from '@digheontech/digh.ui';
import { Form, Formik, FormikProps } from 'formik';
import { IFormInvoiceUB04Payload } from '@/interfaces';

import { validationSchema } from './validations-form';
import {
  UB04Items01_08,
  UB04Items09_17,
  UB04Items18_28,
  UB04Items29_41,
  UB04ServiceLines,
  UB04Items50_55,
  UB04Items56_65,
  UB04Items66_75,
  UB04Items76_81,
  UB04Totals,
} from './items';

interface FormInvoiceUB04Props {
  onSubmit: (values: IFormInvoiceUB04Payload) => void;
  isLoading: boolean;
  innerRef?: React.Ref<FormikProps<IFormInvoiceUB04Payload>>;
  initialValues: IFormInvoiceUB04Payload;
  invoiceId: string;
}

export function FormInvoiceUB04({
  onSubmit,
  isLoading,
  innerRef,
  initialValues,
  invoiceId,
}: FormInvoiceUB04Props) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (values: IFormInvoiceUB04Payload) => {
    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      innerRef={innerRef}
      enableReinitialize={true}
    >
      {formikProps => (
        <Form className="w-full max-w-none">
          {/* Header del formulario UB04 */}
          <div className="bg-blue-50 border-2 border-blue-200 p-4 mb-6 rounded-lg">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-blue-800 mb-2">UNIFORM BILLING FORM UB-04</h1>
              <h2 className="text-lg font-semibold text-blue-600">CMS-1450</h2>
            </div>
          </div>

          {/* Layout principal del formulario - similar al PDF */}
          <div className="space-y-0 border-2 border-black">
            {/* Sección superior: Provider y Patient Information (Fields 1-8) */}
            <div className="border-b border-black">
              <UB04Items01_08 isLoading={isLoading || isSubmitting} />
            </div>

            {/* Sección: Patient ID and Admission Information (Fields 9-17) */}
            <div className="border-b border-black">
              <UB04Items09_17 isLoading={isLoading || isSubmitting} />
            </div>

            {/* Sección: Condition and Occurrence Codes (Fields 18-28) */}
            <div className="border-b border-black">
              <UB04Items18_28 isLoading={isLoading || isSubmitting} />
            </div>

            {/* Sección: Procedure Codes and Physician Information (Fields 29-41) */}
            <div className="border-b border-black">
              <UB04Items29_41 isLoading={isLoading || isSubmitting} />
            </div>

            {/* Sección: Service Lines (Fields 42-49) */}
            <div className="border-b border-black">
              <UB04ServiceLines isLoading={isLoading || isSubmitting} />
            </div>

            {/* Sección: Payer Information (Fields 50-55) */}
            <div className="border-b border-black">
              <UB04Items50_55 isLoading={isLoading || isSubmitting} />
            </div>

            {/* Sección: Financial Information (Fields 56-65) */}
            <div className="border-b border-black">
              <UB04Items56_65 isLoading={isLoading || isSubmitting} />
            </div>

            {/* Sección: Diagnosis Codes (Fields 66-75) */}
            <div className="border-b border-black">
              <UB04Items66_75 isLoading={isLoading || isSubmitting} />
            </div>

            {/* Sección: Physician Information (Fields 76-81) */}
            <div className="border-b border-black">
              <UB04Items76_81 isLoading={isLoading || isSubmitting} />
            </div>

            {/* Sección: Totals and Summary */}
            <div>
              <UB04Totals isLoading={isLoading || isSubmitting} />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-6 border-t border-gray-200 mt-6">
            <Button
              type="submit"
              disabled={isLoading || isSubmitting || !formikProps.isValid}
              className="px-8 py-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg"
            >
              {isSubmitting ? 'Guardando...' : 'Guardar Factura UB04'}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
}
