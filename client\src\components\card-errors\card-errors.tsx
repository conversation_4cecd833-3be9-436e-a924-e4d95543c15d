'use client';

import { MdInfo } from 'react-icons/md';

interface ICardErrors {
  error?: Error | null;
  onRetry?: () => void;
  title?: string;
  description?: string;
}

export function CardErrors({ error, onRetry, title, description }: ICardErrors) {
  const onPress = () => {
    if (onRetry) onRetry();
  };

  return (
    <section className="bg-white p-8 rounded-md flex flex-col items-center justify-center sm:p-16 md:p-28 lg:p-48 h-full">
      <div className="flex justify-center flex-col items-center">
        <MdInfo className="text-red-500" size={32} />
        <h3 className="text-black text-xl font-bold leading-tight">{title ?? null}</h3>
      </div>
      <div className="w-full text-center text-slate-500 text-base leading-tight mt-4">
        {description ?? null}
        <p className="text-red-500 mt-2">{error?.message}</p>
      </div>
      {onRetry && (
        <button onClick={onPress} className="cursor-pointer mt-8 w-32">
          Reintentar
        </button>
      )}
    </section>
  );
}
