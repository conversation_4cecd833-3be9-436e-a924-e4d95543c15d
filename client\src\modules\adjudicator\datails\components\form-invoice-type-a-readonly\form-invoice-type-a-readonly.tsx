import {
  BillingSection,
  ConditionRelatedSection,
  PatientInsuredSection,
  PhysicianSupplierSection,
  SignatureSection,
} from './sections';
import { SupplementalInformationTable } from '../supplemental-information-table';
import { IFormInvoiceTypeAReadOnly } from './interfaces';
import { isFieldInAdjustments } from './utils';
import { DocumentList } from '../section-documents-view/document-list';

export const FormInvoiceTypeAReadOnly = ({ invoice }: IFormInvoiceTypeAReadOnly) => {
  return (
    <main
      className="w-full mx-auto border-2 border-sky-700 bg-white p-6 rounded shadow-md text-xs"
      style={{ fontFamily: 'Arial, sans-serif' }}
    >
      <header className="text-center text-2xl font-bold text-primary-700 mb-2 tracking-wide border-b-2 border-sky-700 pb-2">
        HEALTH INSURANCE CLAIM FORM (CMS-1500)
      </header>
      <PatientInsuredSection invoice={invoice} />
      <ConditionRelatedSection invoice={invoice} />
      <SignatureSection invoice={invoice} />
      <PhysicianSupplierSection invoice={invoice} />
      {Array.isArray(invoice.supplementalInformation) &&
      invoice.supplementalInformation.length > 0 ? (
        <SupplementalInformationTable
          isFieldInAdjustments={fieldName => isFieldInAdjustments(invoice, fieldName)}
          supplementalInfo={invoice.supplementalInformation.map(info => ({
            ...info,
            denialReason: info.reason || null,
            _id: info._id || '',
          }))}
        />
      ) : (
        <p>No hay información suplementaria disponible.</p>
      )}
      <BillingSection invoice={invoice} />
      <DocumentList />
    </main>
  );
};
