import { ReactNode } from 'react';
import { useSearchParams } from 'next/navigation';
import { IInvoiceAdjustment } from '@/interfaces';
import { getDenialReasonLabel } from '@/tools/getDenialReasonLabel';
import { DenialReason } from '@/enums';
import { fieldNameMap } from '../form-invoice-type-a-readonly/utils/field-name-map';
import { ModalDiscardAdjustments } from './modal-discard-adjustments';
import { ModalDiscardIndividualAdjustment } from './modal-discard-individual-adjustment';

interface IAdjustmentsSidebar {
  adjustments: IInvoiceAdjustment | IInvoiceAdjustment[] | null;
  originalInvoiceId?: string;
}

const getReadableFieldName = (fieldName: string): string => {
  return (
    fieldNameMap[fieldName] ||
    fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
  );
};

const extractFieldOrder = (label: string): number => {
  const match = label.match(/^(\d+)([a-z]*)/i);
  if (!match) return 999;
  const [_, number, suffix] = match;
  return parseInt(number) + (suffix ? suffix.charCodeAt(0) / 100 : 0);
};

const formatDateIfValid = (val: any): string | null => {
  if (typeof val === 'string' && /^\d{4}-\d{2}-\d{2}T/.test(val)) {
    const date = new Date(val);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }
  }
  return null;
};

const renderValue = (value: any, fieldName?: string): ReactNode => {
  if (value === null || value === undefined) return 'N/A';
  if (typeof value === 'boolean') return value ? 'Sí' : 'No';

  const formatted = formatDateIfValid(value);
  if (formatted) return formatted;

  if (Array.isArray(value)) {
    if (value.length === 0) return null;
    return (
      <ul className="list-disc pl-4">
        {value.map((v, i) => {
          if (fieldName === 'denialReason' && typeof v === 'string') {
            const translatedValue = getDenialReasonLabel(v as DenialReason);
            return <li key={i}>{translatedValue}</li>;
          }
          return <li key={i}>{renderValue(v)}</li>;
        })}
      </ul>
    );
  }

  if (typeof value === 'object' && value !== null) {
    if (Object.keys(value).length === 0) return null;
    return (
      <div className="pl-2 space-y-1">
        {Object.entries(value).map(([key, reason], idx) => {
          let displayValue = reason;
          if (key === 'denialReason' && typeof reason === 'string') {
            displayValue = getDenialReasonLabel(reason as DenialReason);
          }
          return (
            <div key={idx} className="flex justify-between text-sm">
              <span className="capitalize font-medium">{key.replace(/([A-Z])/g, ' $1')}:</span>
              <span className="text-right">{renderValue(displayValue, key)}</span>
            </div>
          );
        })}
      </div>
    );
  }

  return String(value);
};

const getAdjustedFields = (
  adjustments: IInvoiceAdjustment | IInvoiceAdjustment[] | null,
): { fieldName: string; value: any; label: string }[] => {
  if (!adjustments) return [];
  const adjustmentsArray = Array.isArray(adjustments) ? adjustments : [adjustments];
  const fields: { fieldName: string; value: any; label: string }[] = [];

  const addField = (key: string, value: any, prefix = '') => {
    const fieldName = prefix ? `${prefix}.${key}` : key;

    if (
      key.startsWith('_') ||
      ['__v', 'createdAt', 'updatedAt', 'originalInvoiceId', 'fileCount'].includes(key) ||
      value === null ||
      (typeof value === 'object' && Object.keys(value).length === 0) ||
      (Array.isArray(value) && value.length === 0)
    ) {
      return;
    }

    if (
      typeof value === 'object' &&
      !Array.isArray(value) &&
      (key === 'patient' || key === 'insured')
    ) {
      Object.entries(value).forEach(([nestedKey, nestedValue]) => {
        addField(nestedKey, nestedValue, key);
      });
    } else {
      fields.push({ fieldName, value, label: getReadableFieldName(fieldName) });
    }
  };

  adjustmentsArray.forEach(adj => {
    Object.entries(adj).forEach(([key, value]) => {
      addField(key, value);
    });
  });

  return fields;
};

export const AdjustmentsSidebar = ({ adjustments, originalInvoiceId }: IAdjustmentsSidebar) => {
  const searchParams = useSearchParams();
  const invoiceId = searchParams.get('invoiceId');
  const rawFields = getAdjustedFields(adjustments);
  const sortedFields = rawFields.sort(
    (a, b) => extractFieldOrder(a.label) - extractFieldOrder(b.label),
  );

  if (sortedFields.length === 0) {
    return (
      <div className="bg-primary-100 p-6 rounded-xl shadow-md border border-dashed border-yellow-400 text-center min-h-svh flex justify-center flex-col">
        <h2 className="text-xl font-bold text-yellow-700 mb-2">Adjusted Fields</h2>
        <p className="text-yellow-600 text-sm italic">No adjusted fields yet</p>
        <div className="mt-24 text-yellow-500 text-[6rem] animate-pulse">⚠️</div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-primary-700">Adjusted Fields</h2>
      </div>

      <div className="my-4 pb-4 border-b border-gray-200">
        {originalInvoiceId && (
          <ModalDiscardAdjustments
            originalInvoiceId={originalInvoiceId}
            disabled={sortedFields.length === 0}
          />
        )}
      </div>

      <ul className="space-y-3 max-h-[1560px] overflow-auto">
        {sortedFields.map((field, index) => (
          <li key={index} className="bg-yellow-50 border border-yellow-300 rounded p-3 shadow-sm">
            <div className="flex items-start gap-3">
              {invoiceId && (
                <ModalDiscardIndividualAdjustment
                  invoiceId={invoiceId}
                  serviceId={field.fieldName}
                  fieldLabel={field.label}
                  fieldValue={field.value}
                />
              )}
              <div className="flex-1">
                <p className="font-semibold text-sm text-gray-700 mb-1">{field.label}</p>
                <div className="text-sm text-gray-600 break-words">{renderValue(field.value)}</div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};
