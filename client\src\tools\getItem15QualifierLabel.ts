import { Item15Qualifier } from '@/enums';

export const getItem15QualifierLabel = (qual: Item15Qualifier) => {
  const labels = {
    [Item15Qualifier.InitialTreatment]: 'Initial Treatment',
    [Item15Qualifier.LatestVisit]: 'Latest Visit or Consultation',
    [Item15Qualifier.AcuteCondition]: 'Acute Manifestation of a Chronic Condition',
    [Item15Qualifier.Accident]: 'Accident',
    [Item15Qualifier.LastXRay]: 'Last X-ray',
    [Item15Qualifier.Prescription]: 'Prescription',
    [Item15Qualifier.ReportStart]: 'Report Start (Assumed Care Date)',
    [Item15Qualifier.ReportEnd]: 'Report End (Relinquished Care Date)',
    [Item15Qualifier.FirstVisit]: 'First Visit or Consultation',
  };
  return labels[qual];
};
