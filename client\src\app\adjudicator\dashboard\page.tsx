'use client';

import { AppMenu, SidebarHeaderTemplate } from '@/components';
import { useSidebarCollapsed } from '@/hooks/use-sidebar-collapsed';
import { DashboardPage } from '@/modules/adjudicator';

export default function AdjudicatorDashboard() {
  const [collapsed, setCollapsed] = useSidebarCollapsed();

  return (
    <SidebarHeaderTemplate
      sidebar={<AppMenu collapsed={collapsed} setCollapsed={setCollapsed} />}
      header={null}
      collapsed={collapsed}
    >
      <DashboardPage />
    </SidebarHeaderTemplate>
  );
}
