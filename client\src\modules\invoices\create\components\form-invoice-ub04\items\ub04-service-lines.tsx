import { FieldArray } from 'formik';
import { FormField } from '@/components';
import { Button } from '@digheontech/digh.ui';
import { Key } from 'react';

export function UB04ServiceLines({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 p-4">
      <h3 className="text-lg font-bold mb-4">Service Lines (Revenue Codes)</h3>
      <FieldArray name="serviceLines">
        {({ push, remove, form }) => (
          <div>
            <div className="grid grid-cols-7 gap-2 mb-2 font-bold">
              <span>Revenue Code</span>
              <span>Description</span>
              <span>HCPCS/Rate</span>
              <span>Service Date</span>
              <span>Units</span>
              <span>Total Charges</span>
              <span>Non-Covered</span>
            </div>

            {form.values.serviceLines?.map((_: any, index: Key | null | undefined) => (
              <div key={index} className="grid grid-cols-7 gap-2 mb-2">
                <FormField name={`serviceLines.${index}.revenueCode`} disabled={isLoading} />
                <FormField name={`serviceLines.${index}.revenueDescription`} disabled={isLoading} />
                <FormField name={`serviceLines.${index}.hcpcsCode`} disabled={isLoading} />
                <FormField
                  name={`serviceLines.${index}.serviceDate`}
                  type="date"
                  disabled={isLoading}
                />
                <FormField
                  name={`serviceLines.${index}.serviceUnits`}
                  type="number"
                  disabled={isLoading}
                />
                <FormField
                  name={`serviceLines.${index}.totalCharges`}
                  type="number"
                  step={0.01}
                  disabled={isLoading}
                />
                <FormField
                  name={`serviceLines.${index}.nonCoveredCharges`}
                  type="number"
                  step={0.01}
                  disabled={isLoading}
                />
              </div>
            ))}

            <Button
              type="button"
              onPress={() =>
                push({
                  revenueCode: '',
                  revenueDescription: '',
                  hcpcsCode: '',
                  serviceDate: '',
                  serviceUnits: 0,
                  totalCharges: 0,
                  nonCoveredCharges: 0,
                })
              }
            >
              Add Service Line
            </Button>
          </div>
        )}
      </FieldArray>
    </section>
  );
}
