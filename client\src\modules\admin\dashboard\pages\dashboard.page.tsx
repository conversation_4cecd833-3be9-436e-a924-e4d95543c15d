import React from 'react';
import { GrBusinessService } from 'react-icons/gr';
import { LuUsersRound, LuCirclePercent, LuDollarSign, LuChartSpline } from 'react-icons/lu';

import { ItemsCard } from '@/components';

export function DashboardPage() {
  return (
    <div className="min-h-screen bg-white p-8">
      <div className="w-full mx-auto">
        <section className="flex justify-start w-full flex-wrap gap-16 items-center">
          <ItemsCard href="/providers/list" title="Proveedores" icon={GrBusinessService} />
          <ItemsCard href="/users/providers" title="Usuarios" icon={LuUsersRound} />
          <ItemsCard href="/" title="Reclamaciones" icon={LuCirclePercent} />
          <ItemsCard href="/" title="Finanzas" icon={LuDollarSign} />
          <ItemsCard href="/" title="Reportes" icon={LuChartSpline} />
        </section>
      </div>
    </div>
  );
}
