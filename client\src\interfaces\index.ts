import {
  ProvidersType,
  UserRole,
  Provider<PERSON>tatus,
  InsuranceCarrier,
  Gender,
  Relationship,
  InvoiceType,
  InvoiceStatus,
  YesOrNot,
  Item14Qualifier,
  Item15Qualifier,
  Item17Role,
  Item22ResubmissionCode,
  ItemEPSDT,
  DenialReason,
} from '@/enums';
import { Language } from '@/i18n/enums';

export interface IAddress {
  street: string;
  municipality: string;
  zipCode: number;
}

export interface IUser {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phoneNumber?: string;
  role: UserRole;
  address?: IAddress | object;
  language?: Language;
  avatarUrl?: string;
  createdAt?: Date;
  updatedAt?: Date;
  providerId?: string;
}

export interface IFormProvidersValues {
  providerName: string;
  npi: string;
  type?: ProvidersType;
  userName: string;
  userLastName: string;
  email: string;
  password: string;
}

export interface IFormAdjudicatorValues {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}
export interface IProvidersTable {
  _id: string;
  name: string;
  npi: string;
  type: ProvidersType;
  status: ProviderStatus;
}

export interface IProviderUsersTable {
  _id: string;
  name: string;
  lastName: string;
  email: string;
  role: string;
  status: ProviderStatus;
}

export interface IInvoicesTable {
  _id: string;
  patientRecordNumber: string;
  patient: {
    fullName: string;
    birthDate: Date;
    gender?: Gender;
    address?: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
    };
    phoneNumber?: string;
  };
  type: InvoiceType;
  date: string;
  fileCount: number;
  status: InvoiceStatus;
  updatedAt: Date;
}

export interface IFormValidateProviders {
  npi?: string;
  providerId?: string;
  userId: string;
}

export interface IOnLogin {
  token: string;
  user: IUser;
}

export interface IInvoicePayload {
  patientRecordNumber: string;
  type: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface IPatient {
  fullName: string;
  birthDate?: string;
  gender?: Gender;
  address: Address;
  phoneNumber?: string;
}

export interface IInsured {
  fullName: string;
  address: Address;
  phoneNumber: string;
  policyGroupNumber: string;
  birthDate?: string;
  gender?: Gender;
  planOrProgramName: string;
  anotherHealthBenefitPlan: YesOrNot | boolean;
}

export interface InvoiceTypeA {
  json: Json;
}

interface Json {
  _id: string;
  conditionPatientRelatedToAutoAccident: boolean;
  conditionPatientRelatedToAutoAccidentPlace: string;
  conditionPatientRelatedToEmployment: string;
  conditionPatientRelatedToOtherAccident: boolean;
  createdAt: string;
  createdBy: string;
  dateOfCurrentIllnessInjuryPregnancy: string;
  hospitalizationFromDate: string;
  hospitalizationToDate: string;
  insuranceCarrier: string;
  insuranceIdNumber: string;
  insured: IInsured;
  insuredOrAuthorizedSignature: string;
  isOutsideLab: YesOrNot | boolean;
  otherDateConditionOfIllnessOrTreatment: string;
  otherInsuredName: string;
  otherInsuredPlanNameOrProgramName: string;
  otherInsuredPolicyOrGroupNumber: string;
  outsideLabCharges?: number | 0.0;
  patient: IPatient;
  patientOrAuthorizedSignature: string;
  patientOrAuthorizedSignatureDate: string;
  patientRecordNumber: string;
  patientRelationshipToInsured: Relationship | '';
  qualifierOfCurrentIllnessInjuryAccident: Item14Qualifier | '';
  qualifierOfOtherConditionOfIllnessOrTreatment: Item15Qualifier | '';
  referringProviderName: string;
  referringProviderNpi: string;
  referringProviderOtherId: string;
  referringProviderOtherIdQualifier?: string;
  referringProviderQualifier: Item17Role | '';
  type: InvoiceType;
  unableToWorkFromDate: string;
  unableToWorkToDate: string;
  updatedAt: string;
  icd10DiagnosisCodesForDiseasesOrInjuries: IDiagnosis[];
  originalReferenceNumber: string;
  priorAuthorizationNumber: string;
  resubmissionCode: Item22ResubmissionCode | '';
  serviceFacilityLocation: IServiceFacilityLocation;
  billingProvider: IBillingProvider;
  acceptAssignment: YesOrNot | boolean;
  federalTaxIdNumber?: string;
  federalTaxIdNumberType?: string;
  patientAccountNumber?: string;
  totalCharge?: number | 0.0;
  amountPaid?: number | 0.0;
  physicianSignatureDate: string;
  physicianSignature: string;
}

export interface IDiagnosis {
  code: string;
  description: {
    en: string;
    es: string;
  };
}

export interface ISupplementalInformation {
  _id?: string;
  fromDateOfService?: string;
  toDateOfService?: string;
  placeOfService?: string;
  emergencyIndicator?: YesOrNot | YesOrNot.No;
  proceduresCode?: string;
  proceduresModifier: any[];
  diagnosisPointer?: string;
  charges?: number | 0.0;
  daysOrUnits?: number;
  epsdtFamilyPlan?: ItemEPSDT | '';
  idQualifier?: string;
  renderingProviderId?: string;
  reason?: DenialReason | '';
  denialReason?: DenialReason | null; // Agregar esta propiedad
}

export interface IServiceFacilityLocation {
  name?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  npi?: string;
  otherId?: string;
}

export interface IBillingProvider {
  name?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  npi?: string;
  otherId?: string;
}

export interface IInvoiceAdjustment {
  _id: string;
  originalInvoiceId: string;
  __v: number;
  createdAt: string;
  fileCount: number;
  icd10DiagnosisCodesForDiseasesOrInjuries: any[];
  patient: {
    fullName: string;
  };
  supplementalInformation: any[];
  updatedAt: string;
  [key: string]: any; // Para permitir campos adicionales
}

export interface IFormInvoiceTypeAPayload {
  invoiceAdjustments: IInvoiceAdjustment | IInvoiceAdjustment[] | null;
  patient: IPatient;
  insured: IInsured;
  supplementalInformation: ISupplementalInformation[];
  serviceFacilityLocation: IServiceFacilityLocation;
  billingProvider: IBillingProvider;

  // TYPE STRING
  conditionPatientRelatedToAutoAccidentPlace: string;
  dateOfCurrentIllnessInjuryPregnancy?: string;
  insuranceIdNumber: string;
  insuredOrAuthorizedSignature: string;
  otherDateConditionOfIllnessOrTreatment?: string;
  otherInsuredName: string;
  otherInsuredPlanNameOrProgramName: string;
  otherInsuredPolicyOrGroupNumber: string;
  patientOrAuthorizedSignature: string;
  referringProviderName: string;
  referringProviderNpi: string;
  referringProviderOtherId: string;
  referringProviderOtherIdQualifier: string;
  originalReferenceNumber: string;
  priorAuthorizationNumber: string;
  federalTaxIdNumber?: string;
  federalTaxIdNumberType?: string;
  patientAccountNumber?: string;
  physicianSignature?: string;

  // TYPE NUMBER
  outsideLabCharges?: number | 0.0;
  totalCharge?: number | 0.0;
  amountPaid?: number | 0.0;

  // TYPE ENUM
  insuranceCarrier: InsuranceCarrier | undefined;
  patientRelationshipToInsured: Relationship | undefined;
  qualifierOfCurrentIllnessInjuryAccident: Item14Qualifier | undefined;
  qualifierOfOtherConditionOfIllnessOrTreatment: Item15Qualifier | undefined;
  referringProviderQualifier: Item17Role | undefined;
  icd10DiagnosisCodesForDiseasesOrInjuries: IDiagnosis[];
  resubmissionCode: Item22ResubmissionCode | undefined;

  // TYPE BOOLEAN
  conditionPatientRelatedToAutoAccident: YesOrNot | boolean;
  conditionPatientRelatedToEmployment: YesOrNot | boolean;
  conditionPatientRelatedToOtherAccident: YesOrNot | boolean;
  isOutsideLab: YesOrNot | boolean;
  acceptAssignment: YesOrNot | boolean;

  // TYPE STRING (FECHAS)
  hospitalizationFromDate?: string;
  hospitalizationToDate?: string;
  patientOrAuthorizedSignatureDate?: string;
  unableToWorkFromDate?: string;
  unableToWorkToDate?: string;
  physicianSignatureDate?: string;
}

export interface ICompletedInvoice {
  _id: string;
  type: string;
  patientRecordNumber: string;
  createdBy: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  fileCount: number;
  icd10DiagnosisCodesForDiseasesOrInjuries: string[] | IDiagnosis[];
  supplementalInformation: ISupplementalInformation[];
  acceptAssignment?: boolean;
  billingProvider?: IBillingProvider;
  conditionPatientRelatedToAutoAccident?: boolean;
  conditionPatientRelatedToAutoAccidentPlace?: string;
  conditionPatientRelatedToEmployment?: boolean;
  conditionPatientRelatedToOtherAccident?: boolean;
  federalTaxIdNumber?: string;
  federalTaxIdNumberType?: string;
  insuranceCarrier?: string;
  insuranceIdNumber?: string;
  insured?: IInsured;
  isOutsideLab?: boolean;
  originalReferenceNumber?: string;
  otherInsuredName?: string;
  otherInsuredPlanNameOrProgramName?: string;
  otherInsuredPolicyOrGroupNumber?: string;
  outsideLabCharges?: number;
  patient?: IPatient;
  patientAccountNumber?: string;
  patientRelationshipToInsured?: string;
  physicianSignature?: string;
  physicianSignatureDate?: string;
  priorAuthorizationNumber?: string;
  qualifierOfCurrentIllnessInjuryAccident?: string;
  qualifierOfOtherConditionOfIllnessOrTreatment?: string;
  referringProviderName?: string;
  referringProviderNpi?: string;
  referringProviderOtherId?: string;
  referringProviderOtherIdQualifier?: string;
  referringProviderQualifier?: string;
  resubmissionCode?: string;
  serviceFacilityLocation?: IServiceFacilityLocation;
  totalCharge?: number;
  amountPaid?: number;
  dateOfCurrentIllnessInjuryPregnancy?: string;
  hospitalizationFromDate?: string;
  hospitalizationToDate?: string;
  patientOrAuthorizedSignatureDate?: string;
  unableToWorkFromDate?: string;
  unableToWorkToDate?: string;
  otherDateConditionOfIllnessOrTreatment?: string;
  confirmDate?: string;
  confirmSign?: string;
}

export interface ICPT {
  [x: string]: any;
  code: string;
  description: string;
}

// Agregar al archivo de interfaces existente
export interface IFormInvoiceUB04Payload {
  // Información del paciente (campos 1-8)
  providerName: string;
  providerNumber: string;
  patientName: string;
  patientAddress: string;
  patientBirthDate: string;
  patientSex: string;
  admissionDate: string;
  admissionHour: string;

  // Información del proveedor (campos 9-17)
  patientId: string;
  federalTaxNumber: string;
  admissionSource: string;
  admissionType: string;
  dischargeHour: string;
  pointOfOrigin: string;
  dischargeStatus: string;

  // Información de servicios (campos 18-28)
  conditionCodes: string[];
  occurrenceCodes: IUB04OccurrenceCode[];
  occurrenceSpanCodes: IUB04OccurrenceSpanCode[];
  valueAmountCodes: IUB04ValueAmountCode[];

  // Líneas de servicio (campos 42-49)
  serviceLines: IUB04ServiceLine[];

  // Totales (campos 50-55)
  totalCharges: number;
  nonCoveredCharges: number;

  // Información del pagador (campos 50-65)
  payerInformation: IUB04PayerInfo[];

  // Códigos de diagnóstico (campos 66-75)
  diagnosisCodes: IUB04DiagnosisCode[];

  // Información adicional
  remarks: string;
  attendingPhysician: IUB04Physician;
  operatingPhysician?: IUB04Physician;
  otherPhysician?: IUB04Physician;

  // Campos faltantes que se usan en el código:
  procedureCodes?: Array<{
    code: string;
    date: string;
  }>;
  totalPriorPayments?: number;
  balanceDue?: number;
  certificationNumber?: string;
  patientReasonForVisit?: string;
  prospectivePaymentSystemCode?: string;
  externalCauseOfInjury?: string;
  principalProcedureCode?: string;

  // Agregar campos NPI que se usan en invoices-create.page.tsx:
  npiProvider?: string; // Cambiar de providerNpi a npiProvider
  npiOther?: string;
  estimatedAmountDue?: number;
  priorPayments?: number;
  amountDue?: number;
}

export interface IUB04ServiceLine {
  revenueCode: string;
  revenueDescription: string;
  hcpcsCode: string;
  serviceDate: string;
  serviceUnits: number;
  totalCharges: number;
  nonCoveredCharges: number;
  denialReason?: string;
}

export interface IUB04OccurrenceCode {
  code: string;
  date: string;
}

// Agregar esta interfaz:
export interface IUB04OccurrenceSpanCode {
  code: string;
  fromDate: string;
  toDate: string;
}

// Eliminar estas interfaces duplicadas al final del archivo:
// export interface IUB04Physician { ... } (la segunda definición)
// export interface IUB04ValueAmountCode { ... } (si está duplicada)
// export interface IUB04PayerInfo { ... } (si está duplicada)
// export interface IUB04DiagnosisCode { ... } (si está duplicada)

// Mantener solo las primeras definiciones y actualizar IUB04Physician:
export interface IUB04Physician {
  name: string;
  npi: string;
  upin?: string;
  qualifier?: string; // Agregar qualifier como opcional
}

// Agregar interfaces para los códigos
export interface IUB04ConditionCode {
  code: string;
}

export interface IUB04ValueAmountCode {
  code: string;
  amount: number;
}

export interface IUB04PayerInfo {
  priorPayments: number;
  payerName: string;
  payerId: string;
  groupNumber: string;
  treatmentAuthorizationCode: string;
  documentControlNumber: string;
  employerName: string;
}

export interface IUB04DiagnosisCode {
  code: string;
  description: string;
  type: 'principal' | 'secondary' | 'admitting' | 'external';
}

export interface IUB04Physician {
  name: string;
  npi: string;
  upin?: string;
}
