import { Http } from '@/config/http';
import { YesOrNot } from '@/enums';
import { useShowError, useShowSuccess } from '@/hooks';
import { useToast } from '@/hooks/useToast';
import { IFormInvoiceTypeAPayload, IFormInvoiceUB04Payload } from '@/interfaces';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';

interface ConfirmInvoicePayload {
  invoiceId: string;
  confirmSign: string;
  confirmDate: Date;
  invoiceData: IFormInvoiceTypeAPayload | IFormInvoiceUB04Payload;
}

// Auxiliary function to convert YesOrNot to boolean
const convertYesNoToBoolean = (value: string | boolean | undefined): boolean | undefined => {
  if (value === YesOrNot.Yes || value === 'YES') return true;
  if (value === YesOrNot.No || value === 'NO') return false;
  if (typeof value === 'boolean') return value;
  return undefined;
};

// Auxiliary function to ensure that dates are instances of Date
const ensureDate = (value: Date | string | undefined): Date | undefined => {
  if (!value) return undefined;
  if (value instanceof Date) return value;
  try {
    return new Date(value);
  } catch (e) {
    console.error('Error al convertir fecha:', e);
    return undefined;
  }
};

export const useConfirmInvoice = () => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { triggerToast } = useToast();
  const router = useRouter();

  const {
    mutate: confirm,
    isPending: isConfirming,
    ...rest
  } = useMutation({
    mutationKey: ['confirm_invoice'],
    mutationFn: ({ invoiceId, confirmSign, confirmDate, invoiceData }: ConfirmInvoicePayload) => {
      // Verificar si es TypeA o UB04 y procesar en consecuencia
      const isTypeA = 'patient' in invoiceData && 'insured' in invoiceData;

      let processedData;

      if (isTypeA) {
        const typeAData = invoiceData as IFormInvoiceTypeAPayload;
        processedData = {
          ...typeAData,
          supplementalInformation: typeAData.supplementalInformation,
          insured: {
            ...typeAData.insured,
            anotherHealthBenefitPlan: convertYesNoToBoolean(
              typeAData.insured?.anotherHealthBenefitPlan,
            ),
          },
          conditionPatientRelatedToEmployment: convertYesNoToBoolean(
            typeAData.conditionPatientRelatedToEmployment,
          ),
          conditionPatientRelatedToAutoAccident: convertYesNoToBoolean(
            typeAData.conditionPatientRelatedToAutoAccident,
          ),
          conditionPatientRelatedToOtherAccident: convertYesNoToBoolean(
            typeAData.conditionPatientRelatedToOtherAccident,
          ),
          isOutsideLab: convertYesNoToBoolean(typeAData.isOutsideLab),
          acceptAssignment: convertYesNoToBoolean(typeAData.acceptAssignment),
          dateOfCurrentIllnessInjuryPregnancy: ensureDate(
            typeAData.dateOfCurrentIllnessInjuryPregnancy,
          ),
          otherDateConditionOfIllnessOrTreatment: ensureDate(
            typeAData.otherDateConditionOfIllnessOrTreatment,
          ),
          confirmSign,
          confirmDate,
        };
      } else {
        // Para UB04, simplemente agregar confirmSign y confirmDate
        processedData = {
          ...invoiceData,
          confirmSign,
          confirmDate,
        };
      }

      return Http.patch(`invoices/${invoiceId}/confirm`, processedData);
    },
    onError: (error: AxiosError) => {
      if (error.response?.status === 400) {
        const { data } = error.response;

        if (data && typeof data === 'object' && 'message' in data && Array.isArray(data.message)) {
          const errorMessages = data.message.map(item => {
            if (item && typeof item === 'object' && item.name && item.errors) {
              const fieldName = item.name
                .replace(/([A-Z])/g, ' $1')
                .replace(/^./, (str: string) => str.toUpperCase())
                .replace(
                  /\.([a-z])/g,
                  (match: string) => `: ${match[1].toUpperCase()}${match.substring(2)}`,
                )
                .replace(/([a-z])([A-Z])/g, '$1 $2');

              return `${fieldName}: ${item.errors[0]}`;
            }
            return String(item);
          });

          triggerToast({
            title: 'Campos con errores',
            description: errorMessages,
          });
        } else {
          // Fallback for other types of errors
          triggerToast({
            title: 'Error de validación',
            description:
              data !== null &&
              typeof data === 'object' &&
              'message' in data &&
              typeof data.message === 'string'
                ? data.message
                : 'Por favor, revise los campos del formulario',
          });
        }

        // Log the complete error for debugging
        console.error('Error de validación:', data);
      } else {
        showError(error);
      }
    },
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Factura completada y enviada.' });
      router.push('/invoices');
    },
  });

  return { confirm, isConfirming, ...rest };
};
