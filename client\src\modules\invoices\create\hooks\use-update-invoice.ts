import { useMutation } from '@tanstack/react-query';
import { useShowError, useShowSuccess } from '@/hooks';
import { AxiosResponseError, Http } from '@/config/http';
import { IFormInvoiceTypeAPayload } from '@/interfaces';
import { useGetInvoice } from './use-get-invoice';

export const useUpdateInvoice = (invoiceId: string | null) => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateInvoiceId } = useGetInvoice();

  const {
    mutate: update,
    isPending: isUpdating,
    ...rest
  } = useMutation({
    mutationKey: ['update_invoice'],
    mutationFn: (payload: IFormInvoiceTypeAPayload) => Http.patch(`invoices/${invoiceId}`, payload),
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Factura actualizada.' });
      invalidateInvoiceId();
    },
  });

  return { update, isUpdating, ...rest };
};
