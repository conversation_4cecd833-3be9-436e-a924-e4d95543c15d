import { FormField } from '@/components';
import { CityZipCodeSearch } from '../searchs/city-zip-code-search';

export function Items33({ disabled }: { disabled: boolean }) {
  return (
    <section className="col-span-1 border border-gray-300 border-y-transparent p-4 hover-card">
      <div className="w-full mt-2">
        <b>33.</b> BILLING PROVIDER INFO & PH #
      </div>

      <section className="w-full grid grid-cols gap-4 mt-2">
        <div className="grid grid-cols-2 gap-4">
          <FormField disabled={disabled} name="billingProvider.name" label="NAME" />
          <FormField
            disabled={disabled}
            name="billingProvider.phoneNumber"
            label="PHONE NUMBER"
            type="phone"
          />
        </div>
        <FormField disabled={disabled} name="billingProvider.address" label="ADDRESS" />
        <CityZipCodeSearch
          city="billingProvider.city"
          state="billingProvider.state"
          zipCode="billingProvider.zipCode"
          disabled={disabled}
        />
      </section>
      <section className="grid grid-cols-2 gap-4 mt-2">
        <FormField disabled={disabled} name="billingProvider.npi" label="A." />
        <FormField disabled={disabled} name="billingProvider.otherId" label="B." />
      </section>
    </section>
  );
}
