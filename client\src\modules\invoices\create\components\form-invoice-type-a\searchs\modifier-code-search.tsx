import { SearchField } from 'react-aria-components';
import { SetStateAction, useState, useEffect } from 'react';
import { Input } from '@digheontech/digh.ui';

import {
  IModifierCode,
  useGetDiagnosesModifiers,
} from '../../../hooks/use-get-diagnoses-modifiers';

interface IModifierCodeSearch {
  onSelectModifierCode: (code: string) => void;
  initialValue: string;
  language?: 'es' | 'en';
  disabled?: boolean;
  className?: string;
}

export function ModifierCodeSearch({
  onSelectModifierCode,
  initialValue,
  language = 'es',
  disabled,
  className,
}: IModifierCodeSearch) {
  const [inputValue, setInputValue] = useState(initialValue || '');
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  const { modifierCodes = [], isLoading } = useGetDiagnosesModifiers(searchTerm, language);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setInputValue(initialValue || '');
    setSearchTerm(initialValue || '');
  }, [initialValue]);

  const handleSearch = (value: SetStateAction<string>) => {
    const newValue = value as string;
    setInputValue(newValue);
    setShowResults(!!newValue);

    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (newValue) {
      const timeout = setTimeout(() => {
        setSearchTerm(newValue);
      }, 500);
      setSearchTimeout(timeout);
    } else {
      setSearchTerm('');
    }
  };

  const handleSelectModifierCode = (modifierCode: IModifierCode) => {
    setInputValue(modifierCode.description[language]);
    setSearchTerm(modifierCode.description[language]);
    setShowResults(false);
    onSelectModifierCode(modifierCode.code);
  };

  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  return (
    <div className={`relative ${className || ''}`}>
      <SearchField
        value={inputValue}
        onChange={handleSearch}
        className="relative flex items-center w-full mt-1"
        aria-label="Modifier Code"
      >
        <Input
          aria-label="Modifier Code"
          value={inputValue}
          placeholder="Buscar..."
          className="field-shadow !h-10 !w-16"
          disabled={disabled}
        />
      </SearchField>

      {showResults && modifierCodes?.length > 0 && (
        <ul className="absolute z-10 w-[420px] max-h-60 overflow-auto mt-1 bg-white border border-gray-200 rounded-lg divide-y divide-gray-200 shadow-lg">
          {modifierCodes.map(modifierCode => (
            <li
              key={modifierCode.code}
              className="px-4 py-2 text-gray-700 cursor-pointer hover:bg-gray-100"
              onClick={() => handleSelectModifierCode(modifierCode)}
            >
              <div className="flex justify-start gap-4 text-sm">
                <strong>
                  <small>Código:</small>
                </strong>
                {modifierCode.code}
                <strong>
                  <small>Descripción:</small>
                </strong>{' '}
                {modifierCode.description[language]}
              </div>
            </li>
          ))}
        </ul>
      )}

      {isLoading && <p className="mt-2 text-gray-500">Cargando...</p>}

      {searchTerm && !isLoading && modifierCodes?.length === 0 && (
        <p className="mt-2 text-red-400 text-sm text-pretty w-96">
          No se encontraron códigos modificadores.
        </p>
      )}
    </div>
  );
}
