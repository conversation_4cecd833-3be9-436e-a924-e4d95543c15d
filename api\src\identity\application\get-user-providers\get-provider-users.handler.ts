import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';

import { ProviderRepository } from 'src/providers/infrastructure/repositories/provider.repository';
import { UserRepository } from 'src/identity/infrastructure/repositories/user.repository';
import { UserStatus } from 'src/shared/enums/user-status.enum';

import { GetProviderUsersQuery } from './get-provider-users.query';

@QueryHandler(GetProviderUsersQuery)
export class GetProviderUsersHandler
  implements IQueryHandler<GetProviderUsersQuery>
{
  constructor(
    readonly userRepository: UserRepository,
    readonly providerRepository: ProviderRepository,
  ) {}
  async execute() {
    const userProviders = await this.userRepository.getAllProviderUsers();
    return this.mapProviderInfo(userProviders);
  }

  private async mapProviderInfo(data: Record<string, any>[]) {
    const providersId = [...new Set(data.map(user => user.providerId))];
    const provider = await this.providerRepository.getAllByIds(providersId);

    return data.map(user => {
      const providerData = provider.find(
        provider => provider.id === user.providerId,
      );
      return {
        id: user.id,
        name: user.profile.firstName,
        lastName: user.profile.lastName,
        email: user.email,
        provider: providerData.name,
        status: UserStatus.Active,
      };
    });
  }
}
