import { Label } from 'react-aria-components';
import { LuDollarSign } from 'react-icons/lu';

import { YesOrNot } from '@/enums';
import { FormField } from '@/components';

const { Yes, No } = YesOrNot;

interface IItems25_30 {
  disabled: boolean;
  totalCharge: number;
}

export function Items25_30({ disabled, totalCharge }: IItems25_30) {
  return (
    <section
      className="grid grid-cols-3 border border-gray-300 border-y-transparent"
      style={{ gridTemplateColumns: '437px 520px repeat(1, 1fr)' }}
    >
      <aside className="flex gap-4 p-4 hover-card">
        <b>25.</b>
        <div className="grid grid-cols-2 gap-4 w-full">
          <FormField name="federalTaxIdNumber" label="FEDERAL TAX I.D. NUMBER" />
          <div className="max-w-14">
            <FormField
              label="TYPE"
              className="flex"
              name="federalTaxIdNumberType"
              disabled={disabled}
              type="radio"
              options={[
                { value: 'SSN', label: 'SSN' },
                { value: 'EIN', label: 'EIN' },
              ]}
            />
          </div>
        </div>
      </aside>
      <section className="flex gap-4 px-4 border border-gray-300 border-y-transparent  hover-card">
        <b className="mt-2">26. </b>
        <div className="mt-2 grid grid-cols-2 gap-4 w-full">
          <FormField name="patientAccountNumber" label="PATIENT'S ACCOUNT NO." />
          <div className="flex flex-col border border-gray-300 border-y-transparent border-r-transparent">
            <div className="flex justify-center gap-4">
              <b>27. </b>
              <div className="flex justify-center max-w-full">
                <FormField
                  label={
                    <div className="flex justify-center">
                      <Label>
                        ACCEPT ASSIGNMENT?
                        <br />
                        <small>(For govt. claims, see back)</small>
                      </Label>
                    </div>
                  }
                  name="acceptAssignment"
                  disabled={disabled}
                  type="radio"
                  options={[
                    { value: Yes, label: Yes },
                    { value: No, label: No },
                  ]}
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="grid grid-cols-3 border border-gray-300 border-x-transparent border-b-transparent">
        <div className="p-4 hover-card">
          <b>28. </b>
          <Label>TOTAL CHARGE</Label>
          <div className="flex items-end gap-2">
            <div className="mb-2 ml-2">
              <LuDollarSign />
            </div>
            <div className="w-full">
              <FormField name="totalCharge" type="number" value={totalCharge} disabled />
            </div>
          </div>
        </div>
        <div className="p-4 border border-gray-300 border-y-transparent border-b-transparent hover-card">
          <b>29. </b>
          <Label>AMOUNT PAID</Label>
          <div className="flex items-end gap-2">
            <div className="mb-2 ml-2">
              <LuDollarSign />
            </div>
            <div className="w-full">
              <FormField name="amountPaid" type="number" />
            </div>
          </div>
        </div>
        <div className="p-4 hover-card">
          <b>30. </b>
          <Label>Rsvd for NUCC use</Label>
          <div className="flex items-end gap-2">
            <FormField name=" " className="border-none bg-transparent" disabled />
          </div>
        </div>
      </section>
    </section>
  );
}
