import { useField } from 'formik';
import { useState, useEffect } from 'react';
import { Input } from '@digheontech/digh.ui';
import { SearchField } from 'react-aria-components';

import { IPlaceOfService, PLACE_OF_SERVICE_CODES } from './place_of_services_codes';

interface IPlaceOfServiceSearch {
  onSelectPlace?: (code: string) => void;
  initialValue?: string;
  disabled?: boolean;
  name: string;
  className?: string;
}

export function PlaceOfServiceSearch({
  onSelectPlace,
  initialValue = '',
  disabled,
  name,
  className,
}: IPlaceOfServiceSearch) {
  const [field, meta, helpers] = useField(name);

  const [inputValue, setInputValue] = useState(initialValue || field.value || '');
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  const [filteredResults, setFilteredResults] = useState<IPlaceOfService[]>([]);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const valueToUse = field.value || initialValue;

    if (valueToUse) {
      const place = PLACE_OF_SERVICE_CODES.find(p => p.code === valueToUse);

      if (place) {
        setInputValue(place.code);
      } else {
        setInputValue(valueToUse);
      }

      setSearchTerm(valueToUse);
    } else {
      setInputValue('');
      setSearchTerm('');
    }
  }, [initialValue, field.value]);

  useEffect(() => {
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      const results = PLACE_OF_SERVICE_CODES.filter(
        item => item.code.includes(searchTerm) || item.name.toLowerCase().includes(lowerSearchTerm),
      );
      setFilteredResults(results);
    } else {
      setFilteredResults([]);
    }
  }, [searchTerm]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    setShowResults(!!newValue);

    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (newValue) {
      const timeout = setTimeout(() => {
        setSearchTerm(newValue);
      }, 500);
      setSearchTimeout(timeout);
    } else {
      setSearchTerm('');
      setFilteredResults([]);
      helpers?.setValue('');
      helpers?.setTouched(true);
    }
  };

  const handleSelectPlace = (place: IPlaceOfService) => {
    setInputValue(place.code);
    setSearchTerm(place.code);
    setShowResults(false);

    helpers?.setValue(place.code);
    helpers?.setTouched(true);

    if (onSelectPlace) {
      onSelectPlace(place.code);
    }
  };

  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
      // Ensure field is properly reset when component unmounts
      if (!field.value && inputValue === '') {
        helpers?.setValue('');
      }
    };
  }, [searchTimeout, field.value, inputValue, helpers]);

  return (
    <div className={`relative ${className || ''}`}>
      <SearchField aria-label="Place of Service" className="relative flex items-center" {...field}>
        <Input
          value={inputValue}
          onChange={e => handleSearch(e)}
          placeholder="Buscar..."
          className="field-shadow !h-12 w-full"
          disabled={disabled}
        />
      </SearchField>

      {showResults && filteredResults.length > 0 && (
        <ul className="absolute z-10 min-w-max max-h-60 overflow-auto mt-1 bg-white border border-gray-200 rounded-lg divide-y divide-gray-200 shadow-lg">
          {filteredResults.map(place => (
            <li
              key={place.code}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSelectPlace(place)}
            >
              <div className="flex flex-col">
                <small>
                  <strong>Code:</strong> {place.code}
                </small>

                <small>
                  <strong>Name:</strong> {place.name}
                </small>
              </div>
            </li>
          ))}
        </ul>
      )}

      {searchTerm && filteredResults.length === 0 && (
        <p className="mt-2 text-red-400 text-sm text-pretty w-96">
          No se encontraron lugares de servicio.
        </p>
      )}

      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
