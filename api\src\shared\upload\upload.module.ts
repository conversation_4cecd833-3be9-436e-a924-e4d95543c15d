import { DynamicModule, Module } from '@nestjs/common';
import { S3_BUCKET, S3_CLIENT, UPLOAD_OPTIONS } from './upload.constants';
import { UploadModuleAsyncOptions, UploadOptions } from './upload.interface';
import { UploadService } from './upload.service';

@Module({})
export class UploadModule {
  static forRootAsync(options: UploadModuleAsyncOptions): DynamicModule {
    return {
      ...options,
      module: UploadModule,
      providers: [
        UploadService,
        {
          provide: UPLOAD_OPTIONS,
          useFactory: options.useFactory,
          inject: options.inject,
        },
        {
          provide: S3_CLIENT,
          useFactory: (options: UploadOptions) => options.s3.client,
          inject: [UPLOAD_OPTIONS],
        },
        {
          provide: S3_BUCKET,
          useFactory: (options: UploadOptions) => options.s3.bucket,
          inject: [UPLOAD_OPTIONS],
        },
      ],
      exports: [UploadService],
    };
  }
}
