'use client';

import { AppMenu, SidebarHeaderTemplate } from '@/components';
import { useSidebarCollapsed } from '@/hooks/use-sidebar-collapsed';
import { ProviderUsersPage } from '@/modules/users';

export default function ProvidersList() {
  const [collapsed, setCollapsed] = useSidebarCollapsed();

  return (
    <SidebarHeaderTemplate
      sidebar={<AppMenu collapsed={collapsed} setCollapsed={setCollapsed} />}
      header={null}
      collapsed={collapsed}
    >
      <ProviderUsersPage />
    </SidebarHeaderTemplate>
  );
}
