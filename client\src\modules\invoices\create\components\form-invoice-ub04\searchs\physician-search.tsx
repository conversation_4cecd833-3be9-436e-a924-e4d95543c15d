import { SetStateAction, useState, useEffect } from 'react';
import { SearchField } from 'react-aria-components';
import { Input } from '@digheontech/digh.ui';

interface IPhysicianSearch {
  name: string;
  label: string;
  onSelectPhysician?: (physician: any) => void;
  initialValue?: string;
  disabled?: boolean;
}

export function PhysicianSearch({
  name,
  label,
  onSelectPhysician,
  initialValue = '',
  disabled,
}: IPhysicianSearch) {
  const [inputValue, setInputValue] = useState(initialValue);
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  // TODO: Replace with actual physician search hook
  const physicians: any[] = [];
  const isLoading = false;

  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  const handleSearch = (value: SetStateAction<string>) => {
    const newValue = value as string;
    setInputValue(newValue);
    setShowResults(!!newValue);
    setSearchTerm(newValue);
  };

  const handleSelectPhysician = (physician: any) => {
    setInputValue(physician.name);
    setShowResults(false);
    if (onSelectPhysician) {
      onSelectPhysician(physician);
    }
  };

  return (
    <div className="w-full relative">
      <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
      <SearchField
        value={inputValue}
        onChange={handleSearch}
        className="relative flex items-center w-full"
        aria-label={label}
      >
        <Input
          name={name}
          aria-label={label}
          value={inputValue}
          placeholder="Buscar médico..."
          className="field-shadow !h-12"
          disabled={disabled}
        />
      </SearchField>

      {showResults && physicians?.length > 0 && (
        <ul className="absolute z-10 w-full max-h-60 overflow-auto mt-1 bg-white border border-gray-200 rounded-lg divide-y divide-gray-200 shadow-lg">
          {physicians.map((physician: any, index: number) => (
            <li
              key={index}
              className="px-4 py-2 text-gray-700 cursor-pointer hover:bg-gray-100"
              onClick={() => handleSelectPhysician(physician)}
            >
              <div className="flex justify-start gap-4">
                <small>
                  <strong>Nombre:</strong> {physician.name}
                </small>
                <small>
                  <strong>NPI:</strong> {physician.npi}
                </small>
              </div>
            </li>
          ))}
        </ul>
      )}

      {isLoading && <p className="mt-2 text-gray-500">Cargando...</p>}

      {searchTerm && !isLoading && physicians?.length === 0 && (
        <p className="mt-2 text-red-400 text-sm">No se encontraron médicos.</p>
      )}
    </div>
  );
}
