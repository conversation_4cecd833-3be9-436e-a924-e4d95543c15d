import { AxiosResponseError } from '@/config/http';
import { useToast } from './useToast';

export const useShowError = () => {
  const { triggerToast } = useToast();

  const showError = (error: AxiosResponseError | any) => {
    const { response } = error;
    const { data, status: statusCode } = response || {};
    const errorCode = data?.code;

    if (typeof errorCode === 'string') {
      const title = errorCode;

      triggerToast({ title });
    } else if (statusCode) {
      const title = statusCode.toString();

      triggerToast({ title });
      return;
    }

    triggerToast({ title: `Error`, description: error.message || '' });
  };

  return { showError };
};
