'use client';

import { App<PERSON><PERSON>u, HeaderWithButtons, SidebarHeaderTemplate } from '@/components';
import { useSidebarCollapsed } from '@/hooks/use-sidebar-collapsed';
import { InvoicesPage } from '@/modules/invoices/list';

export default function Invoices() {
  const [collapsed, setCollapsed] = useSidebarCollapsed();

  return (
    <SidebarHeaderTemplate
      header={
        <HeaderWithButtons
          back="/providers/dashboard"
          path="/invoices/create"
          textPath="Registrar Factura"
          textBack="Dashboard"
        />
      }
      sidebar={<AppMenu collapsed={collapsed} setCollapsed={setCollapsed} />}
      collapsed={collapsed}
    >
      <InvoicesPage />
    </SidebarHeaderTemplate>
  );
}
