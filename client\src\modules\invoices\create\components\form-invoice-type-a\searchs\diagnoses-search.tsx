import { SetStateAction, useState, useEffect } from 'react';
import { SearchField } from 'react-aria-components';
import { Input } from '@digheontech/digh.ui';

import { IDiagnosis } from '@/interfaces';
import { useGetDiagnoses } from '../../../hooks/use-get-diagnoses';

interface IDiagnosesSearch {
  onSelectDiagnosis: (code: string) => void;
  initialValue: string;
  language?: 'es' | 'en';
  disabled?: boolean;
}

export function DiagnosesSearch({
  onSelectDiagnosis,
  initialValue,
  language = 'es',
  disabled,
}: IDiagnosesSearch) {
  const [inputValue, setInputValue] = useState(initialValue || '');
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);
  const { diagnoses = [], isLoading } = useGetDiagnoses(searchTerm, language);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setInputValue(initialValue || '');
    setSearchTerm(initialValue || '');
  }, [initialValue]);

  const handleSearch = (value: SetStateAction<string>) => {
    const newValue = value as string;
    setInputValue(newValue);
    setShowResults(!!newValue);

    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    if (newValue) {
      const timeout = setTimeout(() => {
        setSearchTerm(newValue);
      }, 500);
      setSearchTimeout(timeout);
    } else {
      setSearchTerm('');
    }
  };

  const handleSelectDiagnosis = (diagnosis: IDiagnosis) => {
    setInputValue(diagnosis.description[language]);
    setSearchTerm(diagnosis.description[language]);
    setShowResults(false);
    onSelectDiagnosis(diagnosis.code);
  };

  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  return (
    <div className="w-full max-w-md relative">
      <SearchField
        value={inputValue}
        onChange={handleSearch}
        className="relative flex items-center w-full mt-2"
        aria-label="Diagnosis"
      >
        <Input
          aria-label="Diagnosis"
          value={inputValue}
          placeholder="Buscar..."
          className="field-shadow !h-12"
          disabled={disabled}
        />
      </SearchField>

      {showResults && diagnoses?.length > 0 && (
        <ul className="absolute z-10 min-w-max max-h-60 overflow-auto mt-1 bg-white border border-gray-200 rounded-lg divide-y divide-gray-200 shadow-lg">
          {diagnoses.map((diagnosis: IDiagnosis) => (
            <li
              key={diagnosis.code}
              className="px-4 py-2 text-gray-700 cursor-pointer hover:bg-gray-100"
              onClick={() => handleSelectDiagnosis(diagnosis)}
            >
              <div className="flex justify-start gap-4">
                <small>
                  <strong>Código:</strong> {diagnosis.code}
                </small>
                <small>
                  <strong>Descripción:</strong> {diagnosis.description[language]}
                </small>
              </div>
            </li>
          ))}
        </ul>
      )}

      {isLoading && <p className="mt-2 text-gray-500">Cargando...</p>}

      {searchTerm && !isLoading && diagnoses?.length === 0 && (
        <p className="mt-2 text-red-400 text-sm text-pretty w-96">
          No se encontraron diagnósticos.
        </p>
      )}
    </div>
  );
}
