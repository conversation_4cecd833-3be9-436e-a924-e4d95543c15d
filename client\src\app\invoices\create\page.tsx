'use client';

import { App<PERSON><PERSON>u, HeaderWithButtons, SidebarHeaderTemplate } from '@/components';
import { useSidebarCollapsed } from '@/hooks/use-sidebar-collapsed';
import { InvoicesCreatePage } from '@/modules/invoices/create';

export default function InvoicesRegister() {
  const [collapsed, setCollapsed] = useSidebarCollapsed();

  return (
    <SidebarHeaderTemplate
      header={<HeaderWithButtons back="/invoices" textBack="Registrar Facturas" />}
      sidebar={<AppMenu collapsed={collapsed} setCollapsed={setCollapsed} />}
      collapsed={collapsed}
    >
      <InvoicesCreatePage />
    </SidebarHeaderTemplate>
  );
}
