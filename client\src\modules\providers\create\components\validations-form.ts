import * as Yup from 'yup';

import { ProvidersType } from '@/enums';
import { IFormProvidersValues } from '@/interfaces';

export const fieldsProvider = [
  { name: 'providerName', label: 'Nombre', placeholder: 'Nombre del proveedor' },
  { name: 'npi', label: 'Unique ID (NPI)', placeholder: 'ID único' },
];
export const fieldsUser = [
  { name: 'userName', label: 'Nombre', placeholder: 'Nombre del usuario' },
  { name: 'userLastName', label: 'Apellido', placeholder: 'Apellido del usuario' },
  { name: 'email', label: 'Correo <PERSON>o', placeholder: 'Email del usuario' },
  { name: 'password', label: 'Contraseña', type: 'password', placeholder: 'Su contraseña' },
];

export const initialValues: IFormProvidersValues = {
  providerName: '',
  npi: '',
  type: undefined,
  userName: '',
  userLastName: '',
  email: '',
  password: '',
};

export const validationSchema = Yup.object().shape({
  providerName: Yup.string()
    .trim()
    .required('El nombre del proveedor es requerido')
    .min(2, 'El nombre debe tener al menos 2 caracteres'),

  npi: Yup.string()
    .trim()
    .required('El NPI es requerido')
    .matches(/^\d{10}$/, 'El NPI debe ser exactamente 10 dígitos sin espacios ni símbolos'),

  type: Yup.mixed<ProvidersType>()
    .oneOf(Object.values(ProvidersType), 'El tipo seleccionado no es válido')
    .required('El tipo de proveedor es requerido'),

  userName: Yup.string()
    .trim()
    .required('El nombre del usuario es requerido')
    .min(2, 'El nombre debe tener al menos 2 caracteres'),

  userLastName: Yup.string()
    .trim()
    .required('El apellido del usuario es requerido')
    .min(2, 'El nombre debe tener al menos 2 caracteres'),

  email: Yup.string()
    .trim()
    .required('El correo electrónico es requerido')
    .email('El formato del correo electrónico no es válido'),

  password: Yup.string()
    .required('La contraseña es requerida')
    .min(6, 'La contraseña debe tener al menos 6 caracteres'),
});
