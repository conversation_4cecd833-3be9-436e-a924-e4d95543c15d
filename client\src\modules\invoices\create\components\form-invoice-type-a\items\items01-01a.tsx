import { FormField } from '@/components';
import { InsuranceCarrier } from '@/enums';

const { CHAMPVA, FECA, GROUP_HEALTH_PLAN, MEDICAID, MEDICARE, OTHER, TRICARE } = InsuranceCarrier;

export function Items01_01a({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="flex justify-between border border-gray-300 border-b-transparent ">
      <div className="flex items-center gap-4 w-full p-4 hover-card">
        <strong>1. </strong>
        <div className="w-full bg-gray-50 rounded-lg p-4">
          <FormField
            disabled
            type="radio"
            name="insuranceCarrier"
            options={[
              { value: MEDICARE, label: 'Medicare', sublabel: '(Medicare #)' },
              { value: MEDICAID, label: 'Medicaid', sublabel: '(Medicaid #)' },
              { value: TRICARE, label: 'Tricare', sublabel: "(<PERSON>ponso<PERSON>'s SSN)" },
              { value: CHAMPVA, label: 'Champva', sublabel: '(Member ID#)' },
              {
                value: GROUP_HEALTH_PLAN,
                label: 'Group Health Plan',
                sublabel: '(SSN or ID)',
              },
              { value: FECA, label: 'Feca Black Lung', sublabel: '(SSN)' },
              { value: OTHER, label: 'Other', sublabel: '(ID)' },
            ]}
          />
        </div>
      </div>
      <div className="flex gap-4 w-1/2 justify-between border border-x-transparent border-y-transparent border-l-gray-300 border-gray-300 p-4 hover-card">
        <div className="flex gap-4 w-80">
          <strong>1a.</strong>
          <FormField
            disabled={isLoading}
            name="insuranceIdNumber"
            label="INSURED'S I.D. NUMBER"
            className="w-full"
            required={false}
          />
        </div>
        <small>(For Program in Item 1)</small>
      </div>
    </section>
  );
}
