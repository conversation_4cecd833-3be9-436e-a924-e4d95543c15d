'use client';

import { Di<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON><PERSON>, Dialog } from 'react-aria-components';
import { ReactNode } from 'react';
import { RiCloseCircleLine } from 'react-icons/ri';

interface IBaseModal {
  isOpen: boolean;
  onClose: () => void;
  trigger: ReactNode;
  children: ReactNode;
  title?: string;
  className?: HTMLElement['className'];
}

export function BaseModal({ isOpen, onClose, trigger, children, title, className }: IBaseModal) {
  return (
    <DialogTrigger>
      {trigger}
      {isOpen && (
        <ModalOverlay
          className={({ isEntering, isExiting }) => `
            fixed inset-0 z-10 overflow-y-auto bg-black/25 flex min-h-full items-center justify-center p-4 text-center backdrop-blur
            ${isEntering ? 'animate-in fade-in duration-300 ease-out' : ''}
            ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}
          `}
        >
          <Modal
            className={({ isEntering, isExiting }) => `
              w-full max-w-md overflow-hidden rounded-2xl bg-slate-50 pt-7 pb-10 px-14 text-left align-middle shadow-xl relative
              ${isEntering ? 'animate-in zoom-in-95 ease-out duration-300' : ''}
              ${isExiting ? 'animate-out zoom-out-95 ease-in duration-200' : ''}
              ${className}
            `}
          >
            <Dialog role="dialog" className="outline-none relative">
              <div className="flex justify-between mb-4">
                <div>
                  <span className="w-max text-sm font-bold text-primary-400 uppercase">
                    {title}
                  </span>
                </div>
                <RiCloseCircleLine
                  className="text-slate-800 text-2xl cursor-pointer hover:bg-red-200 rounded-md"
                  onClick={onClose}
                />
              </div>
              {children}
            </Dialog>
          </Modal>
        </ModalOverlay>
      )}
    </DialogTrigger>
  );
}
