'use client';

import { UserRole } from '@/enums';
import useLoggedUser from '@/hooks/use-logged-user';
import { MenuLink } from './menu-link';
import { LiaFileInvoiceDollarSolid } from 'react-icons/lia';
import { RxDashboard } from 'react-icons/rx';

export function ItemsAdjudicator({ collapsed }: { collapsed: boolean }) {
  const { Adjudicator } = UserRole;
  const { loggedUser } = useLoggedUser();

  if (loggedUser?.role !== Adjudicator) {
    return null;
  }

  return (
    <nav className="flex flex-col h-full py-6 px-4 gap-6 items-center w-full">
      <ul className="flex flex-col gap-4">
        <li className="mt-auto w-full">
          <MenuLink
            href="/adjudicator/dashboard"
            label="Dashboard"
            icon={<RxDashboard />}
            collapsed={collapsed}
          />
        </li>
        <li>
          <MenuLink
            href="/adjudicator/list"
            label="Reclamaciones"
            icon={<LiaFileInvoiceDollarSolid />}
            collapsed={collapsed}
          />
        </li>
      </ul>
    </nav>
  );
}
