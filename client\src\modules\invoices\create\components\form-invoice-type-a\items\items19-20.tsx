'use client';

import { FormField } from '@/components';

import { YesOrNot } from '@/enums';

interface IItems19_20 {
  disabled: boolean;
}

const { Yes, No } = YesOrNot;

export function IItems19_20({ disabled }: IItems19_20) {
  return (
    <>
      <section className="grid grid-cols-2 items-center border border-gray-300 p-4 border-t-transparent border-r-transparent gap-4 hover-card">
        <div className="flex gap-4">
          <strong>19.</strong>
          <FormField
            disabled
            name=" "
            label="ADDITIONAL CLAIM INFORMATION (Designated by NUCC)"
            className="border-none bg-transparent"
          />
        </div>
      </section>
      <section className="grid grid-cols-1 items-center border border-gray-300 p-4 border-t-transparent gap-4 hover-card">
        <div className="flex gap-4">
          <strong>20.</strong>
          <div className="flex justify-start gap-16 w-full">
            <div className="min-w-64">
              <FormField
                label="OUTSIDE LAB?"
                name="isOutsideLab"
                disabled={disabled}
                type="radio"
                options={[
                  { value: Yes, label: Yes },
                  { value: No, label: No },
                ]}
              />
            </div>

            <FormField
              label="$ CHARGES"
              name="outsideLabCharges"
              type="number"
              disabled={disabled}
            />
          </div>
        </div>
      </section>
    </>
  );
}
