import { IFieldCMS, ICMSCheckbox } from '../interfaces';

export const CMSCheckbox = ({ checked }: ICMSCheckbox) => (
  <span className="inline-block align-middle w-3 h-3 border border-gray-700 mr-1 bg-white">
    {checked && <span className="block w-full h-full bg-sky-600" />}
  </span>
);

export const FieldCMS = ({
  isVerified,
  label,
  value,
  className = '',
  labelClass = '',
  valueClass = '',
  children,
}: IFieldCMS) => (
  <div
    className={`flex flex-col rounded p-2 min-h-[42px] ${className} ${
      isVerified ? 'border border-yellow-600 bg-yellow-200' : 'bg-white'
    }`}
  >
    <span className={`text-[14px] text-primary-500 font-bold leading-none ${labelClass}`}>
      {label}
    </span>
    <span className={`text-[13px] font-medium text-gray-900 mt-1 truncate ${valueClass}`}>
      {value}
    </span>
    {children}
  </div>
);
