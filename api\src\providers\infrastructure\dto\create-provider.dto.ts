import {
  IsE<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  IsString,
  Matches,
} from 'class-validator';
import { User } from 'src/identity/infrastructure/schemas/user.schema';
import { ProviderType } from 'src/shared/enums/provider-type.enum';
import { FieldExists } from 'src/shared/validation';
import { Provider } from '../schemas/provider.schema';

export class CreateProviderDto {
  @IsString()
  @IsNotEmpty()
  providerName: string;

  @Matches(/^\d{10}$/, {
    message: 'NPI must be exactly 10 digits with no spaces or symbols.',
  })
  @FieldExists({ repositoryName: Provider.name, fieldName: 'npi' })
  npi: string;

  @IsEnum(ProviderType)
  type: string;

  @IsNotEmpty()
  @IsString()
  userName: string;

  @IsNotEmpty()
  @IsString()
  userLastName: string;

  @IsNotEmpty()
  @IsEmail()
  @FieldExists({ repositoryName: User.name, fieldName: 'email' })
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}
