'use client';

import { AppMenu, SidebarHeaderTemplate } from '@/components';
import { DashboardPage } from '@/modules/admin/dashboard';
import { useSidebarCollapsed } from '@/hooks/use-sidebar-collapsed';

export default function AdminDashboard() {
  const [collapsed, setCollapsed] = useSidebarCollapsed();

  return (
    <SidebarHeaderTemplate
      sidebar={<AppMenu collapsed={collapsed} setCollapsed={setCollapsed} />}
      header={null}
      collapsed={collapsed}
    >
      <DashboardPage />
    </SidebarHeaderTemplate>
  );
}
