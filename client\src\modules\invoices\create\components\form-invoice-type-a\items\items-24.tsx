import { useMemo, useState, useEffect } from 'react';
import { But<PERSON> } from '@digheontech/digh.ui';
import { Label } from 'react-aria-components';
import { BiPlusCircle, BiMinusCircle } from 'react-icons/bi';

import { ItemEPSDT, YesOrNot } from '@/enums';
import { FieldDatePicker, FormField } from '@/components';

import { PlaceOfServiceSearch } from '../searchs/place-of-service-search';
import { SelectItem24H } from '../selects/select-item-24-h';
import { CPTSearch } from '../searchs/cpt-search';
import { ModifierCodeSearch } from '../searchs/modifier-code-search';

const { Yes, No } = YesOrNot;

const INITIAL_ROWS = 1;
const MAX_ROWS = 6;

interface IItem24 {
  disabled: boolean;
  initialRows?: number;
  maxRows?: number;
  length: number;
  supplementalInfo: any[];
  onSupplementalInfoChange: (newSupplementalInfo: any[]) => void;
  formikProps: any;
}

interface RowData {
  id: string;
  index: number;
}

const createEmptySupplementalItem = () => ({
  fromDateOfService: null,
  toDateOfService: null,
  placeOfService: '',
  emergencyIndicator: No,
  proceduresCode: '',
  proceduresModifier: ['', '', '', ''],
  diagnosisPointer: '',
  charges: undefined,
  daysOrUnits: undefined,
  epsdtFamilyPlan: ItemEPSDT.NotUsed,
  idQualifier: '',
  renderingProviderId: '',
});

export function Item24({
  disabled,
  initialRows = INITIAL_ROWS,
  maxRows = MAX_ROWS,
  length,
  supplementalInfo,
  onSupplementalInfoChange,
  formikProps,
}: IItem24) {
  const [rows, setRows] = useState<RowData[]>(() =>
    Array.from({ length: length || initialRows }, (_, i) => ({
      id: `row-${i}-${Date.now()}`,
      index: i,
    })),
  );

  const ROWS_LENGTH = useMemo(() => rows.length, [rows]);

  useEffect(() => {
    const currentLength = supplementalInfo.length;

    if (ROWS_LENGTH > currentLength) {
      const newItems = Array(ROWS_LENGTH - currentLength)
        .fill(null)
        .map(() => createEmptySupplementalItem());

      const updatedSupplementalInfo = [...supplementalInfo, ...newItems];
      onSupplementalInfoChange(updatedSupplementalInfo);

      if (formikProps && formikProps.setFieldValue) {
        formikProps.setFieldValue('supplementalInformation', updatedSupplementalInfo);
      }
    } else if (ROWS_LENGTH < currentLength) {
      const updatedSupplementalInfo = supplementalInfo.slice(0, ROWS_LENGTH);
      onSupplementalInfoChange(updatedSupplementalInfo);

      if (formikProps && formikProps.setFieldValue) {
        formikProps.setFieldValue('supplementalInformation', updatedSupplementalInfo);
      }
    }

    const updatedInfo = supplementalInfo.map(item => ({
      ...item,
      emergencyIndicator: item.emergencyIndicator || No,
    }));

    if (JSON.stringify(updatedInfo) !== JSON.stringify(supplementalInfo)) {
      onSupplementalInfoChange(updatedInfo);
      if (formikProps && formikProps.setFieldValue) {
        formikProps.setFieldValue('supplementalInformation', updatedInfo);
      }
    }
  }, [ROWS_LENGTH, supplementalInfo, formikProps, onSupplementalInfoChange]);

  const addRow = () => {
    if (ROWS_LENGTH >= maxRows) return;

    const newRow = {
      id: `row-${ROWS_LENGTH}-${Date.now()}`,
      index: ROWS_LENGTH,
    };

    setRows([...rows, newRow]);

    const newEmptyItem = createEmptySupplementalItem();
    const newSupplementalInfo = [...supplementalInfo, newEmptyItem];
    onSupplementalInfoChange(newSupplementalInfo);

    if (formikProps && formikProps.setFieldValue) {
      formikProps.setFieldValue('supplementalInformation', newSupplementalInfo);
    }
  };

  const removeLastRow = () => {
    if (ROWS_LENGTH <= 1) return;

    setRows(rows.slice(0, -1));

    const newSupplementalInfo = supplementalInfo.slice(0, -1);
    onSupplementalInfoChange(newSupplementalInfo);

    if (formikProps && formikProps.setFieldValue) {
      formikProps.setFieldValue('supplementalInformation', newSupplementalInfo);
    }
  };

  const handleFieldChange = (index: number, field: string, value: any) => {
    const newSupplementalInfo = [...supplementalInfo];

    if (!newSupplementalInfo[index]) {
      newSupplementalInfo[index] = createEmptySupplementalItem();
    }

    if (field === 'emergencyIndicator') {
      newSupplementalInfo[index][field] = value;

      if (formikProps && formikProps.setFieldValue) {
        const fieldPath = `supplementalInformation[${index}].emergencyIndicator`;
        formikProps.setFieldValue(fieldPath, value);
      }
    } else if (field === 'epsdtFamilyPlan') {
      newSupplementalInfo[index][field] = value || ItemEPSDT.NotUsed;

      if (formikProps && formikProps.setFieldValue) {
        const fieldPath = `supplementalInformation[${index}].epsdtFamilyPlan`;
        formikProps.setFieldValue(fieldPath, value || ItemEPSDT.NotUsed);
      }
    } else if (field.includes('[') && field.includes(']')) {
      const [mainField, subFieldIndex] = field.split(/[\[\]]/);

      if (!Array.isArray(newSupplementalInfo[index][mainField])) {
        newSupplementalInfo[index][mainField] = ['', '', '', ''];
      }

      newSupplementalInfo[index][mainField][parseInt(subFieldIndex)] = value || '';

      if (formikProps && formikProps.setFieldValue) {
        const fieldPath = `supplementalInformation[${index}].${mainField}`;
        formikProps.setFieldValue(fieldPath, newSupplementalInfo[index][mainField]);
      }
    } else {
      if (field === 'charges' || field === 'daysOrUnits') {
        newSupplementalInfo[index][field] =
          value === '' || value === undefined ? undefined : Number(value);
      } else {
        newSupplementalInfo[index][field] = value === undefined ? '' : value;
      }

      if (formikProps && formikProps.setFieldValue) {
        const fieldPath = `supplementalInformation[${index}].${field}`;
        let formikValue = newSupplementalInfo[index][field];
        formikProps.setFieldValue(fieldPath, formikValue);
      }
    }

    onSupplementalInfoChange(newSupplementalInfo);
  };

  useEffect(() => {
    const ensureInitializedData = () => {
      if (supplementalInfo.length < rows.length) {
        const updatedInfo = [...supplementalInfo];

        for (let i = supplementalInfo.length; i < rows.length; i++) {
          updatedInfo[i] = createEmptySupplementalItem();
        }

        onSupplementalInfoChange(updatedInfo);

        if (formikProps && formikProps.setFieldValue) {
          formikProps.setFieldValue('supplementalInformation', updatedInfo);
        }
      }
    };

    ensureInitializedData();
  }, [rows, supplementalInfo, formikProps, onSupplementalInfoChange]);

  return (
    <div className="relative border border-b-0 border-gray-300">
      {/* Header */}
      <div className="w-full bg-gray-100 border border-gray-300 p-2">
        <div className="flex justify-between items-baseline">
          <strong className="text-sm text-primary-500 font-bold">
            <small className="text-red-500">*</small> 24. SERVICE LINES ({ROWS_LENGTH}/24. SERVICE
            LINES ({ROWS_LENGTH}/{maxRows})
          </strong>
          <div className="flex gap-2">
            {rows.length <= 1 ? null : (
              <Button
                buttonType="normal"
                className="p-2 border-none rounded-full hover:bg-primary-200 focus:outline-none"
                onPress={removeLastRow}
                aria-label="Remove last row"
                leftIcon={<BiMinusCircle size={18} />}
              />
            )}
            {rows.length >= maxRows ? null : (
              <Button
                buttonType="normal"
                className="p-2 border-none rounded-full hover:bg-primary-200 focus:outline-none"
                onPress={addRow}
                aria-label="Add row"
                leftIcon={<BiPlusCircle size={18} />}
              />
            )}
          </div>
        </div>
      </div>

      {/* Column Headers */}
      <div
        className="grid grid-cols-12 bg-gray-50 border-x border-gray-300 text-xs font-semibold"
        style={{ gridTemplateColumns: '136px 136px 84px 80px 320px repeat(7, 1fr)' }}
      >
        <div className="col-span-2 p-2 border-r border-gray-300 text-center">
          A. DATE(S) OF SERVICE
          <div className="grid grid-cols-2 gap-2 mt-1">
            <Label className="text-xs">From</Label>
            <Label className="text-xs">To</Label>
          </div>
        </div>
        <div className="!min-w-24 w-fit p-2 border-r border-gray-300 text-center">
          B. PLACE OF SERVICE
        </div>
        <div className="col-span-1 p-2 border-r border-gray-300 text-center">C. EMG</div>
        <div className="col-span-2 p-2 border-r border-gray-300 text-center">
          D. PROCEDURES, SERVICES, OR SUPPLIES
          <div className="grid grid-cols-2 gap-2 mt-1">
            <Label className="text-xs">CPT / HCPCS</Label>
            <Label className="text-xs">Modifier</Label>
          </div>
        </div>
        <div className="col-span-1 p-2 border-r border-gray-300 text-center">
          E. DIAGNOSIS POINTER
        </div>
        <div className="col-span-1 p-2 border-r border-gray-300 text-center">F. $ CHARGES</div>
        <div className="col-span-1 p-2 border-r border-gray-300 text-center">G. DAYS OR UNITS</div>
        <div className="col-span-1 p-2 border-r border-gray-300 text-center">
          H. EPSDT Family Plan
        </div>
        <div className="col-span-1 p-2 border-r border-gray-300 text-center">I. ID. QUAL.</div>
        <div className="col-span-1 p-2">J. RENDERING PROVIDER ID. #</div>
      </div>

      {/* Rows */}
      {rows.map(row => {
        const rowData = supplementalInfo[row.index] || createEmptySupplementalItem();

        return (
          <div
            key={row.id}
            className="grid grid-cols-12 bg-gray-50 border-x border-gray-300 text-xs font-semibold "
            style={{ gridTemplateColumns: '136px 136px 84px 80px 320px repeat(7, 1fr)' }}
          >
            {/* A. DATE(S) OF SERVICE */}
            <div className="col-span-2 ">
              <div className="hover-card grid grid-cols-2 gap-2 border border-gray-300 justify-center items-center border-b-transparent p-2">
                <FieldDatePicker
                  name={`supplementalInformation[${row.index}].fromDateOfService`}
                  value={rowData.fromDateOfService}
                  disabled={disabled}
                  className="w-full text-sm"
                  onChange={value => {
                    handleFieldChange(row.index, 'fromDateOfService', value);
                  }}
                  aria-label="From Date of Service"
                />
                <FieldDatePicker
                  name={`supplementalInformation[${row.index}].toDateOfService`}
                  value={rowData.toDateOfService}
                  disabled={disabled}
                  className="w-full text-sm"
                  onChange={value => {
                    handleFieldChange(row.index, 'toDateOfService', value);
                  }}
                  aria-label="To Date of Service"
                />
              </div>
            </div>
            {/* B. PLACE OF SERVICE */}
            <div className="hover-card !min-w-24 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
              <PlaceOfServiceSearch
                initialValue={rowData.placeOfService || ''}
                onSelectPlace={code => {
                  handleFieldChange(row.index, 'placeOfService', code);
                }}
                disabled={disabled}
                name={`supplementalInformation[${row.index}].placeOfService`}
              />
            </div>
            {/* C. EMG */}
            <div className="hover-card border border-gray-300 flex justify-center items-center border-b-transparent px-2">
              <div className="flex flex-col justify-center items-center gap-2">
                <div className="flex flex-col justify-between gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name={`supplementalInformation[${row.index}].emergencyIndicator`}
                      value={Yes}
                      checked={
                        rowData.emergencyIndicator === Yes || rowData.emergencyIndicator === 'YES'
                      }
                      onChange={() => {
                        const newSupplementalInfo = [...supplementalInfo];
                        if (!newSupplementalInfo[row.index]) {
                          newSupplementalInfo[row.index] = createEmptySupplementalItem();
                        }
                        newSupplementalInfo[row.index].emergencyIndicator = Yes;
                        onSupplementalInfoChange(newSupplementalInfo);

                        if (formikProps && formikProps.setFieldValue) {
                          formikProps.setFieldValue(
                            `supplementalInformation[${row.index}].emergencyIndicator`,
                            Yes,
                          );
                        }
                      }}
                      disabled={disabled}
                      className="mr-2 flex"
                    />
                    {Yes}
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name={`supplementalInformation[${row.index}].emergencyIndicator`}
                      value={No}
                      checked={
                        rowData.emergencyIndicator === No ||
                        rowData.emergencyIndicator === 'NO' ||
                        !rowData.emergencyIndicator
                      }
                      onChange={() => {
                        const newSupplementalInfo = [...supplementalInfo];
                        if (!newSupplementalInfo[row.index]) {
                          newSupplementalInfo[row.index] = createEmptySupplementalItem();
                        }
                        newSupplementalInfo[row.index].emergencyIndicator = No;
                        onSupplementalInfoChange(newSupplementalInfo);

                        if (formikProps && formikProps.setFieldValue) {
                          formikProps.setFieldValue(
                            `supplementalInformation[${row.index}].emergencyIndicator`,
                            No,
                          );
                        }
                      }}
                      disabled={disabled}
                      className="mr-2 flex"
                    />
                    {No}
                  </label>
                </div>
              </div>
            </div>
            {/* D. PROCEDURES, SERVICES, OR SUPPLIES */}
            <div className="hover-card col-span-2 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
              <div className="flex justify-around gap-2">
                <CPTSearch
                  language="en"
                  initialValue={rowData.proceduresCode || ''}
                  onSelectCode={code => {
                    handleFieldChange(row.index, 'proceduresCode', code);
                  }}
                  disabled={disabled}
                  name={`supplementalInformation[${row.index}].proceduresCode`}
                />
                <div className="grid grid-cols-4 gap-2">
                  {[0, 1, 2, 3].map(modIndex => (
                    <ModifierCodeSearch
                      key={`modifier-${row.index}-${modIndex}`}
                      initialValue={
                        (rowData.proceduresModifier && rowData.proceduresModifier[modIndex]) || ''
                      }
                      onSelectModifierCode={code =>
                        handleFieldChange(row.index, `proceduresModifier[${modIndex}]`, code)
                      }
                      disabled={disabled}
                      language="en"
                    />
                  ))}
                </div>
              </div>
            </div>
            {/* E. DIAGNOSIS POINTER */}
            <div className="hover-card col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
              <FormField
                name={`supplementalInformation[${row.index}].diagnosisPointer`}
                value={rowData.diagnosisPointer || ''}
                disabled={disabled}
                className="w-full text-sm p-0"
                maxLength={4}
                onChange={e => handleFieldChange(row.index, 'diagnosisPointer', e.target.value)}
                aria-label="Diagnosis Pointer"
                viewLabel={false}
              />
            </div>
            {/* F. $ CHARGES */}
            <div className="hover-card col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
              <FormField
                name={`supplementalInformation[${row.index}].charges`}
                value={rowData.charges || undefined}
                type="number"
                className="w-full text-sm"
                disabled={disabled}
                onChange={e => handleFieldChange(row.index, 'charges', e.target.value)}
                aria-label="$ Charges"
                viewLabel={false}
              />
            </div>
            {/* G. DAYS OR UNITS */}
            <div className="hover-card col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
              <FormField
                name={`supplementalInformation[${row.index}].daysOrUnits`}
                value={rowData.daysOrUnits || ''}
                type="number"
                className="w-full text-sm"
                disabled={disabled}
                onChange={e => handleFieldChange(row.index, 'daysOrUnits', e.target.value)}
                aria-label="Days or Units"
                viewLabel={false}
              />
            </div>
            {/* H. EPSDT/Family Plan */}
            <div className="hover-card col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
              <SelectItem24H
                disabled={disabled}
                className="w-full text-sm flex justify-center items-center"
                value={(rowData.epsdtFamilyPlan as ItemEPSDT) || ''}
                name={`supplementalInformation[${row.index}].epsdtFamilyPlan`}
                onChange={(value: ItemEPSDT) =>
                  handleFieldChange(row.index, 'epsdtFamilyPlan', value)
                }
              />
            </div>
            {/* I. ID QUAL */}
            <div className="hover-card col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
              <FormField
                name={`supplementalInformation[${row.index}].idQualifier`}
                value={rowData.idQualifier || ''}
                disabled={disabled}
                className="w-full text-sm"
                onChange={e => handleFieldChange(row.index, 'idQualifier', e.target.value)}
                aria-label="ID Qualifier"
                viewLabel={false}
              />
            </div>
            {/* J. RENDERING PROVIDER ID. # */}
            <div className="hover-card col-span-1 border border-gray-300 flex justify-center items-center border-b-transparent px-2">
              <FormField
                name={`supplementalInformation[${row.index}].renderingProviderId`}
                value={rowData.renderingProviderId || ''}
                disabled={disabled}
                className="w-full text-sm"
                onChange={e => handleFieldChange(row.index, 'renderingProviderId', e.target.value)}
                aria-label="Rendering Provider ID"
                viewLabel={false}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
}
