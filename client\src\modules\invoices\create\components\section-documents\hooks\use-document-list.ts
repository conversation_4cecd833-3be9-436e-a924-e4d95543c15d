import { useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosResponseError, Http } from '@/config/http';

export interface IDocument {
  _id: string;
  invoiceId: string;
  url: string;
  originalName: string;
  size: number;
  fileType: string;
  createdAt: string;
  updatedAt: string;
  documentType: string;
}

const key = 'DOCUMENT_LIST';

export function useDocumentList(invoiceId: string) {
  const queryClient = useQueryClient();

  const { data: documents = [], ...rest } = useQuery<IDocument[], AxiosResponseError>({
    queryKey: [key, invoiceId],
    queryFn: () => Http.get(`invoices/${invoiceId}/files`).then(({ data }) => data),
  });

  const invalidateFiles = () => queryClient.invalidateQueries({ queryKey: [key, invoiceId] });

  return { invalidateFiles, documents, ...rest };
}
