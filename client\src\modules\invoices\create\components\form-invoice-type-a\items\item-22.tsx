import { Key } from 'react';
import { useField } from 'formik';
import { LuChevronsUpDown } from 'react-icons/lu';
import { Button, ListBox, Popover, Select, SelectValue } from 'react-aria-components';

import { Item22ResubmissionCode } from '@/enums';
import { getItem22ResubmissionCodeLabel } from '@/tools';
import { StatusItem } from '@/modules/providers/create/components/provider-select-type/status-item';

const RESUBMISSION_CODES = [
  Item22ResubmissionCode.ReplacementOfPriorClaim,
  Item22ResubmissionCode.VoidCancelOfPriorClaim,
] as const;

interface IResubmissionOption {
  code: Item22ResubmissionCode;
}

interface IResubmissionSelectItem22 {
  className?: HTMLElement['className'];
  disabled?: boolean;
}

const ResubmissionOption = ({ code }: IResubmissionOption) => (
  <StatusItem key={code} id={code}>
    <div className="flex gap-2 items-baseline">
      <span>{code}</span> <small>{getItem22ResubmissionCodeLabel(code)}</small>
    </div>
  </StatusItem>
);

export function ResubmissionSelectItem22({ className, disabled }: IResubmissionSelectItem22) {
  const [field, meta, helpers] = useField<Item22ResubmissionCode>('resubmissionCode');

  const handleSelectionChange = (key: Key) => {
    helpers.setValue(key as Item22ResubmissionCode);
  };

  return (
    <div className={className}>
      <Select
        className="flex flex-col gap-1 w-full mb-4"
        selectedKey={field.value}
        onSelectionChange={handleSelectionChange}
        defaultSelectedKey={undefined}
        placeholder="select"
        isDisabled={disabled}
        aria-label="Resubmission Code"
      >
        <Button className="field-shadow h-4">
          <SelectValue className="flex-1 truncate placeholder-shown:italic">
            {value => getItem22ResubmissionCodeLabel(value as unknown as Item22ResubmissionCode)}
          </SelectValue>
          <LuChevronsUpDown />
        </Button>
        <Popover className="max-h-60 w-[--trigger-width] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out">
          <ListBox className="outline-none p-1">
            {RESUBMISSION_CODES.map(code => (
              <ResubmissionOption key={code} code={code} />
            ))}
          </ListBox>
        </Popover>
      </Select>
      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
