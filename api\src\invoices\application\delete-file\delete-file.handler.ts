import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { AwsS3Service } from 'src/file-management/infrastructure/aws-s3.service';
import { FOLDER_INVOICE } from '../../domain/invoice.enum';
import { InvoiceFileRepository } from '../../infrastructure/repositories/invoice-file.repository';
import { InvoiceRepository } from '../../infrastructure/repositories/invoice.repository';
import { DeleteFileCommand } from './delete-file.command';

@CommandHandler(DeleteFileCommand)
export class DeleteFileHandler implements ICommandHandler<DeleteFileCommand> {
  constructor(
    private readonly invoiceFileRepository: InvoiceFileRepository,
    private readonly invoiceRepository: InvoiceRepository,
    private readonly fileService: AwsS3Service,
  ) {}

  async execute(command: DeleteFileCommand) {
    const file = await this.invoiceFileRepository.findByIdAndInvoiceId(
      command.fileId,
      command.invoiceId,
      true,
    );

    const filePath = `${FOLDER_INVOICE}/${file.fileName}`;

    await this.fileService.remove(filePath);
    await this.invoiceFileRepository.deleteById(command.fileId);
    await this.invoiceRepository.update(command.invoiceId, {
      $inc: { fileCount: -1 },
    });
  }
}
