import { v4 as uuid } from 'uuid';
import { Inject, Injectable } from '@nestjs/common';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Upload } from '@aws-sdk/lib-storage';
import {
  CompleteMultipartUploadCommandOutput,
  GetObjectCommand,
  HeadObjectCommand,
  HeadObjectCommandOutput,
  PutObjectCommandInput,
  S3Client,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { S3_BUCKET, S3_CLIENT } from './upload.constants';

@Injectable()
export class UploadService {
  private static OBJECT_EXPIRATION_IN_SECONDS = 300; // 5 minutes
  private static UNKNOWN_MIMETYPE = 'application/octet-stream';

  constructor(
    @Inject(S3_CLIENT) private readonly s3: S3Client,
    @Inject(S3_BUCKET) private readonly bucket: string,
  ) {}

  signUrl(Key: string, expiresInSeconds?: number): Promise<string> {
    return getSignedUrl(
      this.s3,
      new GetObjectCommand({
        Key,
        Bucket: this.bucket,
      }),
      {
        expiresIn:
          expiresInSeconds || UploadService.OBJECT_EXPIRATION_IN_SECONDS,
      },
    );
  }

  async store(
    file: unknown,
    params?: Omit<PutObjectCommandInput, 'Bucket' | 'Key' | 'Body'> &
      Required<Pick<PutObjectCommandInput, 'ContentType'>> & { Key?: string },
  ) {
    const upload = new Upload({
      client: this.s3,
      params: {
        ...params,
        Bucket: this.bucket,
        Key: params.Key ?? uuid(),
        Body: file as any,
      },
    });
    const result: CompleteMultipartUploadCommandOutput = await upload.done();
    const info = await this.getFileInfo(result.Key, result.Bucket);
    return {
      key: result.Key,
      bucket: result.Bucket,
      fileUrl: result.Location,
      contentType: info.ContentType,
      sizeInBytes: info.ContentLength,
    };
  }

  private async getFileInfo(
    Key: string,
    Bucket: string,
  ): Promise<HeadObjectCommandOutput> {
    const command = new HeadObjectCommand({ Key, Bucket });
    const info = await this.s3.send(command);
    return {
      ...info,
      ContentType: info.ContentType || UploadService.UNKNOWN_MIMETYPE,
    };
  }

  async deleteFileByKey(Key: string) {
    return this.s3.send(new DeleteObjectCommand({ Key, Bucket: this.bucket }));
  }
}
