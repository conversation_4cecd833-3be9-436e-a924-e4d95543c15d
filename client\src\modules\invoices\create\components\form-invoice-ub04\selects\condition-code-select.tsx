import { FormField } from '@/components';

interface ConditionCodeSelectProps {
  name: string;
  disabled?: boolean;
}

export function ConditionCodeSelect({ name, disabled }: ConditionCodeSelectProps) {
  const conditionCodes = [
    { value: '01', label: 'Military Service Related' },
    { value: '02', label: 'Condition is Employment Related' },
    { value: '03', label: 'Patient Covered by Insurance Not Reflected Here' },
    { value: '04', label: 'Information Only Bill' },
    { value: '05', label: 'Lien Has Been Filed' },
    { value: '06', label: 'ESRD Patient in First 18 Months' },
    { value: '07', label: 'Treatment of Non-Terminal Condition for Hospice' },
    { value: '08', label: 'Beneficiary Would Not Provide Information' },
    { value: '09', label: 'Neither Patient Nor Spouse is Employed' },
    { value: '10', label: 'Patient and/or Spouse is Employed but No EGHP' },
    { value: '11', label: 'Disabled Beneficiary But No LGHP' },
    { value: '12', label: 'Payer Codes' },
    { value: '13', label: 'Payer Codes' },
    { value: '14', label: 'Payer Codes' },
    { value: '15', label: 'Payer Codes' },
    { value: '16', label: 'Payer Codes' },
    { value: '17', label: 'Payer Codes' },
    { value: '18', label: 'Maiden Name Retained' },
    { value: '19', label: "Child Retains Mother's Name" },
    { value: '20', label: 'Beneficiary Requested Billing' },
    { value: '21', label: 'Billing for Denial Notice' },
    { value: '26', label: 'VA Eligible Patient Chooses to Receive Services' },
    { value: '27', label: 'Patient Referred to a Sole Community Hospital' },
    { value: '28', label: "Patient and/or Spouse's EGHP is Secondary to Medicare" },
    { value: '29', label: "Disabled Beneficiary and/or Family Member's LGHP is Secondary" },
    { value: '30', label: 'Qualifying Clinical Trial' },
    { value: '31', label: 'Patient is Student (Full Time-Day)' },
    { value: '32', label: 'Patient is Student (Cooperative/Work Study Program)' },
    { value: '33', label: 'Patient is Student (Full Time-Night)' },
    { value: '34', label: 'Patient is Student (Part Time)' },
    { value: '35', label: 'Reserved for National Assignment' },
    { value: '36', label: 'General Care Patient in a Special Unit' },
    { value: '37', label: 'Ward Accommodation at Patient Request' },
    { value: '38', label: 'Semi-Private Room Not Available' },
    { value: '39', label: 'Private Room Medically Necessary' },
    { value: '40', label: 'Same Day Transfer' },
    { value: '41', label: 'Partial Hospitalization Program' },
    { value: '42', label: 'Continuing Care Not Related to Inpatient Admission' },
    { value: '43', label: 'Continuing Care Not Provided Within Prescribed Post-Discharge Window' },
    { value: '44', label: 'Inpatient Admission Changed to Outpatient' },
    { value: '45', label: 'Ambiguous Gender Category' },
    { value: '46', label: 'Non-Availability Statement on File' },
    { value: '47', label: 'Reserved for National Assignment' },
    {
      value: '48',
      label: 'Psychiatric Residential Treatment Centers for Individuals Under Age 21',
    },
    { value: '49', label: 'Product Replacement Within Product Lifecycle' },
    { value: '50', label: 'Product Replacement for Known Recall of a Product' },
    { value: '51', label: 'Attestation of Unrelated Outpatient Non-Diagnostic Services' },
    { value: '52', label: 'Attestation of Unrelated Outpatient Diagnostic Services' },
    { value: '53', label: 'Attestation of Related Outpatient Non-Diagnostic Services' },
    { value: '54', label: 'Attestation of Related Outpatient Diagnostic Services' },
    { value: '55', label: 'Skilled Nursing Facility Bed Not Available' },
    { value: '56', label: 'Medical Appropriateness' },
    { value: '57', label: 'SNF Readmission' },
    { value: '58', label: 'Terminated Medicare+Choice Organization' },
    { value: '59', label: 'Non-Primary ESRD Facility' },
    { value: '60', label: 'Day Outlier Cost Center' },
    { value: '61', label: 'Cost Outlier - Cost Center' },
    { value: '62', label: 'Payer Code' },
    { value: '66', label: 'Provider Does Not Wish Cost Outlier Payment' },
    { value: '67', label: 'Beneficiary Elects Not to Use Lifetime Reserve Days' },
    { value: '68', label: 'Beneficiary Elects to Use Lifetime Reserve Days' },
    { value: '69', label: 'Operational Outlier' },
    { value: '70', label: 'Self Administered EPO' },
    { value: '71', label: 'Full Care in Unit' },
    { value: '72', label: 'Self Care in Unit' },
    { value: '73', label: 'Self Care Training' },
    { value: '74', label: 'Home' },
    { value: '75', label: 'Home - 100% Reimbursement' },
    { value: '76', label: 'Back-up In-facility Dialysis' },
    {
      value: '77',
      label:
        'Provider Accepts or is Obligated/Required Due to a Contractual Arrangement or Law to Accept Payment by a Primary Payer as Payment in Full',
    },
    { value: '78', label: 'New Coverage Not Implemented by HMO' },
    { value: '79', label: 'Corf Services Provided Off-Site' },
  ];

  return (
    <FormField
      name={name}
      type="select"
      options={conditionCodes}
      disabled={disabled}
      placeholder="Select Condition Code"
    />
  );
}
