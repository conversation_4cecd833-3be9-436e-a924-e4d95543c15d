'use client';

import { But<PERSON> } from '@digheontech/digh.ui';
import { useFormikContext } from 'formik';
import { FileTrigger } from 'react-aria-components';
import { MdFileUpload } from 'react-icons/md';

interface IFileUpload {
  name: string;
  mimeTypes: string[];
  dataCy?: string;
}

export function FileUploadSmall({ dataCy, mimeTypes, name }: IFileUpload) {
  const formik = useFormikContext();
  const file = (formik.values as any)[name] as File | null;

  const handleFileSelect = (files: FileList | null) => {
    const selectedFile = files && files.length > 0 ? files[0] : null;
    formik.setFieldTouched(name, true);
    formik.setFieldValue(name, selectedFile, true);
  };

  return (
    <FileTrigger onSelect={handleFileSelect} acceptedFileTypes={mimeTypes} data-cy={dataCy}>
      <section
        data-cy="selectFile"
        className="flex flex-col justify-center items-center gap-2 h-full"
      >
        <Button className="h-full" buttonType="normal" leftIcon={<MdFileUpload />} fullWidth>
          {file ? file.name : 'select File'}
        </Button>
      </section>
    </FileTrigger>
  );
}
