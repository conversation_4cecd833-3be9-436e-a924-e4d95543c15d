import { ReactNode } from 'react';
import { IFormInvoiceTypeAPayload } from '@/interfaces';

export interface SectionProps {
  invoice: IFormInvoiceTypeAPayload;
  isLoading?: boolean;
}

export interface IFieldCMS {
  label: string | ReactNode;
  value?: ReactNode;
  className?: string;
  labelClass?: string;
  valueClass?: string;
  children?: ReactNode;
  isVerified?: boolean;
}

export interface ICMSCheckbox {
  checked: boolean;
}

export interface IFormInvoiceTypeAReadOnly {
  invoice: IFormInvoiceTypeAPayload;
  isLoading?: boolean;
}
