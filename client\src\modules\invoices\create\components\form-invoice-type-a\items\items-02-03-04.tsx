import { FieldDatePicker, FormField } from '@/components';
import { Gender } from '@/enums';

const { Female, Male } = Gender;

export function Items02_03_04({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="grid grid-cols-3 border border-gray-300 border-b-transparent">
      <div className="flex gap-4 p-4 w-full hover-card">
        <strong>2.</strong>
        <FormField
          disabled={isLoading}
          name="patient.fullName"
          label="PATIENT'S NAME (Last Name, First Name, Middle Initial)"
          className="w-full"
        />
      </div>
      <div className="flex justify-evenly border border-y-transparent border-gray-300 p-4 hover-card">
        <div className="w-full">
          <div className="flex gap-4 w-full">
            <strong>3.</strong>
            <FieldDatePicker
              label="PATIENT´S BIRTHDATE"
              name="patient.birthDate"
              disabled={isLoading}
              labelClassName="text-primary-500 font-bold block text-sm mb-1"
            />
          </div>
        </div>
        <div className="w-1/2">
          <FormField
            label="SEX"
            type="radio"
            disabled={isLoading}
            name="patient.gender"
            options={[
              { value: Female, label: 'F' },
              { value: Male, label: 'M' },
            ]}
          />
        </div>
      </div>
      <div className="flex gap-4 p-4  hover-card">
        <strong>4.</strong>
        <FormField
          disabled={isLoading}
          name="insured.fullName"
          label="INSURED'S NAME (Last Name, First Name, Middle Initial)"
        />
      </div>
    </section>
  );
}
