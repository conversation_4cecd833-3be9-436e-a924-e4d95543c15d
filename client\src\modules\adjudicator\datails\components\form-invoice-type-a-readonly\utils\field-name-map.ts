export const fieldNameMap: Record<string, string> = {
  // Sección 1
  insuranceCarrier: '1. Insurance Type',
  insuranceIdNumber: '1a. Insured ID Number',
  // Sección 2
  'patient.fullName': '2. Patient Name',
  // Sección 3
  'patient.birthDate': '3. Patient Birthdate',
  'patient.gender': '3. Sex',
  // Sección 4
  'insured.fullName': '4. Insured Name',
  // Sección 5
  'patient.address': '5. Patient Address',
  // Sección 6
  patientRelationshipToInsured: "6. Patient's Relationship to Insured",
  // Sección 7
  'insured.address': '7. Insured Address',
  // Sección 9
  otherInsuredName: "9. Other Insured's Name",
  'insured.policyGroupNumber': "9a. Other Insured's Policy or Group Number",
  otherInsuredPlanNameOrProgramName: "9d. Other Insured's Plan or Program Name",
  // Sección 10
  conditionPatientRelatedToEmployment: '10a. Related to Employment?',
  conditionPatientRelatedToAutoAccident: '10b. Related to Auto Accident?',
  conditionPatientRelatedToAutoAccidentPlace: '10b. Accident Place',
  conditionPatientRelatedToOtherAccident: '10c. Other Accident?',
  // Sección 11
  otherInsuredPolicyOrGroupNumber: "11. Insured's policy group or feca number",
  'insured.birthDate': '11a. Insured Date of Birth',
  'insured.gender': '11a. Insured Gender',
  'insured.planOrProgramName': '11c. Insurance Plan Name or Program Name',
  'insured.anotherHealthBenefitPlan': '11d. Is there another health benefit plan?',
  // Sección 12
  patientOrAuthorizedSignatureDate: '12. Patient Or Authorized Signature Date',
  // section 14
  dateOfCurrentIllnessInjuryPregnancy: '14. Date of Current Illness, Injury, or Pregnancy',
  qualifierOfCurrentIllnessInjuryAccident: '14. QUAL',
  // section 15
  otherDateConditionOfIllnessOrTreatment: '15. Other Date',
  qualifierOfOtherConditionOfIllnessOrTreatment: '15. QUAL',
  // section 16
  unableToWorkFromDate: '16. Unable to Work From',
  unableToWorkToDate: '16. Unable to Work To',
  // section 17
  referringProviderName: '17. Referring Provider',
  referringProviderQualifier: '17. Referring Provider Name',
  referringProviderNpi: '17b. NPI',
  referringProviderOtherIdQualifier: '17a. Qualifier',
  referringProviderOtherId: '17a. Other ID',
  // section 18
  hospitalizationFromDate: '18. Hospitalization dates related to current services From Date',
  hospitalizationToDate: '18. Hospitalization dates related to current services To Date',
  // Sección 20
  isOutsideLab: '20. Outside Lab?',
  outsideLabCharges: '20. Charges',
  // section 21
  icd10DiagnosisCodesForDiseasesOrInjuries: '21. ICD-10 Diagnosis Codes for Diseases or Injuries',
  // section 22
  resubmissionCode: '22. Medicaid Resubmission Code',
  originalReferenceNumber: '22. Original Ref. NO.:',
  // section 23
  priorAuthorizationNumber: '23. Prior Authorization Number',
  // section 24
  supplementalInformation: '24. Service Line Information',
  // section 25
  federalTaxIdNumber: '25. Federal Tax ID Number',
  federalTaxIdNumberType: '25. Tax Type',
  // section 26
  patientAccountNumber: '26. Patient Account Number',
  // section 27
  acceptAssignment: '27. Accept Assignment?',
  // section 28
  totalCharge: '28. Total Charge',
  // section 29
  amountPaid: '29. Amount Paid',
  // section 31
  physicianSignatureDate: '31. Physician Signature Date',
  // section 32
  serviceFacilityLocation: '32. Service Facility Location',
  // section 33
  billingProvider: '33. Billing Provider',
};
