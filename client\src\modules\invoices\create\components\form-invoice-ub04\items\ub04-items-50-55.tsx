import { FieldArray } from 'formik';
import { FormField } from '@/components';
import { But<PERSON> } from '@digheontech/digh.ui';
import { PayerSearch } from '../searchs';
import { Key } from 'react';

export function UB04Items50_55({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 p-4">
      <h3 className="text-lg font-bold mb-4">Payer Information (Fields 50-55)</h3>

      <FieldArray name="payerInformation">
        {({ push, remove, form }) => (
          <div>
            {form.values.payerInformation?.map((_: any, index: Key | null | undefined) => (
              <div key={index} className="border border-gray-200 p-4 mb-4 rounded">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-semibold">Payer {Number(index) + 1}</h4>
                  <Button onPress={() => remove(Number(index))}>Remove Payer</Button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Campo 50: Payer Name */}
                  <PayerSearch
                    name={`payerInformation.${index}.payerName`}
                    label="50. Payer Name"
                    disabled={isLoading}
                  />

                  {/* Campo 51: Health Plan ID */}
                  <FormField
                    name={`payerInformation.${index}.payerId`}
                    label="51. Health Plan ID"
                    disabled={isLoading}
                  />

                  {/* Campo 52: Release of Information */}
                  <FormField
                    name={`payerInformation.${index}.releaseOfInformation`}
                    label="52. Release of Information"
                    type="select"
                    options={[
                      { value: 'Y', label: 'Yes' },
                      { value: 'I', label: 'Informed Consent' },
                      { value: 'N', label: 'No' },
                    ]}
                    disabled={isLoading}
                  />

                  {/* Campo 53: Assignment of Benefits */}
                  <FormField
                    name={`payerInformation.${index}.assignmentOfBenefits`}
                    label="53. Assignment of Benefits"
                    type="select"
                    options={[
                      { value: 'Y', label: 'Yes' },
                      { value: 'N', label: 'No' },
                    ]}
                    disabled={isLoading}
                  />

                  {/* Campo 54: Prior Payments */}
                  <FormField
                    name={`payerInformation.${index}.priorPayments`}
                    label="54. Prior Payments"
                    type="number"
                    step={0.01}
                    disabled={isLoading}
                  />

                  {/* Campo 55: Estimated Amount Due */}
                  <FormField
                    name={`payerInformation.${index}.estimatedAmountDue`}
                    label="55. Estimated Amount Due"
                    type="number"
                    step={0.01}
                    disabled={isLoading}
                  />
                </div>
              </div>
            ))}

            <Button
              onPress={() =>
                push({
                  payerName: '',
                  payerId: '',
                  releaseOfInformation: '',
                  assignmentOfBenefits: '',
                  priorPayments: 0,
                  estimatedAmountDue: 0,
                })
              }
            >
              Add Payer
            </Button>
          </div>
        )}
      </FieldArray>
    </section>
  );
}
