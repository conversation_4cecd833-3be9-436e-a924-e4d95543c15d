import { Body, Controller, Get, Post } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';

import { UserRole } from 'src/shared/enums';
import { Roles } from 'src/identity/infrastructure/roles.decorator';

import { CreateProviderCommand } from '../application/create-provider/create-provider.command';
import { ListProvidersQuery } from '../application/list-providers/list-providers.query';

import { CreateProviderDto } from './dto/create-provider.dto';
import { ValidateProviderNpiDto } from './dto/validate-provider-npi.dto';
import { ValidateProviderNpiCommand } from '../application/validate-provider-npi/validate-provider-npi.command';
import { Public } from 'src/shared/validation';

@Controller('providers')
export class ProvidersController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBuys: QueryBus,
  ) {}

  @Post('/')
  @Roles(UserRole.SuperAdmin)
  create(@Body() body: CreateProviderDto) {
    this.commandBus.execute(new CreateProviderCommand(body));
  }

  @Get('/')
  @Roles(UserRole.SuperAdmin)
  listAllProviders() {
    return this.queryBuys.execute(new ListProvidersQuery());
  }

  @Post('/validate-npi')
  @Public()
  validateNPI(@Body() body: ValidateProviderNpiDto) {
    return this.commandBus.execute(new ValidateProviderNpiCommand(body));
  }
}
