import { useQuery } from '@tanstack/react-query';
import { Http } from '@/config/http';

interface RootObject {
  token: string;
  user: User;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  role: string;
  address: Address;
  createdAt: string;
  updatedAt: string;
  avatarUrl?: string;
}

interface Address {
  street: string;
  municipality: string;
  zipCode: number;
}

export function useGetProfile() {
  const { data: profile, ...rest } = useQuery<RootObject | null>({
    queryKey: ['USER_PROFILE'],
    queryFn: () => Http.get('/users/me').then(({ data }) => data),
  });

  return { profile, ...rest };
}
