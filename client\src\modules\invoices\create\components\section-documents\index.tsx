import { Button } from '@digheontech/digh.ui';
import { BiArrowFromTop } from 'react-icons/bi';
import { Disclosure, DisclosureGroup, DisclosurePanel, Heading } from 'react-aria-components';

import { UploadDocument } from './upload-document';
import { DocumentList } from './document-list';

interface ISectionDocuments {
  invoiceId: string;
}

export function SectionDocuments({ invoiceId }: ISectionDocuments) {
  return (
    <DisclosureGroup
      defaultExpandedKeys={['UPLOAD_DOCUMENTS']}
      allowsMultipleExpanded
      className="w-auto"
    >
      <Disclosure id="UPLOAD_DOCUMENTS">
        <Heading className="flex justify-between items-center">
          <Button
            rightIcon={<BiArrowFromTop />}
            slot="trigger"
            buttonType="normal"
            className="w-full border-none bg-slate-100 hover-card"
          >
            <h2 className="text-balance text-lg">Subir Documentos</h2>
          </Button>
        </Heading>
        <DisclosurePanel>
          <section className="grid grid-cols-2 border border-gray-300 border-x-transparent">
            <UploadDocument invoiceId={invoiceId} />
            <DocumentList invoiceId={invoiceId} />
          </section>
        </DisclosurePanel>
      </Disclosure>
    </DisclosureGroup>
  );
}
