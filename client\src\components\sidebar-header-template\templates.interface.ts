export interface ITwoColumnsTemplate {
  children: React.ReactNode;
  columnWidth?:
    | 'sm:grid-cols-[192px_192px]'
    | 'sm:grid-cols-[208px_208px]'
    | 'sm:grid-cols-[224px_224px]'
    | 'sm:grid-cols-[240px_240px]'
    | 'md:grid-cols-[256px_256px]'
    | 'md:grid-cols-[288px_288px]'
    | 'md:grid-cols-[320px_320px]'
    | 'lg:grid-cols-[384px_384px]'
    | 'lg:grid-cols-[420px_420px]'
    | 'xl:grid-cols-[456px_456px]'
    | 'xl:grid-cols-[488px_488px]'
    | 'xl:grid-cols-[520px_520px]';
}

export interface IOneColumnTemplate {
  children: React.ReactNode;
  columnWidth?:
    | 'md:max-w-[192px]'
    | 'md:max-w-[208px]'
    | 'md:max-w-[224px]'
    | 'md:max-w-[240px]'
    | 'md:max-w-[256px]'
    | 'md:max-w-[288px]'
    | 'md:max-w-[320px]'
    | 'md:max-w-[384px]'
    | 'md:max-w-[240px]'
    | 'md:max-w-[456px]'
    | 'md:max-w-[488px]'
    | 'md:max-w-[520px]'
    | 'md:max-w-[588px]'
    | 'md:max-w-[620px]';
}
