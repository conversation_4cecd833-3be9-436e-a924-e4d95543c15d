import { Key } from 'react';
import { useField } from 'formik';
import { LuChevronsUpDown } from 'react-icons/lu';
import { Button, Label, ListBox, Popover, Select, SelectValue } from 'react-aria-components';

import { ProvidersType } from '@/enums';
import { Status } from '@/components';
import { getProviderLabel } from '@/tools';
import { StatusItem } from './status-item';

export function ProviderSelectType() {
  const [field, meta, helpers] = useField<ProvidersType>('type');

  const handleSelectionChange = (key: Key) => {
    helpers.setValue(key as ProvidersType);
  };

  return (
    <div className="flex flex-col gap-1 w-full">
      <Select
        className="flex flex-col gap-1 w-full mb-4"
        selectedKey={field.value}
        onSelectionChange={handleSelectionChange}
        defaultSelectedKey={undefined}
        placeholder="select"
      >
        <Label className="text-slate-700 cursor-default">Tipo</Label>
        <Button className="field-shadow h-8">
          <SelectValue className="flex-1 truncate placeholder-shown:italic">
            {value => getProviderLabel(value as unknown as ProvidersType)}
          </SelectValue>
          <LuChevronsUpDown />
        </Button>
        <Popover className="max-h-60 w-[--trigger-width] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out">
          <ListBox className="outline-none p-1">
            <StatusItem key="PRIVATE" id={ProvidersType.Private}>
              <Status className="bg-blue-300" />
              {getProviderLabel(ProvidersType.Private)}
            </StatusItem>
            <StatusItem key="GOVERNMENT" id={ProvidersType.Government}>
              <Status className="bg-green-300" />
              {getProviderLabel(ProvidersType.Government)}
            </StatusItem>
          </ListBox>
        </Popover>
      </Select>
      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
