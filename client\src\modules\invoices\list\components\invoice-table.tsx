'use client';

import Link from 'next/link';
import { ReactElement, ReactNode, useMemo } from 'react';
import { BiSolidEdit } from 'react-icons/bi';
import { Button, Table } from '@digheontech/digh.ui';
import { Empty, Status } from '@/components';

import { InvoiceStatus, InvoiceType } from '@/enums';
import { formatDate, getInvoiceStatusLabel } from '@/tools';
import { useGetInvoices } from '../hooks/use-get-invoices';
import { ModalDeleteInvoice } from './modal-delete-invoice';

interface TableColumn {
  name: string;
  id: string;
  isRowHeader?: boolean;
  className?: string;
}
interface TableRow {
  id: string;
  patient: ReactNode;
  type: InvoiceType;
  fileCount: ReactNode;
  status: ReactNode;
  updatedAt: ReactNode;
  actions: ReactNode;
  patientRecordNumber: ReactNode;
}

const rowClassName = 'text-sm font-light';
const columnClass = 'text-sm font-bold';

const STATUS_COLORS: Record<InvoiceStatus, string> = {
  [InvoiceStatus.Draft]: 'bg-[#D9D9D9]',
  [InvoiceStatus.Admitted]: 'bg-[#379DAA]',
  [InvoiceStatus.Rejected]: 'bg-[#FF6F5B]',
  [InvoiceStatus.Standby]: 'bg-[#FFC531]',
};

const colorStatus = (status: InvoiceStatus): ReactElement => (
  <Status className={STATUS_COLORS[status]} />
);

const isDisabledBtn = (status: InvoiceStatus) => {
  return status === InvoiceStatus.Admitted;
};

export const InvoicesTable = () => {
  const { invoices, isLoading } = useGetInvoices();

  const columns: TableColumn[] = useMemo(
    () => [
      { isRowHeader: true, name: 'Paciente', id: 'patient', className: columnClass },
      { name: 'Factura', id: 'type', className: columnClass },
      { name: 'Fecha', id: 'updatedAt', className: columnClass },
      { name: 'Documentos', id: 'fileCount', className: columnClass },
      { name: 'Estado', id: 'status', className: columnClass },
      {
        name: 'Acciones',
        id: 'actions',
        className: columnClass + 'flex justify-end w-30 br-red-300 text-center',
      },
    ],
    [],
  );

  const rows: TableRow[] = useMemo(
    () =>
      invoices?.map(
        ({ _id: id, type, patient, status, updatedAt, fileCount, patientRecordNumber }) => ({
          id,
          patient: <span className={rowClassName}>{patient?.fullName || patientRecordNumber}</span>,
          type,
          updatedAt: <span className={rowClassName}>{formatDate(updatedAt)}</span>,
          status: (
            <div className="flex items-baseline justify-start gap-2">
              {colorStatus(status)}
              {getInvoiceStatusLabel(status)}
            </div>
          ),
          fileCount: (
            <span className={rowClassName}>
              {fileCount ? (
                <span>
                  <b>{fileCount}</b> documento(s)
                </span>
              ) : (
                'Sin documentos'
              )}
            </span>
          ),
          patientRecordNumber: <span className={rowClassName}>{patientRecordNumber}</span>,
          actions: !isDisabledBtn(status) && (
            <div className="flex gap-2 justify-center items-center">
              <Link href={`/invoices/create?invoiceId=${id}`} passHref>
                <Button
                  leftIcon={<BiSolidEdit size={20} />}
                  buttonType="normal"
                  disabled={isDisabledBtn(status)}
                  className="rounded-full w-10 h-10 p-2 border-none hover:bg-primary-200"
                />
              </Link>
              <ModalDeleteInvoice
                invoiceId={id}
                disabled={isDisabledBtn(status)}
                patientName={patient?.fullName}
              />
            </div>
          ),
        }),
      ) || [],
    [invoices],
  );

  return (
    <main className="mt-6 bg-white p-5 rounded-md">
      <div className="flex justify-around flex-col gap-4">
        <Table<TableRow>
          isLoading={isLoading}
          columns={columns}
          rows={rows}
          tableBodyProps={{
            renderEmptyState: () => <Empty />,
          }}
          wrapClasses="max-h-full"
          headerCellClasses="bg-[#DDEDF6] text-slate-700"
        />
      </div>
    </main>
  );
};
