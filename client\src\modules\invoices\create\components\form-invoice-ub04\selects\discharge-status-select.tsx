import { FormField } from '@/components';

interface DischargeStatusSelectProps {
  name: string;
  label: string;
  disabled?: boolean;
}

export function DischargeStatusSelect({ name, label, disabled }: DischargeStatusSelectProps) {
  const dischargeStatuses = [
    { value: '01', label: 'Discharged to Home or Self Care' },
    { value: '02', label: 'Discharged/Transferred to SNF' },
    { value: '03', label: 'Discharged/Transferred to ICF' },
    { value: '04', label: 'Discharged/Transferred to Psychiatric Hospital' },
    { value: '05', label: 'Discharged/Transferred to Critical Access Hospital' },
    { value: '06', label: 'Discharged/Transferred to Another Short Term Hospital' },
    { value: '07', label: 'Left Against Medical Advice' },
    { value: '20', label: 'Expired' },
    { value: '21', label: 'Discharged/Transferred to Court/Law Enforcement' },
    { value: '30', label: 'Still Patient or Expected to Return' },
    { value: '43', label: 'Discharged/Transferred to Federal Health Care Facility' },
    { value: '50', label: 'Hospice - Home' },
    { value: '51', label: 'Hospice - Medical Facility' },
    { value: '61', label: 'Discharged/Transferred to Hospital-Based Medicare Approved Swing Bed' },
    { value: '62', label: 'Discharged/Transferred to Inpatient Rehabilitation Facility' },
    { value: '63', label: 'Discharged/Transferred to Medicare Certified Long Term Care Hospital' },
    { value: '64', label: 'Discharged/Transferred to Nursing Facility Certified Under Medicaid' },
    { value: '65', label: 'Discharged/Transferred to Psychiatric Hospital or Unit' },
    { value: '66', label: 'Discharged/Transferred to Critical Access Hospital' },
  ];

  return (
    <FormField
      name={name}
      label={label}
      type="select"
      options={dischargeStatuses}
      disabled={disabled}
    />
  );
}
