import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommandHand<PERSON> } from '@nestjs/cqrs';

import { DomainError } from 'src/shared/domain';
import { InvoiceAdjustmentRepository } from 'src/invoices/infrastructure/repositories/invoice-adjustments.repository';
import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';

import { RemoveItemAdjustmentCommand } from './remove-item-adjustment.command';

@CommandHandler(RemoveItemAdjustmentCommand)
export class RemoveItemAdjustmentHandler
  implements ICommandHandler<RemoveItemAdjustmentCommand>
{
  constructor(
    private readonly invoiceAdjustmentRepository: InvoiceAdjustmentRepository,
    private readonly invoiceRepository: InvoiceRepository,
  ) {}

  async execute(command: RemoveItemAdjustmentCommand) {
    await this.checkExistsInvoiceAdjustment(command.originalInvoiceId);

    return this.invoiceAdjustmentRepository.update(command.originalInvoiceId, {
      [command.itemAdjustmentId]: null,
    });
  }

  private async checkExistsInvoiceAdjustment(originalInvoiceId: string) {
    const invoiceAdjustment =
      await this.invoiceAdjustmentRepository.findByInvoiceId(originalInvoiceId);

    if (!invoiceAdjustment) {
      throw new DomainError(
        'INVOICE_ADJUSTMENT_NOT_FOUND',
        'Invoice adjustment not found',
      );
    }
  }
}
