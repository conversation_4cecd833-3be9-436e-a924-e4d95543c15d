import { useMutation } from '@tanstack/react-query';
import { useShowError, useShowSuccess } from '@/hooks';
import { AxiosResponseError, Http } from '@/config/http';
import { DocumentType } from '@/enums';
import { useDocumentList } from './use-document-list';

interface EditDocumentPayload {
  fileName: string;
  documentType: DocumentType;
}

export const useEditDocument = (invoiceId: string, fileId: string) => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateFiles } = useDocumentList(invoiceId);

  const {
    mutate,
    isPending: isEditing,
    ...rest
  } = useMutation({
    mutationKey: ['edit_document', invoiceId, fileId],
    mutationFn: async (payload: EditDocumentPayload) => {
      return Http.patch(`invoices/${invoiceId}/files/${fileId}`, payload);
    },
    onError: (error: AxiosResponseError) => {
      showError(error);
    },
    onSuccess: () => {
      showSuccess({
        title: 'Éxito',
        description: 'Documento actualizado correctamente.',
      });
      invalidateFiles();
    },
  });

  const onSubmit = (values: EditDocumentPayload, onSuccess?: () => void) => {
    mutate(values, {
      onSuccess: () => {
        if (onSuccess) {
          onSuccess();
        }
      },
    });
  };

  return { onSubmit, isEditing, ...rest };
};
