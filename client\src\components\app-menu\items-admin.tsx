'use client';

import { MdOutlineDashboard } from 'react-icons/md';
import { LuChartSpline, LuCirclePercent, LuDollarSign, LuUsersRound } from 'react-icons/lu';
import { GrBusinessService } from 'react-icons/gr';

import { MenuLink } from './menu-link';
import { UserRole } from '@/enums';
import useLoggedUser from '@/hooks/use-logged-user';

export function ItemsAdmin({ collapsed }: { collapsed: boolean }) {
  const { SuperAdmin } = UserRole;
  const { loggedUser } = useLoggedUser();

  if (loggedUser?.role !== SuperAdmin) {
    return null;
  }

  return (
    <nav className="flex flex-col h-full py-6 px-4 gap-6 items-center w-full">
      <ul className="flex flex-col gap-4">
        <li className="mt-auto w-full">
          <MenuLink
            href="/admin/dashboard"
            label="Dashboard"
            icon={<MdOutlineDashboard />}
            collapsed={collapsed}
          />
        </li>
        <li>
          <MenuLink
            href="/providers/list"
            label="Proveedores"
            icon={<GrBusinessService />}
            collapsed={collapsed}
          />
        </li>
        <li>
          <MenuLink
            href="/users/providers"
            label="Usuarios"
            icon={<LuUsersRound />}
            collapsed={collapsed}
          />
        </li>
        <li>
          <MenuLink
            href="/adjudicator/list"
            label="Reclamaciones"
            icon={<LuCirclePercent />}
            collapsed={collapsed}
          />
        </li>
        <li>
          <MenuLink href="/" label="Finanzas" icon={<LuDollarSign />} collapsed={collapsed} />
        </li>
        <li>
          <MenuLink href="/" label="Reportes" icon={<LuChartSpline />} collapsed={collapsed} />
        </li>
      </ul>
    </nav>
  );
}
