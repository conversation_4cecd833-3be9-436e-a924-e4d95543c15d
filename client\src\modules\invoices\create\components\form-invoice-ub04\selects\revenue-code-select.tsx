import { FormField } from '@/components';

interface RevenueCodeSelectProps {
  name: string;
  disabled?: boolean;
}

export function RevenueCodeSelect({ name, disabled }: RevenueCodeSelectProps) {
  const revenueCodes = [
    // Accommodation Revenue Codes
    { value: '0100', label: 'All Inclusive Rate' },
    { value: '0101', label: 'All Inclusive Rate - Room and Board Plus Ancillary' },
    { value: '0110', label: 'Room and Board - Private (Medical or General)' },
    { value: '0111', label: 'Room and Board - Private (Medical or General) - 1 Bed' },
    { value: '0112', label: 'Room and Board - Private (Medical or General) - 2 Bed' },
    { value: '0120', label: 'Room and Board - Semi-Private (Medical or General)' },
    { value: '0121', label: 'Room and Board - Semi-Private (Medical or General) - 2 Bed' },
    { value: '0122', label: 'Room and Board - Semi-Private (Medical or General) - 3&4 Bed' },
    { value: '0130', label: 'Room and Board - Ward (Medical or General)' },
    { value: '0131', label: 'Room and Board - Ward (Medical or General) - 4+ Bed' },
    { value: '0140', label: 'Room and Board - Other (Medical or General)' },
    { value: '0150', label: 'Room and Board - Intensive Care' },
    { value: '0151', label: 'Room and Board - Intensive Care - Surgical' },
    { value: '0152', label: 'Room and Board - Intensive Care - Medical' },
    { value: '0153', label: 'Room and Board - Intensive Care - Pediatric' },
    { value: '0154', label: 'Room and Board - Intensive Care - Psychiatric' },
    { value: '0155', label: 'Room and Board - Intensive Care - Intermediate ICU' },
    { value: '0156', label: 'Room and Board - Intensive Care - Burn Care' },
    { value: '0157', label: 'Room and Board - Intensive Care - Trauma' },
    { value: '0158', label: 'Room and Board - Intensive Care - Other' },
    { value: '0159', label: 'Room and Board - Intensive Care - Other' },
    { value: '0160', label: 'Room and Board - Coronary Care' },
    { value: '0164', label: 'Room and Board - Coronary Care - Intermediate CCU' },
    { value: '0167', label: 'Room and Board - Coronary Care - Telemetry' },
    { value: '0169', label: 'Room and Board - Coronary Care - Other CCU' },
    { value: '0170', label: 'Room and Board - Nursery' },
    { value: '0171', label: 'Room and Board - Nursery - Newborn Level I' },
    { value: '0172', label: 'Room and Board - Nursery - Newborn Level II' },
    { value: '0173', label: 'Room and Board - Nursery - Newborn Level III' },
    { value: '0174', label: 'Room and Board - Nursery - Newborn Level IV' },
    { value: '0179', label: 'Room and Board - Nursery - Other Nursery' },
    { value: '0180', label: 'Room and Board - Leave of Absence' },
    { value: '0181', label: 'Room and Board - Leave of Absence - Patient Convenience' },
    { value: '0182', label: 'Room and Board - Leave of Absence - Therapeutic Leave' },
    { value: '0183', label: 'Room and Board - Leave of Absence - ICF Mentally Retarded' },
    {
      value: '0185',
      label: 'Room and Board - Leave of Absence - Nursing Home (for Hospitalization)',
    },
    { value: '0189', label: 'Room and Board - Leave of Absence - Other Leave of Absence' },
    { value: '0190', label: 'Room and Board - Subacute Care' },
    { value: '0191', label: 'Room and Board - Subacute Care - Level I' },
    { value: '0192', label: 'Room and Board - Subacute Care - Level II' },
    { value: '0193', label: 'Room and Board - Subacute Care - Level III' },
    { value: '0194', label: 'Room and Board - Subacute Care - Level IV' },
    { value: '0199', label: 'Room and Board - Other' },

    // Ancillary Service Revenue Codes
    { value: '0200', label: 'Intensive Care Unit' },
    { value: '0201', label: 'Surgical ICU' },
    { value: '0202', label: 'Medical ICU' },
    { value: '0203', label: 'Pediatric ICU' },
    { value: '0204', label: 'Psychiatric ICU' },
    { value: '0206', label: 'Intermediate ICU' },
    { value: '0207', label: 'Burn Care ICU' },
    { value: '0208', label: 'Trauma ICU' },
    { value: '0209', label: 'Other ICU' },
    { value: '0210', label: 'Coronary Care Unit' },
    { value: '0211', label: 'Myocardial Infarction CCU' },
    { value: '0212', label: 'Pulmonary Care CCU' },
    { value: '0213', label: 'Heart Transplant CCU' },
    { value: '0214', label: 'Intermediate CCU' },
    { value: '0219', label: 'Other CCU' },
    { value: '0220', label: 'Operating Room Services' },
    { value: '0221', label: 'OR Services - Minor Surgery' },
    { value: '0222', label: 'OR Services - Organ Transplant (Other than Kidney)' },
    { value: '0223', label: 'OR Services - Kidney Transplant' },
    { value: '0224', label: 'OR Services - Heart Transplant' },
    { value: '0225', label: 'OR Services - Heart/Lung Transplant' },
    { value: '0229', label: 'OR Services - Other OR Services' },
    { value: '0230', label: 'Anesthesia' },
    { value: '0231', label: 'Anesthesia - Anesthesia Incident to Radiology' },
    { value: '0232', label: 'Anesthesia - Anesthesia Incident to Other Diagnostic Services' },
    { value: '0233', label: 'Anesthesia - Anesthesia Incident to Other Therapeutic Services' },
    { value: '0234', label: 'Anesthesia - Acupuncture' },
    { value: '0239', label: 'Anesthesia - Other Anesthesia' },
    { value: '0240', label: 'Blood Storage and Processing' },
    { value: '0241', label: 'Blood Admin' },
    { value: '0242', label: 'Packed Red Cells' },
    { value: '0243', label: 'Platelets' },
    { value: '0244', label: 'Fresh Frozen Plasma' },
    { value: '0245', label: 'Whole Blood' },
    { value: '0246', label: 'Plasma' },
    { value: '0247', label: 'Cryoprecipitate' },
    { value: '0248', label: 'Fibrinogen' },
    { value: '0249', label: 'Other Blood Storage and Processing' },
  ];

  return (
    <FormField
      name={name}
      type="select"
      options={revenueCodes}
      disabled={disabled}
      placeholder="Select Revenue Code"
    />
  );
}
