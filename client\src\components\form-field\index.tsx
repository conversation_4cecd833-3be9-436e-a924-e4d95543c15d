'use client';

import { Field, ErrorMessage } from 'formik';
import { InputProps, Label } from 'react-aria-components';
import { Input, Textarea } from '@digheontech/digh.ui';
import { isRequiredForSubmission } from '@/modules/invoices/create/components/form-invoice-type-a/utils/required-fields';

interface IFormField extends InputProps {
  label?: string | React.ReactNode;
  textarea?: boolean;
  type?:
    | 'text'
    | 'radio'
    | 'phone'
    | 'tel'
    | 'email'
    | 'password'
    | 'number'
    | 'checkbox'
    | 'date'
    | 'time'
    | 'datetime-local'
    | 'select'
    | 'file'
    | 'color'
    | 'range';
  options?: {
    value: string | number | boolean;
    label: string;
    sublabel?: string;
    disabled?: boolean;
  }[];
  className?: string;
  height?: string | number;
  border?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  step?: number;
  min?: number | string;
  max?: number | string;
  autoComplete?: 'on' | 'off' | 'email' | 'name' | 'tel' | 'username' | 'password';
  multiple?: boolean;
  viewLabel?: boolean;
}

export const FormField = ({
  label,
  type = 'text',
  name,
  textarea,
  options,
  className,
  height = 40,
  border,
  viewLabel = true,
  ...props
}: IFormField) => {
  const isRequiredField = name ? isRequiredForSubmission(name) : false;

  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      {type === 'radio' ? (
        <>
          <Label
            className={
              isRequiredField
                ? 'text-primary-500 font-bold block text-sm mb-1'
                : ' block text-sm mb-1 font-medium'
            }
          >
            <div className="flex">
              {isRequiredField && <span className="text-red-500 text-lg mr-0.5">*</span>}
              {label}
            </div>
          </Label>
          <div className={`flex justify-between gap-8 cursor-pointer ${className}`}>
            {options?.map(option => (
              <Label key={option.label} className="flex items-center">
                <Field type={type} name={name} value={option.value} className="mr-2" />
                <div>
                  <div className="text-sm font-medium">{option.label}</div>
                  {option.sublabel && (
                    <div className="text-xs text-gray-500">{option.sublabel}</div>
                  )}
                </div>
              </Label>
            ))}
          </div>
          <ErrorMessage name={name!}>
            {msg => <span className="text-red-400 text-sm">{msg}</span>}
          </ErrorMessage>
        </>
      ) : (
        <>
          {textarea && (
            <Label
              className={
                isRequiredField
                  ? 'text-primary-500 font-bold block text-sm mb-1'
                  : ' block text-sm mb-1 font-medium'
              }
            >
              {isRequiredField && <span className="text-red-500 text-lg mr-0.5">*</span>}
              {label}
            </Label>
          )}
          <Field
            as={textarea ? Textarea : Input}
            data-cy={name}
            label={
              <Label
                className={
                  isRequiredField
                    ? 'text-primary-500 font-bold block text-sm mb-1'
                    : ' block text-sm mb-1 font-medium'
                }
              >
                {isRequiredField && viewLabel && (
                  <span className="text-red-500 text-lg mr-0.5">*</span>
                )}
                {label}
              </Label>
            }
            name={name}
            small
            type={type}
            style={{ height: textarea ? 'auto' : height, border: border }}
            aria-label={label}
            {...props}
          />
          <ErrorMessage name={name!}>
            {msg => <span className="text-red-400 text-sm">{msg}</span>}
          </ErrorMessage>
        </>
      )}
    </div>
  );
};
