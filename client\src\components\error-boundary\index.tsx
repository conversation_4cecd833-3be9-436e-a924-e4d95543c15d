'use client';

import { Component, ReactNode } from 'react';
import { CardErrors } from '../card-errors';

interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <CardErrors
          error={this.state.error}
          onRetry={() => this.setState({ hasError: false, error: null })}
          title="Error loading team members"
          description=""
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
