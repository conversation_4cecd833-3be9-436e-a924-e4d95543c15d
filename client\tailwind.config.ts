import plugin from 'tailwindcss/plugin';
import animate from 'tailwindcss-animate';
import tailwindcss from 'tailwindcss-react-aria-components';

module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],
  theme: {
    extend: {
      backgroundImage: {
        guest: 'url(/img/landing-bg.png)',
      },
      cursor: {
        custom: 'url(https://cur.cursors-4u.net/cursors/cur-11/cur1046.cur), auto',
      },
      colors: {
        primary: {
          100: '#ddedf6',
          200: '#b4d9e3',
          300: '#8ac5d0',
          400: '#61b1bd',
          500: '#379daa',
          600: '#2e7d87',
          700: '#245d64',
          800: '#1b3d41',
          900: '#111d1e',
          950: '#0a0a0b',
        },
      },
    },
  },
  plugins: [
    animate,
    tailwindcss,
    plugin(function ({ addUtilities, theme }: any) {
      const newUtilities = {
        'button.btn-primary': {
          backgroundColor: theme('colors.primary.500'),
          borderColor: theme('colors.primary.500'),
          '&:hover': {
            backgroundColor: theme('colors.primary.700'),
            borderColor: theme('colors.primary.700'),
          },
          '&:active': {
            backgroundColor: theme('colors.primary.600'),
            borderColor: theme('colors.primary.600'),
          },
        },
        '.field-shadow': {
          border: 'none',
          display: 'flex',
          alignItems: 'center',
          cursor: 'default',
          borderRadius: theme('borderRadius.lg'),
          borderWidth: '0',
          backgroundColor: theme('colors.white'),
          backgroundOpacity: '0.90',
          transition: 'background-opacity 0.2s',
          padding: theme('spacing.6'),
          paddingLeft: theme('spacing.5'),
          paddingRight: theme('spacing.2'),
          fontSize: theme('fontSize.base'),
          textAlign: 'left',
          lineHeight: theme('lineHeight.normal'),
          boxShadow: theme('boxShadow.md'),
          color: theme('colors.gray.700'),
          '&:focus': {
            outline: 'none',
            ring: '2',
            ringColor: theme('colors.white'),
            ringOffsetWidth: '2',
            ringOffsetColor: theme('colors.primary.700'),
          },
        },
        '.hover-card': {
          '&:hover': {
            backgroundColor: theme('colors.blue.100'),
            color: theme('colors.slate.700'),
            fontSize: theme('fontSize.xl'),
          },
        },
      };
      addUtilities(newUtilities);
    }),
  ],
};
