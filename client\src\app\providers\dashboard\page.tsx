'use client';

import { AppMenu, SidebarHeaderTemplate } from '@/components';
import { DashboardPage } from '@/modules/providers/dashboard';
import { useSidebarCollapsed } from '@/hooks/use-sidebar-collapsed';

export default function ProvidersDashboard() {
  const [collapsed, setCollapsed] = useSidebarCollapsed();
  return (
    <SidebarHeaderTemplate
      sidebar={<AppMenu collapsed={collapsed} setCollapsed={setCollapsed} />}
      collapsed={collapsed}
    >
      <DashboardPage />
    </SidebarHeaderTemplate>
  );
}
