'use client';

import { App<PERSON>enu, HeaderWithButtons, SidebarHeaderTemplate } from '@/components';
import { useSidebarCollapsed } from '@/hooks/use-sidebar-collapsed';
import { InvoicesDetailsPage } from '@/modules/adjudicator/datails';

export default function AdjudicatorInvoicesDetails() {
  const [collapsed, setCollapsed] = useSidebarCollapsed();

  return (
    <SidebarHeaderTemplate
      header={<HeaderWithButtons back="/adjudicator/list" textBack="Reajuste de Facturas" />}
      sidebar={<AppMenu collapsed={collapsed} setCollapsed={setCollapsed} />}
      collapsed={collapsed}
    >
      <InvoicesDetailsPage />
    </SidebarHeaderTemplate>
  );
}
