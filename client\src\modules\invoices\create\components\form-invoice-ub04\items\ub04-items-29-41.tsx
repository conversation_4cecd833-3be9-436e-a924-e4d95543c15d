import { FieldArray } from 'formik';
import { FormField, FieldDatePicker } from '@/components';
import { Button } from '@digheontech/digh.ui';
import { PhysicianSearch } from '../searchs';
import { Key } from 'react';

export function UB04Items29_41({ isLoading }: { isLoading: boolean }) {
  return (
    <section className="border border-gray-300 p-4">
      <h3 className="text-lg font-bold mb-4">
        Procedure Codes and Physician Information (Fields 29-41)
      </h3>

      {/* Campos 29-35: Procedure Codes */}
      <div className="mb-6">
        <h4 className="font-semibold mb-2">29-35. Procedure Codes</h4>
        <FieldArray name="procedureCodes">
          {({ push, remove, form }) => (
            <div>
              <div className="grid grid-cols-3 gap-4 mb-2">
                <span className="font-medium">Procedure Code</span>
                <span className="font-medium">Date</span>
                <span className="font-medium">Actions</span>
              </div>
              {form.values.procedureCodes?.map((_: any, index: Key | null | undefined) => (
                <div key={index} className="grid grid-cols-3 gap-4 mb-2">
                  <FormField name={`procedureCodes.${index}.code`} disabled={isLoading} />
                  <FieldDatePicker name={`procedureCodes.${index}.date`} disabled={isLoading} />
                  <Button onPress={() => remove(Number(index))}>Remove</Button>
                </div>
              ))}
              <Button onPress={() => push({ code: '', date: '' })}>Add Procedure Code</Button>
            </div>
          )}
        </FieldArray>
      </div>

      {/* Campos 36-41: Physician Information */}
      <div className="grid grid-cols-2 gap-6">
        <div>
          <h4 className="font-semibold mb-2">36-38. Attending Physician</h4>
          <div className="space-y-2">
            <PhysicianSearch name="attendingPhysician.name" label="Name" disabled={isLoading} />
            <FormField name="attendingPhysician.npi" label="NPI" disabled={isLoading} />
            <FormField name="attendingPhysician.upin" label="UPIN" disabled={isLoading} />
          </div>
        </div>

        <div>
          <h4 className="font-semibold mb-2">39-41. Operating Physician</h4>
          <div className="space-y-2">
            <PhysicianSearch name="operatingPhysician.name" label="Name" disabled={isLoading} />
            <FormField name="operatingPhysician.npi" label="NPI" disabled={isLoading} />
            <FormField name="operatingPhysician.upin" label="UPIN" disabled={isLoading} />
          </div>
        </div>
      </div>
    </section>
  );
}
