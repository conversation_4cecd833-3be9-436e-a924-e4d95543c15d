import { useQuery } from '@tanstack/react-query';
import { Http } from '@/config/http';
import { ICPT } from '@/interfaces';

const EP = 'https://test.ehr.digheontech.com/api/diagnoses/current-procedural-codes';

export function useGetCPT(term = '', language: 'es' | 'en' = 'es') {
  const { data: CPT, ...rest } = useQuery<ICPT[]>({
    queryKey: ['cpt', term, language],
    queryFn: () =>
      Http.get(`${EP}?language=${language}&term=${encodeURIComponent(term)}`).then(
        ({ data }) => data,
      ),
    enabled: !!term,
  });

  return { CPT, ...rest };
}
