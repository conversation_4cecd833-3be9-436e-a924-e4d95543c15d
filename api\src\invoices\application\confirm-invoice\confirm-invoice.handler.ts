import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '@nestjs/cqrs';
import { BadRequestException } from '@nestjs/common';
import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';
import { ConfirmInvoiceDto } from 'src/invoices/infrastructure/dto/confirm-invoice.dto';
import { InvoiceStatus } from 'src/shared/enums/invoice-status.enum';
import { DomainError } from 'src/shared/domain';
import { ConfirmInvoiceCommand } from './confirm-invoice.command';

@CommandHandler(ConfirmInvoiceCommand)
export class ConfirmInvoiceHandler
  implements ICommandHandler<ConfirmInvoiceCommand>
{
  constructor(private readonly invoiceRepository: InvoiceRepository) {}
  async execute(command: ConfirmInvoiceCommand) {
    const { invoiceId, confirmData } = command;

    const invoice = await this.invoiceRepository.findById(invoiceId, true);

    if (invoice.status === InvoiceStatus.Admitted) {
      throw new DomainError('INVOICE_ADMITTED', 'Invoice is already admitted');
    }

    this.validateInvoiceFields(confirmData);

    return this.invoiceRepository.update(invoiceId, {
      status: InvoiceStatus.Admitted,
      ...confirmData,
    });
  }

  private validateInvoiceFields(invoiceData: ConfirmInvoiceDto) {
    const missingFields = this.collectMissingFields(invoiceData);

    if (missingFields.length > 0) {
      throw new BadRequestException(missingFields, 'Missing fields');
    }
  }

  private collectMissingFields(invoiceData: ConfirmInvoiceDto): string[] {
    const requiredFields = {
      insuranceIdNumber: invoiceData.insuranceIdNumber,
      federalTaxIdNumber: invoiceData.federalTaxIdNumber,
      federalTaxIdNumberType: invoiceData.federalTaxIdNumberType,
      patientAccountNumber: invoiceData.patientAccountNumber,
      physicianSignature: invoiceData.physicianSignature,
      physicianSignatureDate: invoiceData.physicianSignatureDate,
      priorAuthorizationNumber: invoiceData.priorAuthorizationNumber,
      icd10DiagnosisCodesForDiseasesOrInjuries:
        invoiceData.icd10DiagnosisCodesForDiseasesOrInjuries,

      conditionPatientRelatedToEmployment: this.isDefined(
        invoiceData.conditionPatientRelatedToEmployment,
      ),
      conditionPatientRelatedToAutoAccident: this.isDefined(
        invoiceData.conditionPatientRelatedToAutoAccident,
      ),
      conditionPatientRelatedToOtherAccident: this.isDefined(
        invoiceData.conditionPatientRelatedToOtherAccident,
      ),

      'patient.fullName': invoiceData.patient?.fullName,
      'patient.birthDate': invoiceData.patient?.birthDate,
      'patient.address.city': invoiceData.patient?.address?.city,
      'patient.address.zipCode': invoiceData.patient?.address?.zipCode,
      'patient.phoneNumber': invoiceData.patient?.phoneNumber,

      'insured.fullName': invoiceData.insured?.fullName,
      'insured.birthDate': invoiceData.insured?.birthDate,
      'insured.gender': invoiceData.insured?.gender,
      'insured.planOrProgramName': invoiceData.insured?.planOrProgramName,

      'serviceFacilityLocation.name': invoiceData.serviceFacilityLocation?.name,
      'serviceFacilityLocation.address':
        invoiceData.serviceFacilityLocation?.address,
      'serviceFacilityLocation.city': invoiceData.serviceFacilityLocation?.city,
      'serviceFacilityLocation.state':
        invoiceData.serviceFacilityLocation?.state,
      'serviceFacilityLocation.zipCode':
        invoiceData.serviceFacilityLocation?.zipCode,
      'serviceFacilityLocation.npi': invoiceData.serviceFacilityLocation?.npi,

      'billingProvider.name': invoiceData.billingProvider?.name,
      'billingProvider.phoneNumber': invoiceData.billingProvider?.phoneNumber,
      'billingProvider.address': invoiceData.billingProvider?.address,
      'billingProvider.city': invoiceData.billingProvider?.city,
      'billingProvider.state': invoiceData.billingProvider?.state,
      'billingProvider.zipCode': invoiceData.billingProvider?.zipCode,
      'billingProvider.npi': invoiceData.billingProvider?.npi,
    };

    const missingFields = Object.entries(requiredFields)
      .filter(([_, value]) => !value)
      .map(([key]) => key);

    if (!invoiceData.supplementalInformation) {
      missingFields.push('supplementalInformation');
    } else {
      invoiceData.supplementalInformation.forEach((info, index) => {
        if (!info.fromDateOfService) {
          missingFields.push(
            `supplementalInformation[${index}].fromDateOfService`,
          );
        }
        if (!info.toDateOfService) {
          missingFields.push(
            `supplementalInformation[${index}].toDateOfService`,
          );
        }
        if (!info.placeOfService) {
          missingFields.push(
            `supplementalInformation[${index}].placeOfService`,
          );
        }
        if (!info.emergencyIndicator) {
          missingFields.push(
            `supplementalInformation[${index}].emergencyIndicator`,
          );
        }
        if (!info.proceduresCode) {
          missingFields.push(
            `supplementalInformation[${index}].proceduresCode`,
          );
        }
        if (
          !info.proceduresModifier ||
          info.proceduresModifier.length === 0 ||
          !info.proceduresModifier.some(
            modifier => modifier && modifier.trim() !== '',
          )
        ) {
          missingFields.push(
            `supplementalInformation[${index}].proceduresModifier`,
          );
        }
        if (!info.diagnosisPointer) {
          missingFields.push(
            `supplementalInformation[${index}].diagnosisPointer`,
          );
        }
        if (!info.charges) {
          missingFields.push(`supplementalInformation[${index}].charges`);
        }
        if (!info.daysOrUnits) {
          missingFields.push(`supplementalInformation[${index}].daysOrUnits`);
        }
        if (!info.epsdtFamilyPlan) {
          missingFields.push(
            `supplementalInformation[${index}].epsdtFamilyPlan`,
          );
        }
        if (!info.idQualifier) {
          missingFields.push(`supplementalInformation[${index}].idQualifier`);
        }
        if (!info.renderingProviderId) {
          missingFields.push(
            `supplementalInformation[${index}].renderingProviderId`,
          );
        }
      });
    }

    return missingFields;
  }

  private isDefined(value: unknown): boolean {
    return value !== undefined && value !== null;
  }
}
