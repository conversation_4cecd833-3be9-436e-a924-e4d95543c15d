'use client';

import { Field } from 'formik';
import { Signature } from './signature';

interface IItemSignature {
  disabled: boolean;
  name: string;
  width?: string | number | undefined;
  height?: string | number | undefined;
  className?: HTMLElement['className'];
}

export function FieldSignature({ disabled, name, width, height, className }: IItemSignature) {
  const Component = ({ field }: { field: any; form: any }, ...props: any) => {
    return (
      <Signature
        {...field}
        {...props}
        name={field.name}
        disabled={disabled}
        width={width}
        height={height}
        className={className}
      />
    );
  };

  return <Field name={name} disabled={disabled} component={Component} />;
}
