'use client';

import { YesOrNot } from '@/enums';
import { IFormInvoiceTypeAPayload } from '@/interfaces';
import { Empty, LoadingFallback } from '@/components';
import { toYesOrNot } from '@/modules/invoices/create/pages/invoices-create.page';

import { FormInvoiceTypeAReadOnly } from '../components/form-invoice-type-a-readonly/form-invoice-type-a-readonly';
import { initialValues } from '../components/form-invoice-type-a-readonly/utils/validations-form';
import { AdjustmentsSidebar } from '../components/adjustments-sidebar';
import { useGetInvoice } from '../hooks';

const { No } = YesOrNot;
const INSURED_NAME = 'Physician Correctional (660725126)';

export function InvoicesDetailsPage() {
  const { invoices, isLoading } = useGetInvoice();

  const initialFormValues: IFormInvoiceTypeAPayload = invoices
    ? {
        insuranceCarrier: invoices.insuranceCarrier || '',
        insuranceIdNumber: invoices.insuranceIdNumber || '',
        insured: invoices.insured
          ? {
              ...invoices.insured,
              anotherHealthBenefitPlan: toYesOrNot(invoices.insured.anotherHealthBenefitPlan),
            }
          : {
              anotherHealthBenefitPlan: No,
              planOrProgramName: INSURED_NAME,
            },
        patient: invoices.patient || {},
        patientRelationshipToInsured: invoices.patientRelationshipToInsured || '',
        otherInsuredName: invoices.otherInsuredName || '',
        otherInsuredPolicyOrGroupNumber: invoices.otherInsuredPolicyOrGroupNumber || '',
        otherInsuredPlanNameOrProgramName: invoices.otherInsuredPlanNameOrProgramName || '',
        conditionPatientRelatedToEmployment: toYesOrNot(
          invoices.conditionPatientRelatedToEmployment,
        ),
        conditionPatientRelatedToAutoAccident: toYesOrNot(
          invoices.conditionPatientRelatedToAutoAccident,
        ),
        conditionPatientRelatedToOtherAccident: toYesOrNot(
          invoices.conditionPatientRelatedToOtherAccident,
        ),
        conditionPatientRelatedToAutoAccidentPlace:
          invoices.conditionPatientRelatedToAutoAccidentPlace || '',
        patientOrAuthorizedSignature: invoices.patientOrAuthorizedSignature || '',
        physicianSignature: invoices.physicianSignature || undefined,
        insuredOrAuthorizedSignature: invoices.insuredOrAuthorizedSignature || '',
        dateOfCurrentIllnessInjuryPregnancy: invoices.dateOfCurrentIllnessInjuryPregnancy || '',
        qualifierOfCurrentIllnessInjuryAccident:
          invoices.qualifierOfCurrentIllnessInjuryAccident || '',
        otherDateConditionOfIllnessOrTreatment:
          invoices.otherDateConditionOfIllnessOrTreatment || '',
        qualifierOfOtherConditionOfIllnessOrTreatment:
          invoices.qualifierOfOtherConditionOfIllnessOrTreatment || '',
        unableToWorkFromDate: invoices.unableToWorkFromDate || undefined,
        unableToWorkToDate: invoices.unableToWorkToDate || undefined,
        referringProviderName: invoices.referringProviderName || '',
        referringProviderQualifier: invoices.referringProviderQualifier || '',
        referringProviderOtherId: invoices.referringProviderOtherId || '',
        referringProviderOtherIdQualifier: invoices.referringProviderOtherIdQualifier || '',
        referringProviderNpi: invoices.referringProviderNpi || '',
        hospitalizationFromDate: invoices.hospitalizationFromDate || undefined,
        hospitalizationToDate: invoices.hospitalizationToDate || undefined,
        physicianSignatureDate: invoices.physicianSignatureDate || undefined,
        patientOrAuthorizedSignatureDate: invoices.patientOrAuthorizedSignatureDate || undefined,
        isOutsideLab: toYesOrNot(invoices.isOutsideLab),
        outsideLabCharges: invoices.outsideLabCharges || undefined,
        resubmissionCode: invoices.resubmissionCode || undefined,
        icd10DiagnosisCodesForDiseasesOrInjuries:
          invoices.icd10DiagnosisCodesForDiseasesOrInjuries || [],
        originalReferenceNumber: invoices.originalReferenceNumber || '',
        priorAuthorizationNumber: invoices.priorAuthorizationNumber || '',
        supplementalInformation: invoices.supplementalInformation || [],
        serviceFacilityLocation: invoices.serviceFacilityLocation || '',
        billingProvider: invoices.billingProvider || '',
        totalCharge: invoices.totalCharge || undefined,
        amountPaid: invoices.amountPaid || undefined,
        federalTaxIdNumber: invoices.federalTaxIdNumber || '',
        federalTaxIdNumberType: invoices.federalTaxIdNumberType || undefined,
        patientAccountNumber: invoices.patientAccountNumber || '',
        acceptAssignment: toYesOrNot(invoices.acceptAssignment),
        invoiceAdjustments: invoices.invoiceAdjustments || null,
      }
    : initialValues;

  if (isLoading) {
    return (
      <section className="grid place-items-center h-[calc(100vh-10rem)]">
        <LoadingFallback />
      </section>
    );
  }

  if (!invoices)
    return (
      <section className="grid place-items-center h-[calc(100vh-10rem)]">
        <Empty text="No hay datos" />
      </section>
    );

  return (
    <main className="flex gap-4 w-full">
      <FormInvoiceTypeAReadOnly invoice={initialFormValues} isLoading={isLoading} />
      <div className="flex flex-col gap-4 w-1/4 min-w-[300px]">
        <AdjustmentsSidebar
          adjustments={initialFormValues.invoiceAdjustments}
          originalInvoiceId={invoices?._id}
        />
      </div>
    </main>
  );
}
