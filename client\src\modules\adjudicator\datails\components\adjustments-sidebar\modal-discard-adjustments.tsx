import React from 'react';
import { But<PERSON> } from '@digheontech/digh.ui';
import { Dialog, DialogTrigger, Modal, ModalOverlay } from 'react-aria-components';
import { useRemoveAdjustment } from '../../hooks';

interface ModalDiscardAdjustmentsProps {
  originalInvoiceId: string;
  disabled?: boolean;
}

export function ModalDiscardAdjustments({
  originalInvoiceId,
  disabled,
}: ModalDiscardAdjustmentsProps) {
  const { removeAdjustment, isRemoving } = useRemoveAdjustment();

  const handleDiscard = (close: () => void) => {
    removeAdjustment({ originalInvoiceId });
    close();
  };

  return (
    <DialogTrigger>
      <Button buttonType="dangerOutlined" disabled={disabled || isRemoving} className="w-full mt-4">
        {isRemoving ? 'Descartando...' : 'Descartar Todos los Cambios'}
      </Button>
      <ModalOverlay
        className={({
          isEntering,
          isExiting,
        }) => `fixed inset-0 z-10 overflow-y-auto bg-black/25 flex min-h-full items-center justify-center p-4 text-center backdrop-blur
          ${isEntering ? 'animate-in fade-in duration-300 ease-out' : ''}
          ${isExiting ? 'animate-out fade-out duration-200 ease-in' : ''}`}
      >
        <Modal
          className={({
            isEntering,
            isExiting,
          }) => `w-full max-w-md overflow-hidden rounded-2xl bg-white pt-7 pb-6 px-6 text-left align-middle shadow-xl relative
                ${isEntering ? 'animate-in zoom-in-95 ease-out duration-300' : ''}
                ${isExiting ? 'animate-out zoom-out-95 ease-in duration-200' : ''}`}
        >
          <Dialog role="dialog" className="outline-none">
            {({ close }) => (
              <>
                <div className="text-center">
                  <div className="text-6xl mb-4">⚠️</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Confirmar Descarte de Cambios
                  </h3>
                  <p className="text-sm text-gray-600 mb-6">
                    ¿Estás seguro de que deseas descartar todos los ajustes realizados? Esta acción
                    no se puede deshacer.
                  </p>
                </div>

                <div className="flex justify-center gap-3">
                  <Button
                    disabled={isRemoving}
                    type="button"
                    className="px-4 py-2"
                    buttonType="normal"
                    onPress={() => close()}
                  >
                    Cancelar
                  </Button>
                  <Button
                    disabled={isRemoving}
                    type="button"
                    className="px-4 py-2"
                    buttonType="dangerOutlined"
                    onPress={() => handleDiscard(close)}
                  >
                    {isRemoving ? 'Descartando...' : 'Sí, Descartar'}
                  </Button>
                </div>
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
}
