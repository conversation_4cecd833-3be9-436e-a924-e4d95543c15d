import Link from 'next/link';
import { ElementType } from 'react';

interface ICard {
  href: string;
  title: string;
  icon: ElementType;
}

export function ItemsCard({ href, title, icon: Icon }: ICard) {
  return (
    <Link href={href}>
      <div className="bg-slate-50 p-8 rounded-xl shadow-lg hover:shadow-md transition-shadow duration-300 w-80 h-80 flex flex-col items-center justify-center gap-12 cursor-pointer hover:bg-[#b1f7ff]">
        <div className="p-3 rounded-full bg-white">
          <Icon className="text-primary-500 text-6xl bg-white p-1" />
        </div>
        <span className="text-gray-600 text-4xl font-medium">{title}</span>
      </div>
    </Link>
  );
}
