import { useMutation } from '@tanstack/react-query';
import { AxiosResponseError } from '@/config/http';
import { useShowError, useShowSuccess } from '@/hooks';
import { Http } from '@/config/http';
import { useGetInvoice } from './use-get-invoice';

export const useRemoveServiceAdjustment = () => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateAdjustments } = useGetInvoice();

  const {
    mutate: removeServiceAdjustment,
    isPending: isRemoving,
    ...rest
  } = useMutation({
    mutationKey: ['remove_service_adjustment'],
    mutationFn: ({ invoiceId, serviceId }: { invoiceId: string; serviceId: string }) =>
      Http.delete(`/invoices/${invoiceId}/service-adjustment/${serviceId}`),
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'El ajuste individual ha sido descartado.' });
      invalidateAdjustments();
    },
  });

  return { removeServiceAdjustment, isRemoving, ...rest };
};
