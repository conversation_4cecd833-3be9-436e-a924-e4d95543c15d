import { useFormikContext } from 'formik';
import { FormField } from '@/components';
import { IFormInvoiceUB04Payload } from '@/interfaces';
import { useEffect } from 'react';

export function UB04Totals({ isLoading }: { isLoading: boolean }) {
  const { values, setFieldValue } = useFormikContext<IFormInvoiceUB04Payload>();

  // Calcular totales automáticamente
  useEffect(() => {
    const totalCharges =
      values.serviceLines?.reduce((sum, line) => sum + (line.totalCharges || 0), 0) || 0;
    const nonCoveredCharges =
      values.serviceLines?.reduce((sum, line) => sum + (line.nonCoveredCharges || 0), 0) || 0;
    const totalPriorPayments =
      values.payerInformation?.reduce((sum, payer) => sum + (payer.priorPayments || 0), 0) || 0;
    const balanceDue = totalCharges - totalPriorPayments;

    // Verificar que los valores han cambiado antes de actualizar
    if (values.totalCharges !== totalCharges) {
      setFieldValue('totalCharges', totalCharges);
    }
    if (values.nonCoveredCharges !== nonCoveredCharges) {
      setFieldValue('nonCoveredCharges', nonCoveredCharges);
    }
    if (values.totalPriorPayments !== totalPriorPayments) {
      setFieldValue('totalPriorPayments', totalPriorPayments);
    }
    if (values.balanceDue !== balanceDue) {
      setFieldValue('balanceDue', balanceDue);
    }
  }, [values.serviceLines, values.payerInformation, setFieldValue]);

  return (
    <section className="border border-gray-300 p-4 bg-gray-50">
      <h3 className="text-lg font-bold mb-4">Totals and Summary</h3>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <h4 className="font-semibold mb-3">Charge Summary</h4>
          <div className="space-y-3">
            <FormField
              name="totalCharges"
              label="Total Charges"
              type="number"
              step={0.01}
              disabled={isLoading}
              className="bg-gray-100"
            />
            <FormField
              name="nonCoveredCharges"
              label="Non-Covered Charges"
              type="number"
              step={0.01}
              disabled={isLoading}
              className="bg-gray-100"
            />
            <FormField
              name="totalPriorPayments"
              label="Total Prior Payments"
              type="number"
              step={0.01}
              disabled={isLoading}
              className="bg-gray-100"
            />
            <FormField
              name="balanceDue"
              label="Balance Due"
              type="number"
              step={0.01}
              disabled={isLoading}
              className="bg-gray-100 font-bold"
            />
          </div>
        </div>

        <div>
          <h4 className="font-semibold mb-3">Service Line Summary</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Total Service Lines:</span>
              <span className="font-medium">{values.serviceLines?.length || 0}</span>
            </div>
            <div className="flex justify-between">
              <span>Total Units:</span>
              <span className="font-medium">
                {values.serviceLines?.reduce((sum, line) => sum + (line.serviceUnits || 0), 0) || 0}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Average Charge per Unit:</span>
              <span className="font-medium">
                $
                {(
                  (values.totalCharges || 0) /
                  (values.serviceLines?.reduce((sum, line) => sum + (line.serviceUnits || 0), 0) ||
                    1)
                ).toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
