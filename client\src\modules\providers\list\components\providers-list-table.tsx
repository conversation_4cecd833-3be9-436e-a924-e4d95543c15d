'use client';

import { Table } from '@digheontech/digh.ui';
import { ReactElement, ReactNode, useMemo } from 'react';

import { Empty, Status } from '@/components';
import { ProviderStatus } from '@/enums';
import { IProvidersTable } from '@/interfaces';
import { getProviderLabel, getProviderStatusLabel } from '@/tools';
import { ProvidersModal } from '../../create/components/provider-modal';
import { useGetProviders } from '../hooks/use-get-providers';

interface TableColumn {
  name: string;
  id: keyof IProvidersTable | 'providerName';
  isRowHeader?: boolean;
  className?: string;
}

interface TableRow {
  key: string;
  id: string;
  name: ReactNode;
  npi: ReactNode;
  type: ReactNode;
  status: ReactNode;
}

const rowClassName = 'text-sm font-light';
const columnClassName = 'text-sm font-bold';

const STATUS_COLORS: Record<ProviderStatus, string> = {
  [ProviderStatus.Active]: 'bg-blue-400',
  [ProviderStatus.Inactive]: 'bg-red-400',
  [ProviderStatus.Unlinked]: 'bg-slate-400',
};

const colorStatus = (status: ProviderStatus): ReactElement => (
  <Status className={STATUS_COLORS[status]} />
);

export const ProvidersListTable = () => {
  const { providers, isLoading } = useGetProviders();

  const columns: TableColumn[] = useMemo(
    () => [
      {
        isRowHeader: true,
        name: 'Nombre',
        id: 'name',
        className: columnClassName,
      },
      { name: 'NPI', id: 'npi', className: columnClassName },
      { name: 'Tipo', id: 'type', className: columnClassName },
      { name: 'Estado', id: 'status', className: columnClassName },
    ],
    [],
  );

  const rows: TableRow[] = useMemo(
    () =>
      providers?.map(({ name, npi, type, status }) => ({
        key: name,
        id: name,
        name: <span className={rowClassName}>{name}</span>,
        npi: <span className={rowClassName}>{npi}</span>,
        type: <span className={rowClassName}>{getProviderLabel(type)}</span>,
        status: (
          <div className="flex items-center justify-start gap-2">
            {colorStatus(status)}
            {getProviderStatusLabel(status)}
          </div>
        ),
      })) || [],
    [providers],
  );

  return (
    <main>
      <header className="flex justify-between items-center py-4">
        <h1 className="text-xl font-semibold text-primary-600">Proveedores</h1>
        <ProvidersModal />
      </header>
      <div className="mt-6 bg-white p-5 rounded-md">
        <div className="flex justify-around flex-col gap-4">
          <Table<TableRow>
            isLoading={isLoading}
            columns={columns}
            rows={rows}
            tableBodyProps={{
              renderEmptyState: () => <Empty />,
            }}
            wrapClasses="max-h-full"
          />
        </div>
      </div>
    </main>
  );
};
