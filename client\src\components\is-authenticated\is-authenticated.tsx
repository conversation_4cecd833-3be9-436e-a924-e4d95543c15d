'use client';

import { useIsAuthenticated, useLocation } from '@/hooks';
import { useRouter } from 'next/navigation';

export function IsAuthenticated({ children }: { children: React.ReactNode }) {
  const { fullUrl } = useLocation();
  const { isAuthenticated } = useIsAuthenticated();
  const router = useRouter();

  if (!isAuthenticated) {
    localStorage.setItem('lastLocation', fullUrl);
    router.push('/');
    return;
  }

  return children;
}
