export interface ICityZipCodes {
  city: string;
  zipCodes: string[];
}

export const CITY_ZIP_CODES: ICityZipCodes[] = [
  {
    city: 'San Juan',
    zipCodes: [
      '00901',
      '00907',
      '00909',
      '00911',
      '00912',
      '00913',
      '00915',
      '00917',
      '00918',
      '00919',
      '00920',
      '00921',
      '00923',
      '00924',
      '00925',
      '00926',
      '00927',
      '00928',
      '00929',
      '00930',
      '00931',
      '00933',
      '00934',
      '00935',
      '00936',
      '00937',
      '00940',
      '00955',
    ],
  },
  { city: 'Adjuntas', zipCodes: ['00601'] },
  { city: 'Aguada', zipCodes: ['00602'] },
  { city: 'Aguadilla', zipCodes: ['00603'] },
  { city: 'Maricao', zipCodes: ['00606'] },
  { city: 'Añasco', zipCodes: ['00610'] },
  { city: 'Arecibo', zipCodes: ['00612'] },
  { city: 'Cabo Rojo', zipCodes: ['00616', '00622', '00623'] },
  { city: 'Barceloneta', zipCodes: ['00617'] },
  { city: 'Peñuelas', zipCodes: ['00624'] },
  { city: 'Camuy', zipCodes: ['00627'] },
  { city: 'Lares', zipCodes: ['00631'] },
  { city: 'Sabana Grande', zipCodes: ['00637'] },
  { city: 'Utuado', zipCodes: ['00641'] },
  { city: 'Dorado', zipCodes: ['00646'] },
  { city: 'Guánica', zipCodes: ['00647'] },
  { city: 'Florida', zipCodes: ['00650'] },
  { city: 'Guayanilla', zipCodes: ['00656'] },
  { city: 'Hatillo', zipCodes: ['00659'] },
  { city: 'Isabela', zipCodes: ['00662'] },
  { city: 'Jayuya', zipCodes: ['00664'] },
  { city: 'Lajas', zipCodes: ['00667'] },
  { city: 'Las Marias', zipCodes: ['00669', '00670'] },
  { city: 'Moca', zipCodes: ['00676'] },
  { city: 'Rincón', zipCodes: ['00677'] },
  { city: 'Quebradillas', zipCodes: ['00678'] },
  { city: 'Mayagüez', zipCodes: ['00680', '00682'] },
  { city: 'San Germán', zipCodes: ['00683'] },
  { city: 'San Sebastián', zipCodes: ['00685'] },
  { city: 'Morovis', zipCodes: ['00687'] },
  { city: 'Vega Alta', zipCodes: ['00690', '00692'] },
  { city: 'Vega Baja', zipCodes: ['00693'] },
  { city: 'Yauco', zipCodes: ['00698'] },
  { city: 'Aguas Buenas', zipCodes: ['00703'] },
  { city: 'Salinas', zipCodes: ['00704'] },
  { city: 'Aibonito', zipCodes: ['00705'] },
  { city: 'Maunabo', zipCodes: ['00707'] },
  { city: 'Arroyo', zipCodes: ['00714'] },
  {
    city: 'Ponce',
    zipCodes: ['00716', '00717', '00728', '00730', '00731', '00732', '00733', '00734'],
  },
  { city: 'Naguabo', zipCodes: ['00718'] },
  { city: 'Naranjito', zipCodes: ['00719'] },
  { city: 'Orocovis', zipCodes: ['00720'] },
  { city: 'Patillas', zipCodes: ['00723'] },
  { city: 'Caguas', zipCodes: ['00725', '00727'] },
  { city: 'Cayey', zipCodes: ['00736'] },
  { city: 'Fajardo', zipCodes: ['00738'] },
  { city: 'Cidra', zipCodes: ['00739'] },
  { city: 'Río Grande', zipCodes: ['00745'] },
  { city: 'San Lorenzo', zipCodes: ['00754'] },
  { city: 'Santa Isabel', zipCodes: ['00757'] },
  { city: 'Vieques', zipCodes: ['00765'] },
  { city: 'Villalba', zipCodes: ['00766'] },
  { city: 'Yabucoa', zipCodes: ['00767'] },
  { city: 'Coamo', zipCodes: ['00769'] },
  { city: 'Las Piedras', zipCodes: ['00771'] },
  { city: 'Loíza', zipCodes: ['00772'] },
  { city: 'Luquillo', zipCodes: ['00773'] },
  { city: 'Culebra', zipCodes: ['00775'] },
  { city: 'Juncos', zipCodes: ['00777'] },
  { city: 'Gurabo', zipCodes: ['00778'] },
  { city: 'Guayama', zipCodes: ['00784'] },
  { city: 'Humacao', zipCodes: ['00791'] },
  { city: 'Barranquitas', zipCodes: ['00794'] },
  { city: 'Juana Díaz', zipCodes: ['00795'] },
  { city: 'Toa Baja', zipCodes: ['00949', '00950', '00951'] },
  { city: 'Toa Alta', zipCodes: ['00953'] },
  { city: 'Bayamón', zipCodes: ['00956', '00957', '00959'] },
  { city: 'Guaynabo', zipCodes: ['00965', '00966', '00969'] },
  { city: 'Trujillo Alto', zipCodes: ['00976'] },
  { city: 'Carolina', zipCodes: ['00979', '00982'] },
  { city: 'Cataño', zipCodes: ['00962'] },
  { city: 'Ceiba', zipCodes: ['00735'] },
  { city: 'Comerío', zipCodes: ['00782'] },
  { city: 'Corozal', zipCodes: ['00783'] },
  { city: 'Manatí', zipCodes: ['00674'] },
  { city: 'Canóvanas', zipCodes: ['00729'] },
];
