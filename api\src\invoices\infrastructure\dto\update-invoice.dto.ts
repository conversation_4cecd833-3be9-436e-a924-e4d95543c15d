import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsDefined,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Gender, InsuranceCarrier, Relationship } from 'src/shared/enums';

export class AddressDto {
  @IsString()
  @IsDefined()
  @IsOptional()
  street?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  city?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  state?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  zipCode?: string;
}

export class PatientDto {
  @IsString()
  @IsDefined()
  @IsOptional()
  fullName?: string;

  @IsDate()
  @Type(() => Date)
  @IsDefined()
  @IsOptional()
  birthDate?: Date;

  @IsEnum(Gender)
  @IsOptional()
  gender?: string;

  @ValidateNested()
  @Type(() => AddressDto)
  @IsOptional()
  address?: AddressDto;

  @IsString()
  @IsDefined()
  @IsOptional()
  phoneNumber?: string;
}

export class InsuredDto {
  @IsString()
  @IsDefined()
  @IsOptional()
  fullName?: string;

  @ValidateNested()
  @Type(() => AddressDto)
  @IsOptional()
  address?: AddressDto;

  @IsString()
  @IsDefined()
  @IsOptional()
  phoneNumber?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  policyGroupNumber?: string;

  @IsDate()
  @Type(() => Date)
  @IsDefined()
  @IsOptional()
  birthDate?: Date;

  @IsEnum(Gender)
  @IsDefined()
  @IsOptional()
  gender?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  planOrProgramName?: string;

  @IsBoolean()
  @IsDefined()
  @IsOptional()
  anotherHealthBenefitPlan?: boolean;
}

export class SupplementalInformationDto {
  // item 24 A
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  fromDateOfService?: Date;

  // item 24 A
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  toDateOfService?: Date;

  // item 24 B
  @IsString()
  @IsDefined()
  @IsOptional()
  placeOfService?: string;

  // item 24 C
  @IsString()
  @IsDefined()
  @IsOptional()
  emergencyIndicator?: string;

  // item 24 D
  @IsString()
  @IsDefined()
  @IsOptional()
  proceduresCode?: string;

  // item 24 D
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  proceduresModifier?: string[];

  // item 24 E
  @IsString()
  @IsDefined()
  @IsOptional()
  diagnosisPointer?: string;

  // item 24 F
  @IsNumber()
  @IsOptional()
  charges?: number;

  // item 24 G
  @IsNumber()
  @IsDefined()
  @IsOptional()
  daysOrUnits?: number;

  // item 24 H
  @IsString()
  @IsDefined()
  @IsOptional()
  epsdtFamilyPlan?: string;

  // item 24 I
  @IsString()
  @IsDefined()
  @IsOptional()
  idQualifier?: string;

  // item 24 J
  @IsString()
  @IsDefined()
  @IsOptional()
  renderingProviderId?: string;
}

export class ServiceFacilityLocationDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  city?: string;

  @IsString()
  @IsOptional()
  state?: string;

  @IsString()
  @IsOptional()
  zipCode?: string;

  @IsString()
  @IsOptional()
  npi?: string;

  @IsString()
  @IsOptional()
  otherId?: string;
}

export class BillingProviderDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  city?: string;

  @IsString()
  @IsOptional()
  state?: string;

  @IsString()
  @IsOptional()
  zipCode?: string;

  @IsString()
  @IsOptional()
  npi?: string;

  @IsString()
  @IsOptional()
  otherId?: string;
}

export class UpdateInvoiceDto {
  @IsEnum(InsuranceCarrier)
  @IsOptional()
  insuranceCarrier?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  insuranceIdNumber?: string;

  @IsEnum(Relationship)
  @IsOptional()
  patientRelationshipToInsured?: string;

  @ValidateNested()
  @Type(() => PatientDto)
  @IsOptional()
  patient?: PatientDto;

  @ValidateNested()
  @Type(() => InsuredDto)
  @IsOptional()
  insured?: InsuredDto;

  @IsString()
  @IsDefined()
  @IsOptional()
  otherInsuredName?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  otherInsuredPolicyOrGroupNumber?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  otherInsuredPlanNameOrProgramName?: string;

  @IsBoolean()
  @IsDefined()
  @IsOptional()
  conditionPatientRelatedToEmployment?: boolean;

  @IsBoolean()
  @IsDefined()
  @IsOptional()
  conditionPatientRelatedToAutoAccident?: boolean;

  @IsString()
  @IsDefined()
  @IsOptional()
  conditionPatientRelatedToAutoAccidentPlace?: string;

  @IsBoolean()
  @IsDefined()
  @IsOptional()
  conditionPatientRelatedToOtherAccident?: boolean;

  /*   @IsString()
  @IsDefined()
  @IsOptional()
  patientOrAuthorizedSignature?: string; */

  @IsDate()
  @Type(() => Date)
  @IsDefined()
  @IsOptional()
  patientOrAuthorizedSignatureDate?: Date;

  /*   @IsString()
  @IsDefined()
  @IsOptional()
  insuredOrAuthorizedSignature?: string; */

  // Item 14
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  dateOfCurrentIllnessInjuryPregnancy?: Date;

  @IsString()
  @IsDefined()
  @IsOptional()
  qualifierOfCurrentIllnessInjuryAccident?: string;

  // Item 15
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  otherDateConditionOfIllnessOrTreatment?: Date;

  @IsString()
  @IsDefined()
  @IsOptional()
  qualifierOfOtherConditionOfIllnessOrTreatment?: string;

  // Item 16
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  unableToWorkFromDate?: Date;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  unableToWorkToDate?: Date;

  // Item 17
  @IsString()
  @IsDefined()
  @IsOptional()
  referringProviderName?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  referringProviderQualifier?: string;

  // Item 17a
  @IsString()
  @IsDefined()
  @IsOptional()
  referringProviderOtherId?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  referringProviderOtherIdQualifier?: string;

  // Item 17b
  @IsString()
  @IsDefined()
  @IsOptional()
  referringProviderNpi?: string;

  // Item 18
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  hospitalizationFromDate?: Date;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  hospitalizationToDate?: Date;

  // Item 20
  @IsBoolean()
  @IsDefined()
  @IsOptional()
  isOutsideLab?: boolean;

  @IsNumber()
  @IsDefined()
  @IsOptional()
  outsideLabCharges?: number;

  // item 21
  @IsArray()
  @IsDefined()
  @IsOptional()
  icd10DiagnosisCodesForDiseasesOrInjuries?: string[];

  // item 22
  @IsString()
  @IsDefined()
  @IsOptional()
  resubmissionCode?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  originalReferenceNumber?: string;

  // item 23
  @IsString()
  @IsDefined()
  @IsOptional()
  priorAuthorizationNumber?: string;

  // item 24
  @ValidateNested()
  @IsArray()
  @Type(() => SupplementalInformationDto)
  @IsOptional()
  supplementalInformation?: SupplementalInformationDto[];

  // item 25
  @IsString()
  @IsDefined()
  @IsOptional()
  federalTaxIdNumber?: string;

  @IsString()
  @IsDefined()
  @IsOptional()
  federalTaxIdNumberType?: string;

  // item 26
  @IsString()
  @IsDefined()
  @IsOptional()
  patientAccountNumber?: string;

  // item 27
  @IsBoolean()
  @IsOptional()
  acceptAssignment?: boolean;

  // item 29
  @IsNumber()
  @IsOptional()
  amountPaid?: number;

  // item 31
  @IsString()
  @IsOptional()
  physicianSignature?: string;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  physicianSignatureDate?: Date;

  // item 32
  @ValidateNested()
  @Type(() => ServiceFacilityLocationDto)
  @IsOptional()
  serviceFacilityLocation?: ServiceFacilityLocationDto;

  // item 33
  @ValidateNested()
  @Type(() => BillingProviderDto)
  // @IsOptional()
  billingProvider?: BillingProviderDto;
}
