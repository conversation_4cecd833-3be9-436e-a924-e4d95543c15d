import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Http } from '@/config/http';
import { IProvidersTable } from '@/interfaces';

const key = ['list_providers'];

export const useGetProviders = () => {
  const queryClient = useQueryClient();

  const {
    data: providers,
    isLoading,
    isError,
    error,
  } = useQuery<IProvidersTable[]>({
    queryKey: key,
    queryFn: () => Http.get('providers').then(({ data }): IProvidersTable[] => data),
  });

  const invalidateProviders = () => queryClient.invalidateQueries({ queryKey: key });

  return { providers, invalidateProviders, isLoading, isError, error };
};
