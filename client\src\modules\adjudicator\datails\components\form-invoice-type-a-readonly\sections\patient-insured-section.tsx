import React from 'react';
import { AuditableSection } from '@/modules/adjudicator/datails/components/auditable-section';
import { SectionProps } from '../interfaces';
import { FieldCMS, CMSCheckbox } from '../ui/cms-field';
import { isFieldInAdjustments, getOriginalFieldValue } from '../utils';

export const PatientInsuredSection = ({ invoice, isLoading }: SectionProps) => {
  return (
    <>
      <div className="text-primary-700 text-xl font-bold uppercase my-2 border-b border-sky-200 pb-1">
        Patient and Insured Information
      </div>

      <div className="flex gap-12 mb-2">
        <AuditableSection title="Auditoria" fieldName="insuranceCarrier">
          <FieldCMS
            isVerified={isFieldInAdjustments(invoice, 'insuranceCarrier')}
            label="1. Insurance Type:"
            value={
              <div className="col-span-12 flex flex-wrap gap-2 lowercase mb-2">
                <span className="flex items-center text-xs mr-2">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'insuranceCarrier',
                        invoice.insuranceCarrier,
                      ) === 'MEDICARE'
                    }
                  />{' '}
                  Medicare <span className="ml-1 text-gray-400 text-[10px]">(Medicare #)</span>
                </span>
                <span className="flex items-center text-xs mr-2">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'insuranceCarrier',
                        invoice.insuranceCarrier,
                      ) === 'MEDICAID'
                    }
                  />{' '}
                  Medicaid <span className="ml-1 text-gray-400 text-[10px]">(Medicaid #)</span>
                </span>
                <span className="flex items-center text-xs mr-2">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'insuranceCarrier',
                        invoice.insuranceCarrier,
                      ) === 'TRICARE'
                    }
                  />{' '}
                  Tricare{' '}
                  <span className="ml-1 text-gray-400 text-[10px]">(Sponsor&apos;s SSN)</span>
                </span>
                <span className="flex items-center text-xs mr-2">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'insuranceCarrier',
                        invoice.insuranceCarrier,
                      ) === 'CHAMPVA'
                    }
                  />{' '}
                  Champva <span className="ml-1 text-gray-400 text-[10px]">(Member ID#)</span>
                </span>
                <span className="flex items-center text-xs mr-2">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'insuranceCarrier',
                        invoice.insuranceCarrier,
                      ) === 'GROUP HEALTH PLAN'
                    }
                  />{' '}
                  Group Health Plan
                  <span className="ml-1 text-gray-400 text-[10px]">(SSN or ID)</span>
                </span>
                <span className="flex items-center text-xs mr-2">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'insuranceCarrier',
                        invoice.insuranceCarrier,
                      ) === 'FECA'
                    }
                  />{' '}
                  Feca Black Lung <span className="ml-1 text-gray-400 text-[10px]">(SSN)</span>
                </span>
                <span className="flex items-center text-xs mr-2">
                  <CMSCheckbox
                    checked={
                      getOriginalFieldValue(
                        invoice,
                        'insuranceCarrier',
                        invoice.insuranceCarrier,
                      ) === 'OTHER'
                    }
                  />{' '}
                  Other <span className="ml-1 text-gray-400 text-[10px]">(ID)</span>
                </span>
              </div>
            }
          />
        </AuditableSection>

        <AuditableSection title="Auditoria" fieldName="insuranceIdNumber" isLoading={isLoading}>
          <FieldCMS
            label={
              <div className="flex items-center justify-between gap-2 w-full">
                <span>1a. Insured ID Number</span>
                <small> (For Program in Item 1)</small>
              </div>
            }
            value={getOriginalFieldValue(invoice, 'insuranceIdNumber', invoice.insuranceIdNumber)}
            isVerified={isFieldInAdjustments(invoice, 'insuranceIdNumber')}
          />
        </AuditableSection>
      </div>

      <div className="grid grid-cols-3 gap-2 mb-2">
        <AuditableSection title="Auditoria" fieldName="patient.fullName" isLoading={isLoading}>
          <FieldCMS
            label="2. Patient Name"
            value={getOriginalFieldValue(invoice, 'patient.fullName', invoice.patient?.fullName)}
            isVerified={isFieldInAdjustments(invoice, 'patient.fullName')}
          />
        </AuditableSection>

        <div className="flex gap-2">
          <AuditableSection title="Auditoria" fieldName="patient.birthDate">
            <FieldCMS
              label="3. Patient Birthdate"
              value={
                getOriginalFieldValue(invoice, 'patient.birthDate', invoice.patient?.birthDate)
                  ? new Date(
                      getOriginalFieldValue(
                        invoice,
                        'patient.birthDate',
                        invoice.patient?.birthDate,
                      ),
                    ).toLocaleDateString()
                  : '-'
              }
              isVerified={isFieldInAdjustments(invoice, 'patient.birthDate')}
            />
          </AuditableSection>
          <AuditableSection title="Auditoria" fieldName="patient.gender">
            <FieldCMS
              label="Sex"
              value={
                <span className="flex gap-2">
                  <span>
                    M{' '}
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'patient.gender',
                          invoice.patient?.gender,
                        ) === 'M'
                      }
                    />
                  </span>
                  <span>
                    F{' '}
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'patient.gender',
                          invoice.patient?.gender,
                        ) === 'F'
                      }
                    />
                  </span>
                </span>
              }
              isVerified={isFieldInAdjustments(invoice, 'patient.gender')}
            />
          </AuditableSection>
        </div>

        <AuditableSection title="Auditoria" fieldName="insured.fullName" isLoading={isLoading}>
          <FieldCMS
            label="4. Insured Name"
            value={getOriginalFieldValue(invoice, 'insured.fullName', invoice.insured?.fullName)}
            isVerified={isFieldInAdjustments(invoice, 'insured.fullName')}
          />
        </AuditableSection>
      </div>

      <div className="mb-2">
        <div className="grid grid-cols-3 gap-1">
          <AuditableSection title="Auditoria" fieldName="patient.address" isLoading={isLoading}>
            <FieldCMS
              label="5. Patient Address"
              value={
                <address className="mt-2">
                  <div className="flex gap-2">
                    <span className="text-primary-500 text-sm  mb-2">Street:</span>{' '}
                    <span>
                      {getOriginalFieldValue(
                        invoice,
                        'patient.address.street',
                        invoice.patient?.address?.street,
                      ) || '-'}
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <span className="text-primary-500 text-sm  mb-2">City:</span>{' '}
                    <span>
                      {getOriginalFieldValue(
                        invoice,
                        'patient.address.city',
                        invoice.patient?.address?.city,
                      ) || '-'}
                    </span>
                    <span className="text-primary-500 text-sm  mb-2">State:</span>{' '}
                    <span>
                      {getOriginalFieldValue(
                        invoice,
                        'patient.address.state',
                        invoice.patient?.address?.state,
                      ) || '-'}
                    </span>
                    <span className="text-primary-500 text-sm  mb-2">ZIP:</span>{' '}
                    <span>
                      {getOriginalFieldValue(
                        invoice,
                        'patient.address.zipCode',
                        invoice.patient?.address?.zipCode,
                      ) || '-'}
                    </span>
                  </div>
                </address>
              }
              isVerified={isFieldInAdjustments(invoice, 'patient.address')}
            />
          </AuditableSection>
          <AuditableSection
            title="Auditoria"
            fieldName="patientRelationshipToInsured"
            isLoading={isLoading}
          >
            <FieldCMS
              label="6. Patient's Relationship to Insured"
              value={
                <span className="flex gap-1">
                  <span className="flex items-center text-xs">
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'patientRelationshipToInsured',
                          invoice.patientRelationshipToInsured,
                        ) === 'self'
                      }
                    />{' '}
                    Self
                  </span>
                  <span className="flex items-center text-xs">
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'patientRelationshipToInsured',
                          invoice.patientRelationshipToInsured,
                        ) === 'spouse'
                      }
                    />{' '}
                    Spouse
                  </span>
                  <span className="flex items-center text-xs">
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'patientRelationshipToInsured',
                          invoice.patientRelationshipToInsured,
                        ) === 'child'
                      }
                    />{' '}
                    Child
                  </span>
                  <span className="flex items-center text-xs">
                    <CMSCheckbox
                      checked={
                        getOriginalFieldValue(
                          invoice,
                          'patientRelationshipToInsured',
                          invoice.patientRelationshipToInsured,
                        ) === 'other'
                      }
                    />{' '}
                    Other
                  </span>
                </span>
              }
              isVerified={isFieldInAdjustments(invoice, 'patientRelationshipToInsured')}
            />
          </AuditableSection>
          <div className="grid grid-cols-2 gap-1">
            <AuditableSection title="Auditoria" fieldName="insured.address" isLoading={isLoading}>
              <FieldCMS
                label="7. Insured Address"
                value={
                  <address className="mt-1">
                    <div className="flex gap-2">
                      <span className="text-primary-500 text-sm  mb-2">Street:</span>{' '}
                      <span>
                        {getOriginalFieldValue(
                          invoice,
                          'insured.address.street',
                          invoice.insured?.address?.street,
                        ) || '-'}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <span className="text-primary-500 text-sm  mb-2">City:</span>{' '}
                      <span>
                        {getOriginalFieldValue(
                          invoice,
                          'insured.address.city',
                          invoice.insured?.address?.city,
                        ) || '-'}
                      </span>
                      <span className="text-primary-500 text-sm  mb-2">State:</span>{' '}
                      <span>
                        {getOriginalFieldValue(
                          invoice,
                          'insured.address.state',
                          invoice.insured?.address?.state,
                        ) || '-'}
                      </span>
                      <span className="text-primary-500 text-sm  mb-2">ZIP:</span>{' '}
                      <span>
                        {getOriginalFieldValue(
                          invoice,
                          'insured.address.zipCode',
                          invoice.insured?.address?.zipCode,
                        ) || '-'}
                      </span>
                    </div>
                  </address>
                }
                isVerified={isFieldInAdjustments(invoice, 'insured.address')}
              />
            </AuditableSection>
            <FieldCMS
              label="8. Reserved For NUCC use"
              value={'-'}
              className="h-24"
              isVerified={false}
            />
          </div>
        </div>
      </div>
    </>
  );
};
