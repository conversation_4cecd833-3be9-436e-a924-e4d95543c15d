'use client';

interface IUserLayout {
  children: React.ReactNode;
  header?: React.ReactNode;
  sidebar: React.ReactNode;
  collapsed?: boolean;
}

export function SidebarHeaderTemplate({
  children,
  header,
  sidebar,
  collapsed = false,
}: IUserLayout) {
  return (
    <main
      id="main"
      className={`bg-white grid px-10 min-h-screen grid-rows-[52px_auto] lg:p-0 lg:grid-rows-1 ${
        collapsed ? 'lg:grid-cols-[70px_auto]' : 'lg:grid-cols-[220px_auto]'
      }`}
    >
      <aside
        className={`hidden lg:block bg-[#DDEDF6] text-slate-600 p-4 min-h-full border transition-all duration-200`}
        style={{
          width: collapsed ? 70 : 220,
          minWidth: collapsed ? 70 : 220,
          maxWidth: collapsed ? 70 : 220,
        }}
      >
        {sidebar}
      </aside>
      {header && <header className="py-3 lg:hidden">{header}</header>}
      <div className="hidden lg:grid lg:grid-rows-[64px_auto] px-8">
        {header && <header className="hidden py-4 lg:block">{header}</header>}
        <section className="hidden py-2 lg:block">{children}</section>
      </div>
      <section className="py-2 lg:hidden">{children}</section>
    </main>
  );
}
