import { Item14Qualifier } from '@/enums';
import { StatusItem } from '@/modules/providers/create/components/provider-select-type/status-item';
import { getItem14QualifierLabel } from '@/tools/getItem14QualifierLabel';
import { useField } from 'formik';
import { Key } from 'react';
import { Button, ListBox, Popover, Select, SelectValue } from 'react-aria-components';
import { LuChevronsUpDown } from 'react-icons/lu';

const QUALIFIERS = [Item14Qualifier.LastMenstrualPeriod, Item14Qualifier.Onset] as const;

interface IQualifierOption {
  qualifier: Item14Qualifier;
}

interface IQualifierSelectItem14 {
  className?: HTMLElement['className'];
}

const QualifierOption = ({ qualifier }: IQualifierOption) => (
  <StatusItem key={qualifier} id={qualifier}>
    <div className="flex gap-2 items-baseline">
      <span>{qualifier}</span> <small>{getItem14QualifierLabel(qualifier)}</small>
    </div>
  </StatusItem>
);

export function QualifierSelectItem14({ className }: IQualifierSelectItem14) {
  const [field, meta, helpers] = useField<Item14Qualifier>(
    'qualifierOfCurrentIllnessInjuryAccident',
  );

  const handleSelectionChange = (key: Key) => {
    helpers.setValue(key as Item14Qualifier);
  };

  return (
    <div className={className}>
      <Select
        aria-label="Qualifier"
        className="flex flex-col gap-1 w-full mb-4"
        placeholder="select"
        defaultSelectedKey={undefined}
        selectedKey={field.value}
        onSelectionChange={handleSelectionChange}
      >
        <Button className="field-shadow h-4" aria-label="Qualifier">
          <SelectValue className="flex-1 truncate placeholder-shown:italic">
            {value => getItem14QualifierLabel(value as unknown as Item14Qualifier)}
          </SelectValue>
          <LuChevronsUpDown />
        </Button>
        <Popover className="max-h-60 w-[--trigger-width] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 entering:animate-in entering:fade-in exiting:animate-out exiting:fade-out">
          <ListBox className="outline-none p-1">
            {QUALIFIERS.map(qualifier => (
              <QualifierOption key={qualifier} qualifier={qualifier} />
            ))}
          </ListBox>
        </Popover>
      </Select>
      {meta.touched && meta.error && <div className="text-red-500 text-sm mt-1">{meta.error}</div>}
    </div>
  );
}
