'use client';

import useLoggedUser from '@/hooks/use-logged-user';
import { FormValidateNpi } from '../components/form-validate-npi';

export function ValidateProvider() {
  const { loggedUser } = useLoggedUser();

  const initialValues = {
    providerId: loggedUser?.providerId ?? '',
    userId: loggedUser?.id ?? '',
  };

  return (
    <section className="flex items-center justify-center mt-14">
      <FormValidateNpi initialValues={initialValues} />
    </section>
  );
}
