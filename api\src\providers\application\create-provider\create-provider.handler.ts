import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';

import { HashService } from 'src/shared/hash';
import { ProviderStatus, ProviderType, UserRole } from 'src/shared/enums';
import { UserRepository } from 'src/identity/infrastructure/repositories/user.repository';
import { ProviderRepository } from 'src/providers/infrastructure/repositories/provider.repository';

import { CreateProviderCommand } from './create-provider.command';

type CreateProviderUserData = CreateProviderCommand & {
  providerId: string;
};

@CommandHandler(CreateProviderCommand)
export class CreateProviderHandler
  implements ICommandHandler<CreateProviderCommand>
{
  constructor(
    private readonly providerRepository: ProviderRepository,
    private readonly userRepository: UserRepository,
    private readonly hashService: HashService,
  ) {}
  async execute(command: CreateProviderCommand) {
    const providerData = {
      name: command.providerName,
      npi: command.npi,
      type: command.type as ProviderType,
      status: ProviderStatus.Active,
    };
    const provider = await this.providerRepository.create(providerData);

    return this.createProviderUser({ ...command, providerId: provider.id });
  }

  private async createProviderUser(userData: CreateProviderUserData) {
    const userProvider = {
      profile: {
        firstName: userData.userName,
        lastName: userData.userLastName,
      },
      email: userData.email,
      password: await this.hashService.hash(userData.password),
      role: UserRole.Provider,
      providerId: userData.providerId,
    };

    return this.userRepository.create(userProvider);
  }
}
