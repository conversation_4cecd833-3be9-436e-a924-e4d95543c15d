import { useField } from 'formik';
import { useRef, useEffect } from 'react';
import { Label } from 'react-aria-components';
import SignaturePad from 'signature_pad';

interface ISignature {
  name: string;
  width?: string | number | undefined;
  height?: string | number | undefined;
  className?: HTMLElement['className'];
}

export const Signature = ({ name, width = 400, height = 100, className }: ISignature) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const sigPadRef = useRef<SignaturePad | null>(null);
  const [field, , helpers] = useField(name);

  useEffect(() => {
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d')!;

      const dpr = window.devicePixelRatio || 1;

      canvas.width = 400 * dpr;
      canvas.height = 100 * dpr;

      canvas.style.width = `${width}px`;
      canvas.style.height = `${height}px`;

      ctx.scale(dpr, dpr);

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      sigPadRef.current = new SignaturePad(canvas, {
        penColor: 'black',
        backgroundColor: 'white',
      });

      sigPadRef.current.addEventListener('endStroke', () => {
        const signatureData = sigPadRef.current?.toDataURL() || '';
        helpers.setValue(signatureData);
      });
    }
  }, [height, helpers, width]);

  const clearSignature = () => {
    sigPadRef.current?.clear();
    helpers?.setValue('');
  };

  useEffect(() => {
    if (field.value && sigPadRef.current) {
      sigPadRef.current.fromDataURL(field.value);
    }
  }, [field.value]);

  return (
    <div className={className}>
      <Label className="block text-sm mb-1 font-medium">SIGNED</Label>
      <canvas
        ref={canvasRef}
        style={{ width, height, touchAction: 'none' }}
        className="field-shadow !cursor-custom my-2"
      />
      <button onClick={clearSignature}>
        <small className="text-red-400 ml-4 mt-4">Limpiar</small>
      </button>
    </div>
  );
};
