import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { Provider } from '../schemas/provider.schema';

@Injectable()
export class ProviderRepository {
  constructor(
    @InjectModel(Provider.name)
    private providerModel: Model<Provider>,
  ) {}

  async create(data: Partial<Provider>) {
    return this.providerModel.create(data);
  }

  async getAll() {
    return this.providerModel.find().sort({ createdAt: 1 }).exec();
  }

  async findById(id: string) {
    return this.providerModel.findById(id).exec();
  }

  async getAllByIds(ids: string[]) {
    return this.providerModel.find({ _id: { $in: ids } }).exec();
  }
}
