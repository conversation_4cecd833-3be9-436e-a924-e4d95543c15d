import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

import { ProviderStatus, ProviderType } from 'src/shared/enums';

@Schema({ timestamps: true, collection: 'providers' })
export class Provider {
  @Prop({ required: true })
  name: string;

  @Prop({ unique: true, required: true })
  npi: string;

  @Prop({ required: true })
  type: ProviderType;

  @Prop()
  status: ProviderStatus;
}

export type ProviderDocument = Provider & Document;

export const ProviderSchema = SchemaFactory.createForClass(Provider);
