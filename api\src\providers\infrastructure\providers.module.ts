import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { CqrsModule } from '@nestjs/cqrs';
import { JwtModule } from '@nestjs/jwt';

import { IdentityModule } from 'src/identity/infrastructure/identity.module';
import { AuthService } from 'src/identity/infrastructure/auth.service';
import { HashService } from 'src/shared/hash';
import { FieldExistsRule } from 'src/shared/validation/field-exists.decorator';

import { CreateProviderHandler } from '../application/create-provider/create-provider.handler';
import { ListProvidersHandler } from '../application/list-providers/list-providers.handler';
import { ValidateProviderNpiHandler } from '../application/validate-provider-npi/validate-provider-npi.handler';

import { ProvidersController } from './providers.controller';
import { Provider, ProviderSchema } from './schemas/provider.schema';
import { ProviderRepository } from './repositories/provider.repository';

@Module({
  imports: [
    ConfigModule,
    CqrsModule,
    JwtModule,
    MongooseModule.forFeature([
      { name: Provider.name, schema: ProviderSchema },
    ]),
    forwardRef(() => IdentityModule),
  ],
  providers: [
    AuthService,
    CreateProviderHandler,
    ListProvidersHandler,
    ValidateProviderNpiHandler,
    ProviderRepository,
    HashService,
    FieldExistsRule,
  ],
  controllers: [ProvidersController],
  exports: [ProviderRepository],
})
export class ProvidersModule {}
