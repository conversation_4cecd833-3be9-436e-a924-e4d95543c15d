import { FaRegFilePdf, FaRegFileImage } from 'react-icons/fa';
import { useDocumentList } from '../hooks';
import { ModalView } from '../modal-view';
import { Empty } from '@/components';
import { useSearchParams } from 'next/navigation';

export function DocumentList() {
  const searchParams = useSearchParams();
  const invoiceId = searchParams.get('invoiceId');

  const { documents, isLoading, error } = useDocumentList(invoiceId as string);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading documents</div>;

  return (
    <main className="flex flex-col border border-slate-300">
      <header className="sticky top-0 w-full flex items-center justify-between text-lg text-slate-700 bg-primary-100 px-4 py-2 z-10 uppercase">
        <strong>Associated documents</strong>{' '}
        <span className="text-gray-700 font-semibold"># {documents.length}</span>
      </header>
      <section className="w-full flex justify-between border border-slate-100 items-center px-4 py-2">
        <div>
          <b>File Name </b>
        </div>
        <div>
          <b>Document Type</b>
        </div>
        <div className="mr-6">
          <b>Menu</b>
        </div>
      </section>
      {documents?.length === 0 ? (
        <Empty text="No hay documentos asociados" />
      ) : (
        <aside className="flex flex-col overflow-y-auto max-h-72">
          {documents?.map(doc => (
            <section
              key={doc._id}
              className="w-full flex justify-between border border-slate-100 items-center px-4 py-2"
            >
              <div className="w-1/2 items-baseline">{doc.originalName}</div>
              <div className="w-1/2 items-baseline">
                <div className="flex gap-1 items-center">
                  {doc.fileType === 'application/pdf' ? (
                    <FaRegFilePdf className="text-red-500 text-sm" />
                  ) : (
                    <FaRegFileImage className="text-blue-400 text-sm" />
                  )}
                  <div>{doc.documentType}</div>
                </div>
              </div>
              <div className="w-1/2 flex gap-1 items-center justify-end">
                <ModalView doc={doc} />
              </div>
            </section>
          ))}
        </aside>
      )}
    </main>
  );
}
