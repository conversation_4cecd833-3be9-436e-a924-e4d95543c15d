import { useEffect, useState } from 'react';
import { Http } from '@/config/http';
import useLoggedUser from './use-logged-user';
import { IUser } from '@/interfaces';

interface UserData {
  user: IUser;
}

export function useVerifyToken() {
  const [isVerifyingToken, setIsVerifyingToken] = useState(true);
  const { updateLoggedUser, updateToken } = useLoggedUser();

  const getMe = (token: string) =>
    Http.get('/users/me', {
      headers: { Authorization: `Bearer ${token}` },
    }).then(({ data }) => data);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token && isVerifyingToken) {
      getMe(token)
        .then(({ user }: UserData) => {
          if (user) {
            updateLoggedUser(user);
            updateToken(token);
            setIsVerifyingToken(false);
          }
        })
        .catch(() => {
          setIsVerifyingToken(false);
          localStorage.removeItem('token');
          updateToken(null);
        });
    } else {
      setIsVerifyingToken(false);
    }
  }, [isVerifyingToken, updateLoggedUser, updateToken]);

  return { isVerifyingToken, setIsVerifyingToken };
}
