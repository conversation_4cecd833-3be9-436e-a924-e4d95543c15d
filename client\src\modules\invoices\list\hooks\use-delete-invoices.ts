import { useMutation } from '@tanstack/react-query';
import { AxiosResponseError } from '@/config/http';
import { useShowError, useShowSuccess } from '@/hooks';
import { Http } from '@/config/http';
import { useGetInvoices } from './use-get-invoices';

export const useDeleteInvoices = () => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateInvoices } = useGetInvoices();

  const {
    mutate: deleteInvoice,
    isPending: isDeleting,
    ...rest
  } = useMutation({
    mutationKey: ['delete_invoices'],
    mutationFn: ({ invoiceId }: { invoiceId: string }) => Http.delete(`/invoices/${invoiceId}`),
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Factura eliminada.' });
      invalidateInvoices();
    },
  });

  return { deleteDocument: deleteInvoice, isDeleting, ...rest };
};
