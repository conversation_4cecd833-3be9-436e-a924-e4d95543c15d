'use client';

import Phy<PERSON><PERSON>r<PERSON>ogo from '@/components/phy-corr-logo';
import { ValidateProvider } from '@/modules/providers';

export default function ProvidersList() {
  return (
    <main className="bg-slate-100 grid px-10 min-h-screen justify-center lg:bg-guest bg-no-repeat bg-cover">
      <section className="flex justify-center items-center flex-col">
        <PhyCorrLogo />
        <h1 className="text-slate-700 text-2xl mt-12">Validar Proveedor</h1>
        <p>
          Por motivos de seguridad, necesitamos que ingreses tu NPI para poder acceder a la
          plataforma.
        </p>
        <p>Si no tienes un NPI, por favor ponte en contacto con el administrador.</p>
        <ValidateProvider />
      </section>
    </main>
  );
}
