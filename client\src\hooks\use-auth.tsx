'use client';

import { useRouter } from 'next/navigation';
import { IOnLogin } from '@/interfaces';

import useLoggedUser from './use-logged-user';

export function useAuth() {
  const router = useRouter();
  const { updateLoggedUser, updateToken } = useLoggedUser();

  const onLogin = ({ token, user }: IOnLogin) => {
    localStorage.setItem('token', token);
    updateToken(token);
    updateLoggedUser(user);
    router.push('/');
  };

  const onLogout = () => {
    localStorage.removeItem('token');
    updateToken(null);
    updateLoggedUser(null);
    router.push('/sign-in');
  };

  return { onLogin, onLogout };
}
