import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CqrsModule } from '@nestjs/cqrs';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';

import { HashService } from 'src/shared/hash';
import { ClientRouteBuilder } from 'src/shared/utils';
import { JwtStrategy } from 'src/shared/strategies/jwt.strategy';
import { ProvidersModule } from 'src/providers/infrastructure/providers.module';

import { CreateUserHandler } from '../application/create-user/create-user.handler';
import { CreateSuperAdminHandler } from '../application/create-admin/create-super-admin.handler';
import { SignInHandler } from '../application/sign-in/sign-in.handler';
import { GetLoggedUserHandler } from '../application/get-logged-user/get-logged-user.handler';
import { GetProviderUsersHandler } from '../application/get-user-providers/get-provider-users.handler';
import { GetUsersHandler } from '../application/get-users/get-users.handler';

import { User, UserSchema } from './schemas/user.schema';
import { UserRepository } from './repositories/user.repository';

import { AuthService } from './auth.service';
import { UsersController } from './users.controller';
import { CreateSuperAdminCli } from './cli/create-super-admin.cli';

@Module({
  imports: [
    ConfigModule,
    CqrsModule,
    JwtModule,
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    forwardRef(() => ProvidersModule),
  ],
  controllers: [UsersController],
  providers: [
    AuthService,
    ClientRouteBuilder,
    CreateSuperAdminCli,
    CreateSuperAdminHandler,
    CreateUserHandler,
    GetLoggedUserHandler,
    GetProviderUsersHandler,
    GetUsersHandler,
    HashService,
    JwtStrategy,
    SignInHandler,
    UserRepository,
  ],
  exports: [UserRepository],
})
export class IdentityModule {}
