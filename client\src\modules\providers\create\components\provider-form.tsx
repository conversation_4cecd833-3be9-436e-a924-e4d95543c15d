import { Form, Formik, FormikHelpers } from 'formik';
import { Button } from '@digheontech/digh.ui';

import { FormField } from '@/components';
import { IFormProvidersValues } from '@/interfaces';
import { useCreateProvider } from '../hooks/use-create-provider';
import { ProviderSelectType } from './provider-select-type/provider-select-type';
import { fieldsProvider, fieldsUser, initialValues, validationSchema } from './validations-form';

interface IProviderForm {
  onSubmitSuccess: () => void;
}

export function ProviderForm({ onSubmitSuccess }: IProviderForm) {
  const { create, isCreating } = useCreateProvider();

  const onSubmit = (
    payload: IFormProvidersValues,
    { resetForm }: FormikHelpers<IFormProvidersValues>,
  ) => {
    create(payload);
    resetForm();
    onSubmitSuccess();
  };

  return (
    <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={onSubmit}>
      {({ isValid }) => (
        <Form className="w-full bg-transparent flex flex-col gap-4">
          <div className="text-center text-primary-400 uppercase font-light">Proveedor</div>
          {fieldsProvider.map(({ name, label, placeholder }) => (
            <FormField
              key={name}
              name={name}
              label={label}
              disabled={isCreating}
              placeholder={placeholder}
            />
          ))}
          <ProviderSelectType />

          <div className="text-center text-primary-400 uppercase font-light">Usuario</div>
          {fieldsUser.map(({ name, label }) => (
            <FormField key={name} name={name} label={label} disabled={isCreating} />
          ))}
          <Button
            type="submit"
            primary
            className="mt-4"
            fullWidth
            disabled={!isValid || isCreating}
          >
            Aceptar
          </Button>
        </Form>
      )}
    </Formik>
  );
}
