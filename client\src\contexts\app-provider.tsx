import { useState, ReactNode } from 'react';

import AppContext from '@/contexts/app-context';
import { IUser } from '@/interfaces';
import { IAppContext } from '@/interfaces/app-context.interface';

type IProvider = { children: ReactNode };

export const AppProvider = ({ children }: IProvider) => {
  const [loggedUser, setLoggedUser] = useState<IUser | null>(null);
  const [token, setToken] = useState<string | null>(null);

  const updateLoggedUser = (user: IUser | null) => setLoggedUser(user);

  const updateToken = (newToken: string | null) => setToken(newToken);

  const contextValue: IAppContext = { loggedUser, updateLoggedUser, token, updateToken };

  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
};

export default AppProvider;
