import { useMutation } from '@tanstack/react-query';
import { AxiosResponseError } from '@/config/http';
import { useShowError, useShowSuccess } from '@/hooks';
import { Http } from '@/config/http';
import { useDocumentList } from './use-document-list';

export const useDeleteDocument = (invoiceId: string) => {
  const { showError } = useShowError();
  const { showSuccess } = useShowSuccess();
  const { invalidateFiles } = useDocumentList(invoiceId);

  const {
    mutate: deleteDocument,
    isPending: isDeleting,
    ...rest
  } = useMutation({
    mutationKey: ['delete_document'],
    mutationFn: ({ fileId }: { fileId: string }) =>
      Http.delete(`/invoices/${invoiceId}/files/${fileId}`),
    onError: (error: AxiosResponseError) => showError(error),
    onSuccess: () => {
      showSuccess({ title: 'Éxito', description: 'Documento eliminado.' });
      invalidateFiles();
    },
  });

  return { deleteDocument, isDeleting, ...rest };
};
