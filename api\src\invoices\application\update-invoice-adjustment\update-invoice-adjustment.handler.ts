import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>mandHand<PERSON> } from '@nestjs/cqrs';

import { InvoiceAdjustmentRepository } from 'src/invoices/infrastructure/repositories/invoice-adjustments.repository';
import { UpdateInvoiceAdjustmentCommand } from './update-invoice-adjustment.command';
import { InvoiceAdjustment } from 'src/invoices/infrastructure/schema/invoice-adjustments.schema';
import { InvoiceRepository } from 'src/invoices/infrastructure/repositories/invoice.repository';

@CommandHandler(UpdateInvoiceAdjustmentCommand)
export class UpdateInvoiceAdjustmentHandler
  implements ICommandHandler<UpdateInvoiceAdjustmentCommand>
{
  private readonly NESTED_FIELDS = [
    'patient',
    'insured',
    'serviceFacilityLocation',
    'billingProvider',
  ];

  constructor(
    private readonly invoiceAdjustmentRepository: InvoiceAdjustmentRepository,
    private readonly invoiceRepository: InvoiceRepository,
  ) {}
  async execute(command: UpdateInvoiceAdjustmentCommand) {
    const { originalInvoiceId, ...data } = command;

    const existingInvoiceAdjustment =
      await this.invoiceAdjustmentRepository.findByInvoiceId(originalInvoiceId);

    const currentSupplementalInformation =
      this.getCurrentSupplementalInformation(existingInvoiceAdjustment);

    const updatedSupplementalInformation = this.updateSupplementalInformation(
      currentSupplementalInformation,
      data.invoiceData.supplementalInformation,
    );

    const existingInvoice =
      await this.invoiceRepository.findById(originalInvoiceId);

    const totalCharge = this.recalculateTotalCharge(
      existingInvoice?.supplementalInformation,
      updatedSupplementalInformation,
    );

    let mergedInvoiceData = {};

    if (existingInvoiceAdjustment) {
      mergedInvoiceData = this.mergeAllNestedObjects(
        existingInvoiceAdjustment.toObject(),
        data.invoiceData,
      );
    }

    return this.invoiceAdjustmentRepository.update(originalInvoiceId, {
      ...data.invoiceData,
      ...mergedInvoiceData,
      supplementalInformation: updatedSupplementalInformation,
      totalCharge,
    });
  }

  private getCurrentSupplementalInformation(invoice: InvoiceAdjustment) {
    return invoice?.supplementalInformation?.length > 0
      ? invoice.supplementalInformation
      : [];
  }

  private updateSupplementalInformation(
    currentSupplementalInformation,
    supplementalInformationPayload,
  ) {
    if (!supplementalInformationPayload) {
      return currentSupplementalInformation;
    }

    const existingIndex = currentSupplementalInformation.findIndex(
      item => item._id === supplementalInformationPayload._id,
    );

    if (existingIndex >= 0) {
      currentSupplementalInformation[existingIndex] = {
        ...currentSupplementalInformation[existingIndex],
        ...supplementalInformationPayload,
      };
    } else {
      currentSupplementalInformation.push(supplementalInformationPayload);
    }

    return currentSupplementalInformation;
  }

  private mergeAllNestedObjects(existingInvoiceData, invoiceDataPayload) {
    const mergedObjects = {};

    this.NESTED_FIELDS.forEach(field => {
      if (existingInvoiceData[field] && invoiceDataPayload[field]) {
        mergedObjects[field] = {
          ...existingInvoiceData[field],
          ...invoiceDataPayload[field],
        };
      } else {
        mergedObjects[field] =
          existingInvoiceData[field] || invoiceDataPayload[field];
      }
    });

    return mergedObjects;
  }

  private recalculateTotalCharge(
    originalSupplementalInformation,
    updatedSupplementalInformation,
  ) {
    if (updatedSupplementalInformation.length === 0) {
      return undefined;
    }

    const mergedSupplementalInformation = this.getMergedSupplementalInformation(
      originalSupplementalInformation,
      updatedSupplementalInformation,
    );

    let totalCharge = 0;

    for (const info of mergedSupplementalInformation) {
      if (!info.charges) {
        continue;
      }
      totalCharge += +info.charges;
    }
    return totalCharge;
  }

  private getMergedSupplementalInformation(
    originalSupplementalInformation,
    updatedSupplementalInformation,
  ) {
    const updatedSupplementalInformationMap = new Map(
      updatedSupplementalInformation.map(item => [item._id.toString(), item]),
    );

    return originalSupplementalInformation.map(item => {
      const updatedItem: Record<string, any> =
        updatedSupplementalInformationMap.get(item._id.toString());
      if (updatedItem) {
        return {
          ...item.toObject(),
          ...updatedItem,
          _id: item._id,
        };
      }
      return item;
    });
  }
}
